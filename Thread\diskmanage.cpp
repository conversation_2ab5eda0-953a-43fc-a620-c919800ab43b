#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>

#include "vs_comm_def.h"
#include "diskmanage.h"
#include "disk_manager.h"

// QT界面接口函数，直接调用DiskManager静态方法

int is_disk_device(const char *name)
{
    // 已删除的方法，提供简化实现
    if (!name || strlen(name) == 0) {
        return 0;
    }

    // 简单检查：如果是 /dev/sd* 或 /dev/hd* 格式且长度为8（如/dev/sda），认为是磁盘设备
    if (strncmp(name, "/dev/sd", 7) == 0 || strncmp(name, "/dev/hd", 7) == 0) {
        return (strlen(name) == 8) ? 1 : 0;
    }

    return 0;
}

int is_partition(const char *dev)
{
    // 已删除的方法，提供简化实现
    if (!dev || strlen(dev) == 0) {
        return 0;
    }

    // 简单检查：如果是 /dev/sd*1 或 /dev/hd*1 格式且长度为9（如/dev/sda1），认为是分区
    if (strncmp(dev, "/dev/sd", 7) == 0 || strncmp(dev, "/dev/hd", 7) == 0) {
        return (strlen(dev) == 9 && (dev[8] >= '1' && dev[8] <= '9')) ? 1 : 0;
    }

    return 0;
}

void get_first_partition(const char *dev, char *out, size_t out_size)
{
    DiskManager::getFirstPartition(dev, out, out_size);
}

char* get_mount_point_from_device(const char *part_dev)
{
    return DiskManager::getMountPointFromDevice(part_dev);
}

int mount_disk(const char *device)
{
    return DiskManager::mountDisk(device);
}

int vs_delete_format_part(const char *dev_disk)
{
    return DiskManager::deleteFormatPartition(dev_disk);
}

int vs_format_disk(const char *disk)
{
    return DiskManager::formatDisk(disk);
}
