#include "buffer_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/file.h>
#include <time.h>
#include <errno.h>
#include <iostream>
#include <chrono>

#include "channel.h"

// 缓冲区参数计算函数
void calculateBufferParams(UINT8 streamChn, UINT32 *cache_num, UINT32 *mb_size)
{
    if (!cache_num || !mb_size) return;
    
    switch (streamChn) {
    case 0: // 主码流
        *cache_num = 100;   // 100帧
        *mb_size = 1024*1024; // 1MB每帧
        break;
    case 1: // 子码流
        *cache_num = 300;   // 300帧
        *mb_size = 100*1024; // 100KB每帧
        break;
    default:
        *cache_num = 10;   // 默认10帧
        *mb_size = 50*1024; // 默认50KB每帧
        break;
    }
}

BufferManager::BufferManager(int channel_id) : 
    chn(channel_id),
    initialized(false),
    mb_sec(10),  // 默认缓存10秒
    videoFps(25), // 默认25帧每秒
    bitrate(4*1024*1024), // 默认4Mbps
    frame_seq(0),
    reader_handle(INVALID_HANDLE_VALUE)
{
    // 初始化句柄数组
    for (int i = 0; i < VS_MAX_STREAM_NUM; i++) {
        write_handles[i] = INVALID_HANDLE_VALUE;
        video_mb_cache_sec[i] = 10; // 默认10秒缓存
    }
    
    // 初始化读取上下文
    memset(&read_context, 0, sizeof(read_context));
}

BufferManager::~BufferManager()
{
    // 关闭所有写入缓冲区
    closeAllWriteBuffers();
    
    // 关闭读取流
    closeReaderStream();
}

void BufferManager::init()
{
    if (!initialized) {
        // 初始化媒体缓冲区系统 - 直接调用底层的mb_init
        ::mb_init();  // 使用作用域解析运算符调用全局的mb_init
        initialized = true;
    }
}

// 检查指定通道和码流的缓冲区是否已打开
INT32 BufferManager::isBufferOpen(UINT32 streamChn)
{
    if (streamChn >= VS_MAX_STREAM_NUM) {
        return FALSE;
    }
    
    return write_handles[streamChn] != INVALID_HANDLE_VALUE;
}

// 获取指定通道和码流的缓冲区句柄
HANDLE BufferManager::getBufferHandle(UINT32 streamChn)
{
    if (streamChn >= VS_MAX_STREAM_NUM) {
        return INVALID_HANDLE_VALUE;
    }
    
    return write_handles[streamChn];
}

// 修改 BufferManager 类的方法实现，适配多通道缓存
INT32 BufferManager::openWriteBuffer(UINT32 streamChn)
{
    if (streamChn >= VS_MAX_STREAM_NUM) {
        LogE("参数错误: chn=%d, streamChn=%d", chn, streamChn);
        return FAIL;
    }
    
    // 打开缓冲区
    if (write_handles[streamChn] == INVALID_HANDLE_VALUE) {
        UINT32 cache_num, mb_size;
        getMbCacheNum(streamChn, &cache_num, &mb_size);

        // 修改：使用mb_writer_open2并传递通道号
        write_handles[streamChn] = mb_writer_open2((MB_TYPE)streamChn, mb_size, cache_num, chn);
        
        if (write_handles[streamChn] == INVALID_HANDLE_VALUE) {
            LogE("打开媒体缓冲区失败: chn=%d, streamChn=%d, mb_type=%d", chn, streamChn, streamChn);
            return FAIL;
        }
    }

    return OK;
}


void BufferManager::closeWriteBuffer(UINT32 streamChn)
{
    if (streamChn >= VS_MAX_STREAM_NUM) {
        return;
    }
    
    if (write_handles[streamChn] != INVALID_HANDLE_VALUE) {
        mb_writer_close(write_handles[streamChn]);
        write_handles[streamChn] = INVALID_HANDLE_VALUE;
    }
}

void BufferManager::closeAllWriteBuffers()
{
    for (int i = 0; i < VS_MAX_STREAM_NUM; i++) {
        closeWriteBuffer(i);
    }
}


INT32 BufferManager::createFrameHeader(T_FRAME_HEADER *dst_hdr, FRAMEINFO_t* hdr, uint32_t size)
{
    if (!dst_hdr || !hdr) {
        return FAIL;
    }
    
    memset(dst_hdr, 0, sizeof(T_FRAME_HEADER));
    
    dst_hdr->frameType = (hdr->flags & 0x3) == IPC_FRAME_FLAG_IFRAME ? IPC_FRAME_FLAG_IFRAME : IPC_FRAME_FLAG_PBFRAME;
    dst_hdr->codec = hdr->codec_id;
    
    // 使用时间戳
    time_t local_ts = get_now();
    dst_hdr->utcTime = local_ts;
    
    // 视频时间戳（不需要转换）
    dst_hdr->timeStamp = hdr->timestamp;
    
    dst_hdr->size = size;

    // union
    if (hdr->codec_id <= MEDIA_CODEC_VIDEO_HEVC) {
        // video
        dst_hdr->videoFps = hdr->flags >> 2;
        dst_hdr->videoWidth = 0;
        dst_hdr->videoHeight = 0;
    }
    else {
        // audio
        dst_hdr->audioChannel = 0;
        dst_hdr->audioSampleRate = 0;
        dst_hdr->audioBitsPerSample = 0;
    }
    
    return 0;
}

INT32 BufferManager::pushFrame(UINT32 streamChn, FRAMEINFO_t* hdr, void* data, uint32_t size)
{
    if (!initialized) {
        LogE("BufferManager 未初始化: chn = %d", chn);
        return -1;
    }
    
    if (streamChn >= VS_MAX_STREAM_NUM) {
        LogE("无效的流通道: chn = %d, streamChn = %u", chn, streamChn);
        return -1;
    }
    
    // 创建帧头
    T_FRAME_HEADER frame_hdr;
    int ret = createFrameHeader(&frame_hdr, hdr, size);
    if (ret != 0) {
        LogE("创建帧头失败: chn = %d, streamChn = %u", chn, streamChn);
        return -1;
    }
    
    // 写入缓冲区
    if (write_handles[streamChn] != INVALID_HANDLE_VALUE) {
        return mb_writer_push(write_handles[streamChn], &frame_hdr, (UINT8 *)data);
    }
    
    return 0;
}

VOID BufferManager::getMbCacheNum(UINT8 streamChn, UINT32 *cache_num, UINT32 *mb_size) 
{
    *cache_num = 30;
    *mb_size = 500;
    
    if (QfInRange(streamChn, 0, VS_MAX_STREAM_NUM-1)) {
        // 如果此值有修改,就采用新的值
        if (video_mb_cache_sec[streamChn] > 0)
            mb_sec = video_mb_cache_sec[streamChn];
        
        // 保存此值
        video_mb_cache_sec[streamChn] = mb_sec;

        // 音频包个数        
        INT32 audioChannel       = 1;
        INT32 audioSampleRate    = OT_AUDIO_SAMPLE_RATE_8000;
        INT32 audioBitsPerSample = 16;
        INT32 pack_size          = 160*2;
        UINT32 apacks_1sec = ((audioSampleRate*audioChannel*audioBitsPerSample/8/pack_size)>>1);
        if (apacks_1sec EQU 0) {
            apacks_1sec = ((8000*1*16/8/pack_size)>>1);
        }

        // 计算一秒需要的缓存
        mb_sec = QF_MAX(1, mb_sec);
        *cache_num = (videoFps+apacks_1sec) * mb_sec;
        
        // 修复：直接计算字节数，不要左移10位
        *mb_size = (mb_sec * bitrate) >> 3;  // 转换为字节
        *mb_size = QF_MAX(*mb_size, 500*1024);  // 最小500KB

        // 针对分辨率做特殊处理 - 默认使用4K分辨率(3840*2160)参数
        *mb_size = QF_MAX(*mb_size, 2*1024*1024);  // 最小2MB
        
        // 限制最大大小，避免内存分配过大
        *mb_size = QF_MIN(*mb_size, 100*1024*1024);  // 最大100MB
    }
}

INT32 BufferManager::startVencData(INT32 chn, UINT32 streamChn)
{
    if (streamChn >= VS_MAX_STREAM_NUM) {
        LogE("启动编码数据失败: 无效的码流通道: chn=%d, streamChn=%u", chn, streamChn);
        return -1;
    }
    
    // 打开写入缓冲区
    INT32 ret = openWriteBuffer(streamChn);
    if (ret != 0) {
        LogE("启动编码数据失败: 无法打开写入缓冲区: chn=%d, streamChn=%u", chn, streamChn);
        return -1;
    }


    return 0;
}

void BufferManager::stopVencData(UINT32 streamChn)
{
    if (streamChn >= VS_MAX_STREAM_NUM) {
        return;
    }
    
    // 关闭写入缓冲区
    closeWriteBuffer(streamChn);
}

HANDLE BufferManager::openReaderStream(UINT32 streamChn)
{
	// 关闭可能已存在的读取流
	closeReaderStream();
	
	// 修改：使用mb_reader_open_ex2并传递通道号，增加重试机制
    int retry_count = 0;
    const int max_retries = 3;
    HANDLE handle = INVALID_HANDLE_VALUE;
    
    while (handle == INVALID_HANDLE_VALUE && retry_count < max_retries) {
        handle = mb_reader_open_ex2((MB_TYPE)streamChn, 0x3, chn);
        
        if (handle == INVALID_HANDLE_VALUE) {
            retry_count++;
            // 只有第一次失败时打印日志，避免频繁输出
            if (retry_count == 1) {
                LogE("打开媒体读取流失败，尝试重试: chn=%d, streamChn=%d, mb_type=%d", 
                     chn, streamChn, streamChn);
            }
            // 短暂等待后重试
            usleep(50000); // 50ms
        }
    }
	
	if (handle == INVALID_HANDLE_VALUE) {
		LogE("打开媒体读取流失败(最终): chn=%d, streamChn=%d, mb_type=%d", chn, streamChn, streamChn);
		return INVALID_HANDLE_VALUE;
	}
	
	// 保存句柄
	reader_handle = handle;
	
	// 初始化读取上下文
	read_context.checkKeyFrame = true;
	read_context.first_trans = true;
	read_context.timestamp = 0;
	read_context.audio_handle = INVALID_HANDLE_VALUE;

	return handle;
}


// 打开缓存流
HANDLE BufferManager::openPreReaderStream(UINT32 streamChn)
{
	// 关闭可能已存在的读取流
	closeReaderStream();
	
	// 修改：使用mb_reader_open_last_ex2并传递通道号，增加重试机制
    int retry_count = 0;
    const int max_retries = 3;
    HANDLE handle = INVALID_HANDLE_VALUE;
    
    while (handle == INVALID_HANDLE_VALUE && retry_count < max_retries) {
        handle = mb_reader_open_last_ex2((MB_TYPE)streamChn, 0x3, chn, __func__);
        
        if (handle == INVALID_HANDLE_VALUE) {
            retry_count++;
            // 只有第一次失败时打印日志，避免频繁输出
            if (retry_count == 1) {
                LogE("打开预录流失败，尝试重试: chn=%d, streamChn=%d, mb_type=%d", 
                     chn, streamChn, streamChn);
            }
            // 短暂等待后重试
            usleep(50000); // 50ms
        }
    }
	
	if (handle == INVALID_HANDLE_VALUE) {
		LogE("打开预录流失败(最终): chn=%d, streamChn=%d, mb_type=%d", chn, streamChn, streamChn);
		return INVALID_HANDLE_VALUE;
	}
	
	// 保存句柄
	reader_handle = handle;
	
	// 初始化读取上下文
	read_context.checkKeyFrame = true;
	read_context.first_trans = true;
	read_context.timestamp = 0;
	read_context.audio_handle = INVALID_HANDLE_VALUE;

	return handle;
}


void BufferManager::closeReaderStream()
{
    if (reader_handle != INVALID_HANDLE_VALUE) {
        mb_reader_close(reader_handle);
        reader_handle = INVALID_HANDLE_VALUE;
    }
}

INT32 BufferManager::readFrame(T_FRAME_HEADER** frame_hdr, CHAR** buffer, bool* is_key_frame, UINT64* out_timestamp)
{
    if (reader_handle == INVALID_HANDLE_VALUE || !buffer) {
        return FAIL;
    }
    
    // 从媒体缓冲区读取一帧
    INT32 fnRet = mb_reader_pop(reader_handle, frame_hdr, buffer);
    if (fnRet != OK) {
        // 有跳帧,必须等到I帧
        if (fnRet == FAIL-1) {
            read_context.checkKeyFrame = TRUE;
        }
        return fnRet;
    }
    
    // 检查I帧 - 仅在需要时执行
    if (read_context.checkKeyFrame && (*frame_hdr)->frameType != IPC_FRAME_FLAG_IFRAME) {
        return FAIL-1;  // 需要I帧但当前不是I帧
    }
    
    if (read_context.checkKeyFrame) {
        if (read_context.audio_handle != INVALID_HANDLE_VALUE) {
            mb_reader_reset_pos(read_context.audio_handle);
        }
        read_context.checkKeyFrame = FALSE;
    }
    
    // 更新读取上下文
    read_context.timestamp = (*frame_hdr)->timeStamp;
    
    // 设置输出参数
    if (is_key_frame) {
        *is_key_frame = ((*frame_hdr)->frameType == FT_IFRAME);
    }
    
    if (out_timestamp) {
        *out_timestamp = (*frame_hdr)->timeStamp;
    }
    
    return OK;
}



void BufferManager::resetReaderPosition()
{
    if (reader_handle != INVALID_HANDLE_VALUE) {
        mb_reader_reset_pos(reader_handle);
        
        // 设置等待关键帧标志
        read_context.checkKeyFrame = TRUE;
        read_context.first_trans = TRUE;

    }
}

// 重置读缓冲 预录像
void BufferManager::resetPreReaderPosition()
{
    if (reader_handle != INVALID_HANDLE_VALUE) {
        mb_reader_reset_last_pos(reader_handle);
        
        // 设置等待关键帧标志
        read_context.checkKeyFrame = TRUE;
        read_context.first_trans = TRUE;

    }
}


void BufferManager::setAudioHandle(HANDLE audio_handle)
{
    read_context.audio_handle = audio_handle;
}
// ========== BufferManager 类实现 ==========



// 开启缓冲区
INT32 start_venc_data(INT32 chn, UINT32 streamChn)
{
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("启动编码数据失败: 无法获取 ChannelManager 单例实例");
        return -1;
    }

    Channel* pChannel = channelManager->getChannel(chn);
    if (!pChannel) {
        LogE("启动编码数据失败: 无法获取通道 %d", chn);
        return -1;
    }
    
    BufferManager* pBufferMgr = pChannel->getBufferManager();
    if (!pBufferMgr) {
        LogE("启动编码数据失败: 无法获取缓冲区管理器: chn=%d", chn);
        return -1;
    }
    
    return pBufferMgr->startVencData(chn, streamChn);
}

// 关闭缓冲区
VOID stop_venc_data(INT32 chn, UINT32 streamChn)
{
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("停止编码数据失败: 无法获取 ChannelManager 单例实例");
        return;
    }

    Channel* pChannel = channelManager->getChannel(chn);
    if (!pChannel) {
        LogE("停止编码数据失败: 无法获取通道 %d", chn);
        return;
    }
    
    BufferManager* pBufferMgr = pChannel->getBufferManager();
    if (!pBufferMgr) {
        LogE("停止编码数据失败: 无法获取缓冲区管理器: chn=%d", chn);
        return;
    }
    
    pBufferMgr->stopVencData(streamChn);
}

// 推送流 到MB中
VOID push_frame_mb(INT32 chn, UINT32 streamChn, FRAMEINFO_t *hdr, LPVOID data, UINT32 size)
{
    if (!hdr || !data || size == 0) {
        return;
    }

    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("推送帧失败: 无法获取 ChannelManager 单例实例");
        return;
    }

    Channel* pChannel = channelManager->getChannel(chn);
    if (!pChannel) {
        LogE("推送帧失败: 无法获取通道 %d", chn);
        return;
    }
    
    BufferManager* pBufferMgr = pChannel->getBufferManager();
    if (!pBufferMgr) {
        LogE("推送帧失败: 无法获取缓冲区管理器: chn=%d", chn);
        return;
    }
    
    pBufferMgr->pushFrame(streamChn, hdr, data, size);
}



