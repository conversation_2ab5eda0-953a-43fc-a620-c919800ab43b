#ifndef DISKMANAGE_H
#define DISKMANAGE_H

// QT接口函数，内部调用DiskManager静态方法

// 检测是否是磁盘设备（如 /dev/sda），用于QT界面
int is_disk_device(const char *name);

// 判断是否是分区（如 /dev/sda1），是则返回1，否则返回0，用于QT界面
int is_partition(const char *dev);

// 生成第一个分区名，如 /dev/sda -> /dev/sda1，用于QT界面
void get_first_partition(const char *dev, char *out, size_t out_size);

// 获取挂载点，用于QT界面
char* get_mount_point_from_device(const char *part_dev);

// mount磁盘，用于QT界面
int mount_disk(const char *device);

// 使用parted分区，用于QT界面
int vs_delete_format_part(const char *dev_disk);

// 删除所有分区并格式化，用于QT界面
int vs_format_disk(const char *disk);

#endif // DISKMANAGE_H
