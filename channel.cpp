#include <vector>
#include <string>
#include <iostream>
#include <memory>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <fcntl.h>
#include <errno.h>
#include <ctime>
#include <chrono>

#include "vs_media_buffer.h"
#include "channel.h"
#include "record.h"
#include "playback.h"
#include "disk_manager.h"



// 使用 DiskManager 单例模式，不需要外部声明

// 单例相关静态成员初始化
ChannelManager* ChannelManager::m_instance = nullptr;
TCSLock ChannelManager::m_instanceMutex;

// ========== ChannelManager 单例实现 ==========

// 获取单例实例
ChannelManager* ChannelManager::getInstance()
{
    // 双重检查锁定模式
    if (m_instance == nullptr) {
        m_instanceMutex.Enter();
        if (m_instance == nullptr) {
            m_instance = new ChannelManager();
        }
        m_instanceMutex.Leave();
    }
    return m_instance;
}

// 销毁单例实例
void ChannelManager::destroyInstance()
{
    m_instanceMutex.Enter();
    if (m_instance != nullptr) {
        delete m_instance;
        m_instance = nullptr;
    }
    m_instanceMutex.Leave();
}

// ========== 顶层初始化接口实现 ==========

/**
 * 通道模块顶层初始化函数
 * 供主程序启动时调用，其他模块不得调用
 *
 * @return true 初始化成功, false 初始化失败
 */
bool channel_module_initialize()
{
    // 获取通道管理器单例实例
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("通道模块初始化失败: 无法获取 ChannelManager 单例实例");
        return false;
    }

    // 初始化通道管理器（内部会检查是否已经初始化）
    bool result = channelManager->initialize();

    if (!result) {
        LogE("通道模块初始化失败");
    }

    return result;
}

/**
 * 通道模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void channel_module_cleanup()
{
    // 获取通道管理器单例实例
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        return;
    }

    if (!channelManager->isModuleInitialized()) {
        return;
    }

    // 销毁所有通道
    channelManager->destroyAllChannels();

    // 销毁通道管理器单例实例
    ChannelManager::destroyInstance();

}

/**
 * 检查通道模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool channel_module_is_initialized()
{
    ChannelManager* channelManager = ChannelManager::getInstance();
    return channelManager ? channelManager->isModuleInitialized() : false;
}


// 记录API函数的声明
// 这些在其他文件中已经实现，这里只是声明供内部函数调用
extern bool record_thread_is_recording(int channel);
extern bool record_thread_is_event_recording(int channel);

Channel::Channel(INT32 channelId)
    : m_channelId(channelId)
{
    // 记录开始时间，用于性能统计
    auto start_time = std::chrono::steady_clock::now();
    
    m_recordChannel = new RecordChannel(channelId);
    m_recordManager = new RecordManager(channelId);
    // PlaybackManager 现在使用单例模式，不再为每个通道创建独立实例
    m_playbackManager = SimplePlaybackManager::getInstance();
    m_bufferManager = new BufferManager(channelId);
    // 移除 m_diskManager 创建，使用单例模式

    m_preRecordEnabled = false;
    m_preRecordDuration = 10; // 默认10秒
    m_preRecordBufferSize = 300; // 默认300帧

    // 初始化缓冲区
    m_bufferManager->init();

    // 不再创建独立的磁盘管理器，使用单例实例
    
    // 统计创建时间
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();

}

Channel::~Channel()
{
    // 停止所有活动
    stopRecord();
    closeAllBuffers();
    
    // 释放资源
    delete m_recordChannel;
    delete m_recordManager;
    // PlaybackManager 是单例，不需要删除
    m_playbackManager = nullptr;
    delete m_bufferManager;
    // 不再删除 m_diskManager，使用单例实例
}

INT32 Channel::startRecord(UINT32 streamMask)
{
    // 启动录像
    bool result = m_recordManager->startRecord(streamMask);
    
    // 如果录像启动成功，启动录像通道
    if (result) {
        m_recordChannel->start();
    }
    
    return result ? 0 : -1;
}

INT32 Channel::startRecordWithPreRecord(UINT32 streamMask, INT32 preRecordDuration)
{
    // 如果预录像已启用，使用vs_media_file.cpp中的预录像功能
    if (m_preRecordEnabled) {

        // 设置预录像参数
        m_recordChannel->setPreRecordParams(true, preRecordDuration);
        
        // 使用事件录像消息启动录像
        return startEventRecord(streamMask, AET_EVENT_MOTIONDECT, preRecordDuration);
    } else {
        // 预录像未启用，直接启动普通录像
        return startRecord(streamMask);
    }
}

INT32 Channel::stopRecord()
{
    // 停止录像通道
    m_recordChannel->stop();
    
    // 关闭所有缓冲区
    closeAllBuffers();
    
    // 重置预录像状态
    m_recordChannel->setPreRecordParams(false, 0);
    
    return OK;
}

INT32 Channel::pushFrame(UINT32 streamChn, FRAMEINFO_t* hdr, void* data, uint32_t size)
{
    // 确保推送的帧与通道ID一致
    return m_bufferManager->pushFrame(streamChn, hdr, data, size);
}

void Channel::setEventType(UINT8 event_type)
{
    m_recordChannel->setEventType(event_type);
}


// 修改 closeAllBuffers 方法
void Channel::closeAllBuffers()
{
    m_bufferManager->closeAllWriteBuffers(); // 修正：移除参数
}


// 按通道清理磁盘空间
bool Channel::cleanupChannelSpace(uint64_t bytesToFree)
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager) {
        LogE("通道 %d: 无法获取 DiskManager 单例实例", m_channelId);
        return false;
    }

    // 调用 DiskManager 单例的按通道清理方法
    return diskManager->cleanupChannelSpace(m_channelId, bytesToFree);
}

// 请求异步清盘
bool Channel::requestAsyncCleanup(uint64_t bytesToFree)
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager) {
        LogE("通道 %d: 无法获取 DiskManager 单例实例", m_channelId);
        return false;
    }

    // 调用 DiskManager 单例的异步清理方法
    diskManager->requestAsyncCleanup(m_channelId, bytesToFree);
    return true;
}

void Channel::setPreRecordParams(bool enabled, INT32 duration, INT32 bufferSize)
{
    // 设置预录像参数
    m_preRecordEnabled = enabled;
    m_preRecordDuration = duration;
    if (bufferSize > 0) {
        m_preRecordBufferSize = bufferSize;
    }
    
    // 更新录像通道的预录像参数
    m_recordChannel->setPreRecordParams(enabled, duration);
}

bool Channel::isPreRecordEnabled() const
{
    return m_preRecordEnabled;
}

INT32 Channel::getPreRecordDuration() const
{
    return m_preRecordDuration;
}

// 开始事件录像
INT32 Channel::startEventRecord(UINT32 streamMask, ALARM_EVENT_TYPE eventType, INT32 duration)
{
    if (eventType == AET_EVENT_ALL) {
        LogE("启动事件录像失败: 无效的事件类型");
        return -1;
    }


    // 先启动录像
    // if (!isRecording()) 
	{
        INT32 ret = startRecord(streamMask);
        if (ret != 0) {
            LogE("启动事件录像失败: 无法启动录像 ret=%d", ret);
            return ret;
        }
    }
    
    return 0;
}

// 停止事件录像
INT32 Channel::stopEventRecord()
{

    return 0;
}


// ========== ChannelManager 类实现 ==========

ChannelManager::ChannelManager() : m_initialized(false), m_moduleInitialized(false)
{}

ChannelManager::~ChannelManager()
{
    destroyAllChannels();
}

// 初始化通道管理器（支持任意正整数通道ID）
bool ChannelManager::initialize()
{
    if (m_initialized) {
        return true;
    }

    m_initialized = true;
    m_moduleInitialized = true;

    return true;
}

// 获取通道（按需创建，唯一入口）
Channel* ChannelManager::getChannel(INT32 chn)
{
    // 检查模块是否已初始化
    if (!m_moduleInitialized) {
        LogE("获取通道失败: 通道模块未初始化，请先调用 channel_module_initialize()");
        return nullptr;
    }

    if (!m_initialized) {
        LogE("获取通道失败: ChannelManager 未初始化");
        return nullptr;
    }

    // 架构重构：检查通道ID是否有效（支持任意正整数通道ID）
    if (!isChannelIdValid(chn)) {
        LogE("获取通道失败: 无效的通道ID %d (必须为非负整数)", chn);
        return nullptr;
    }

    // 检查是否已存在
    auto it = channels.find(chn);
    if (it != channels.end()) {
        return it->second;
    }

    // 按需创建新通道
    return createChannelInternal(chn);
}

// 内部创建通道方法
Channel* ChannelManager::createChannelInternal(INT32 chn)
{
    // 创建新通道
    Channel* channel = new Channel(chn);
    if (!channel) {
        LogE("创建通道失败: 内存分配失败 chn = %d", chn);
        return nullptr;
    }

    channels[chn] = channel;
    return channel;
}

// 销毁指定通道（统一销毁入口）
bool ChannelManager::destroyChannel(INT32 chn)
{
    if (!m_initialized) {
        LogE("销毁通道失败: ChannelManager 未初始化");
        return false;
    }

    auto it = channels.find(chn);
    if (it == channels.end()) {
        return false;
    }

    delete it->second;
    channels.erase(it);

    return true;
}

// 销毁所有通道（程序退出时调用）
void ChannelManager::destroyAllChannels()
{
    if (!m_initialized) {
        return;
    }

    if (channels.empty()) {
        return;
    }

    for (auto& pair : channels) {
        delete pair.second;
    }

    channels.clear();
    m_initialized = false;
}

// 检查通道是否存在
bool ChannelManager::hasChannel(INT32 chn) const
{
    if (!m_initialized) {
        return false;
    }

    return channels.find(chn) != channels.end();
}

// 获取所有已存在的通道ID列表
std::vector<INT32> ChannelManager::getExistingChannels() const
{
    std::vector<INT32> existing_channels;
    for (const auto& pair : channels) {
        existing_channels.push_back(pair.first);
    }
    return existing_channels;
}

// 架构重构：获取所有活跃通道ID列表（与RecordEventManager保持一致）
std::vector<INT32> ChannelManager::getActiveChannels() const
{
    std::vector<INT32> active_channels;

    if (!m_initialized) {
        return active_channels;
    }

    for (const auto& pair : channels) {
        INT32 channelId = pair.first;
        Channel* channel = pair.second;

        if (channel) {
            // 检查通道是否有活跃的录像或其他活动
            RecordChannel* recordChannel = channel->getRecordChannel();
            if (recordChannel && recordChannel->isRunning()) {
                active_channels.push_back(channelId);
            }
            // 可以根据需要添加其他活跃状态检查
            // 例如：播放状态、编码状态等
        }
    }

    return active_channels;
}

// 获取通道总数
size_t ChannelManager::getChannelCount() const
{
    return channels.size();
}

// 架构重构：检查通道ID是否有效（支持任意正整数通道ID）
bool ChannelManager::isChannelIdValid(INT32 chn) const
{
    // 移除最大通道数限制，只检查是否为非负整数
    return (chn >= 0);
}




