#!/bin/bash

# 紧急磁盘清理脚本
# 用于立即释放磁盘空间

echo "=== 紧急磁盘清理脚本 ==="
echo "开始时间: $(date)"

# 磁盘路径
DISK1="/mnt/custom/disk/disk1"
DISK2="/mnt/custom/disk/disk2"

# 检查磁盘使用情况
echo ""
echo "=== 当前磁盘使用情况 ==="
df -h $DISK1 $DISK2

# 函数：清理指定磁盘的旧文件
cleanup_disk() {
    local disk_path=$1
    local target_free_gb=$2
    
    echo ""
    echo "=== 清理磁盘: $disk_path ==="
    
    if [ ! -d "$disk_path" ]; then
        echo "磁盘路径不存在: $disk_path"
        return 1
    fi
    
    # 获取当前使用率
    local usage=$(df "$disk_path" | tail -1 | awk '{print $5}' | sed 's/%//')
    echo "当前使用率: ${usage}%"
    
    if [ "$usage" -lt 95 ]; then
        echo "磁盘使用率低于95%，无需清理"
        return 0
    fi
    
    echo "开始清理旧文件..."
    
    # 清理策略1：删除7天前的录像文件
    echo "删除7天前的录像文件..."
    find "$disk_path" -name "*.h264" -type f -mtime +7 -delete 2>/dev/null
    find "$disk_path" -name "*.idx" -type f -mtime +7 -delete 2>/dev/null
    
    # 检查清理效果
    local new_usage=$(df "$disk_path" | tail -1 | awk '{print $5}' | sed 's/%//')
    echo "清理后使用率: ${new_usage}%"
    
    # 如果还是太满，继续清理5天前的文件
    if [ "$new_usage" -gt 90 ]; then
        echo "继续删除5天前的录像文件..."
        find "$disk_path" -name "*.h264" -type f -mtime +5 -delete 2>/dev/null
        find "$disk_path" -name "*.idx" -type f -mtime +5 -delete 2>/dev/null
        
        new_usage=$(df "$disk_path" | tail -1 | awk '{print $5}' | sed 's/%//')
        echo "再次清理后使用率: ${new_usage}%"
    fi
    
    # 如果还是太满，清理3天前的文件
    if [ "$new_usage" -gt 85 ]; then
        echo "继续删除3天前的录像文件..."
        find "$disk_path" -name "*.h264" -type f -mtime +3 -delete 2>/dev/null
        find "$disk_path" -name "*.idx" -type f -mtime +3 -delete 2>/dev/null
        
        new_usage=$(df "$disk_path" | tail -1 | awk '{print $5}' | sed 's/%//')
        echo "最终清理后使用率: ${new_usage}%"
    fi
    
    # 清理空目录
    echo "清理空目录..."
    find "$disk_path" -type d -empty -delete 2>/dev/null
    
    echo "磁盘 $disk_path 清理完成"
}

# 函数：检查并清理临时文件
cleanup_temp_files() {
    echo ""
    echo "=== 清理临时文件 ==="
    
    # 清理系统临时文件
    echo "清理 /tmp 目录..."
    find /tmp -type f -mtime +1 -delete 2>/dev/null
    
    # 清理日志文件（保留最近3天）
    echo "清理旧日志文件..."
    find /var/log -name "*.log" -type f -mtime +3 -delete 2>/dev/null
    
    # 清理核心转储文件
    echo "清理核心转储文件..."
    find / -name "core.*" -type f -delete 2>/dev/null
}

# 函数：显示最大的文件和目录
show_large_files() {
    local disk_path=$1
    echo ""
    echo "=== $disk_path 中最大的10个目录 ==="
    du -h "$disk_path"/* 2>/dev/null | sort -hr | head -10
    
    echo ""
    echo "=== $disk_path 中最大的10个文件 ==="
    find "$disk_path" -type f -exec du -h {} + 2>/dev/null | sort -hr | head -10
}

# 主清理流程
main() {
    # 清理临时文件
    cleanup_temp_files
    
    # 清理两个磁盘
    cleanup_disk "$DISK1" 2
    cleanup_disk "$DISK2" 2
    
    # 显示清理后的磁盘使用情况
    echo ""
    echo "=== 清理后磁盘使用情况 ==="
    df -h $DISK1 $DISK2
    
    # 显示大文件信息（用于进一步分析）
    show_large_files "$DISK1"
    show_large_files "$DISK2"
    
    echo ""
    echo "=== 清理完成 ==="
    echo "结束时间: $(date)"
    
    # 检查是否需要进一步清理
    local disk1_usage=$(df "$DISK1" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//')
    local disk2_usage=$(df "$DISK2" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$disk1_usage" -gt 90 ] || [ "$disk2_usage" -gt 90 ]; then
        echo ""
        echo "⚠️  警告：磁盘使用率仍然很高，建议："
        echo "1. 检查是否有大文件占用空间"
        echo "2. 考虑增加磁盘容量"
        echo "3. 调整录像保存策略"
        echo "4. 检查是否有进程在大量写入文件"
    else
        echo ""
        echo "✅ 清理成功，磁盘使用率已降至安全水平"
    fi
}

# 执行主函数
main

# 重启相关服务以确保清盘功能正常
echo ""
echo "=== 重启磁盘管理服务 ==="
# 这里可以添加重启服务的命令，根据实际情况调整
# systemctl restart your-disk-service
echo "请手动重启应用程序以确保磁盘管理功能正常工作"
