#pragma once

#include <string>
#include <vector>
#include <pthread.h>
#include <atomic>
#include <memory>
#include <functional>
#include <map>
#include <set>
#include "low_delay_common.h"
#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "Thread/Lock.hpp"

#include "record.h"

// ========== 必要的宏定义 ==========
#define MAX_DECODE_CHANNELS 5
#define MAX_PLAYBACK_SIZE   5*1024*1024
#define REC_DB_INDEX "index.db"

// ========== KISS重构：统一接口定义 ==========

// 统一的回放参数结构
struct PlaybackParams {
    int channelId;
    uint32_t startTime;
    uint32_t endTime;
    int speed = 1;
    std::string diskPath;

    // 验证参数有效性
    bool isValid() const {
        return channelId >= 0 && startTime < endTime && !diskPath.empty();
    }

    // 默认构造
    PlaybackParams() : channelId(-1), startTime(0), endTime(0), speed(1) {}

    // 便利构造
    PlaybackParams(int chn, uint32_t start, uint32_t end, const std::string& disk = "", int spd = 1)
        : channelId(chn), startTime(start), endTime(end), speed(spd), diskPath(disk) {}
};

// 统一的回放结果枚举
enum class PlaybackResult {
    SUCCESS,
    INVALID_PARAMS,
    FILE_NOT_FOUND,
    DECODE_ERROR,
    RESOURCE_ERROR,
    ALREADY_RUNNING,
    NOT_RUNNING,
    SEEK_FAILED
};

// 简化的回放状态枚举
enum class PlaybackState {
    STOPPED,
    PLAYING,
    PAUSED,
    SEEKING,
    ERROR
};

// 回放事件枚举
enum class PlaybackEvent {
    START,
    STOP,
    PAUSE,
    RESUME,
    SEEK,
    ERROR_OCCURRED,
    FILE_END
};

// 简化的状态机
class PlaybackStateMachine {
public:
    PlaybackStateMachine() : m_currentState(PlaybackState::STOPPED) {}

    bool canTransition(PlaybackState from, PlaybackState to) const;
    PlaybackState transition(PlaybackEvent event);
    PlaybackState getCurrentState() const { return m_currentState; }

private:
    PlaybackState m_currentState;

    // 状态转换表
    static const std::map<std::pair<PlaybackState, PlaybackEvent>, PlaybackState> s_transitions;
};

// ========== 必要的结构体定义 ==========

// 多磁盘回放上下文
typedef struct {
    FILE *fidx;             // 索引
    FILE *fvideo;           // 视频
    
    UINT32   fseq_day;      // 文件位于数据库第几条
    STimeDay stTimeDay;     // 日期(记录时只有日期有效)    
    T_INDEX_ENTRY index_entry;  // 录像文件索引
    T_FRAME_HEADER hdr;         // 录像头
    INT32    frame_size;        // 帧大小    
    UINT8    is_video;          // 是否视频帧
    time_t   f_st_time;         // 文件开始时间
    time_t   f_end_time;        // 文件结束时间
    UINT8    f_fps;             // 帧率
    UINT8    with_tz;           // 文件时间附加时区
    UINT32   videoWidth;        // 视频宽度
    UINT32   videoHeight;       // 视频高度
    CHAR     disk_top[256];     // 磁盘路径
} T_MDISK_PLAY_CONTEXT;


// 多磁盘回放句柄
typedef T_MDISK_PLAY_CONTEXT* T_MDISK_PLAY_HADLE;

// 回放文件时间结构体
typedef struct {
    time_t st_time;             // 开始时间
    time_t end_time;            // 结束时间
    CHAR disk_top[128];         // 当前录像文件的磁盘路径
    CHAR file_crop_name[128];   // 用于显示的文件名
    CHAR file_path[512];        // 文件路径
    UINT32 file_size;           // 文件大小
    UINT8 event_type;           // 事件类型
} T_PLAYBACK_FILE_TIME;

// ========== KISS重构：RAII资源管理类 ==========

// 自动资源管理类
class PlaybackResource {
public:
    PlaybackResource(size_t bufferSize = MAX_PLAYBACK_SIZE)
        : m_videoBuffer(new char[bufferSize])
        , m_audioBuffer(new char[64*1024])
        , m_bufferSize(bufferSize) {}

    ~PlaybackResource() {
        delete[] m_videoBuffer;
        delete[] m_audioBuffer;
    }

    char* getVideoBuffer() { return m_videoBuffer; }
    char* getAudioBuffer() { return m_audioBuffer; }
    size_t getBufferSize() const { return m_bufferSize; }

    // 禁止拷贝，允许移动
    PlaybackResource(const PlaybackResource&) = delete;
    PlaybackResource& operator=(const PlaybackResource&) = delete;

private:
    char* m_videoBuffer;
    char* m_audioBuffer;
    size_t m_bufferSize;
};

// 文件句柄自动管理
class FileHandle {
public:
    FileHandle(const std::string& path, const std::string& mode)
        : m_file(fopen(path.c_str(), mode.c_str())), m_path(path) {}

    ~FileHandle() {
        if (m_file) fclose(m_file);
    }

    FILE* get() { return m_file; }
    bool isValid() const { return m_file != nullptr; }
    const std::string& getPath() const { return m_path; }

    // 禁止拷贝，允许移动
    FileHandle(const FileHandle&) = delete;
    FileHandle& operator=(const FileHandle&) = delete;

private:
    FILE* m_file;
    std::string m_path;
};

// ========== KISS重构：专职类声明 ==========

// 前向声明
class SimplePlaybackSession;

// 1. 回放会话管理器（简化）
class PlaybackSessionManager {
public:
    PlaybackSessionManager();
    ~PlaybackSessionManager();

    int createSession();
    bool destroySession(int sessionId);
    SimplePlaybackSession* getSession(int sessionId);
    std::vector<int> getActiveSessions() const;

private:
    std::map<int, std::unique_ptr<SimplePlaybackSession>> m_sessions;
    std::atomic<int> m_nextSessionId{1};
    mutable TCSLock m_sessionLock;
};

// 2. 媒体文件读取器（简化）
class MediaFileReader {
public:
    MediaFileReader();
    ~MediaFileReader();

    bool openFile(const std::string& filePath, uint32_t startTime);
    int readFrame(char* buffer, uint32_t bufferSize, T_FRAME_HEADER* header);
    bool seekToTime(uint32_t timestamp);
    void closeFile();
    bool isFileOpen() const;

private:
    std::unique_ptr<FileHandle> m_videoFile;
    std::unique_ptr<FileHandle> m_indexFile;
    T_INDEX_ENTRY m_currentIndex;
    bool m_isOpen;
};

// 3. 回放控制器（简化）
class PlaybackController {
public:
    PlaybackController();
    ~PlaybackController();

    bool startPlayback(int channelId, uint32_t startTime, uint32_t endTime);
    void stopPlayback();
    void pausePlayback(bool pause);
    void setSpeed(int speed);
    PlaybackState getState() const;

private:
    PlaybackSessionManager m_sessionManager;
    MediaFileReader m_fileReader;
    PlaybackStateMachine m_stateMachine;
    std::atomic<bool> m_isPlaying{false};
    int m_currentSessionId{-1};
};

// ========== KISS重构：简化的回放管理器 ==========

class SimplePlaybackManager {
public:
    static SimplePlaybackManager* getInstance();
    static void destroyInstance();

    // 简化的核心接口
    PlaybackResult startPlayback(const PlaybackParams& params);
    PlaybackResult stopPlayback();
    PlaybackResult pausePlayback(bool pause);
    PlaybackResult seekToTime(uint32_t timestamp);

    // 状态查询
    PlaybackState getState() const;
    uint32_t getCurrentTime() const;

    // 速度控制
    PlaybackResult setSpeed(int speed);

    // 录像查询
    bool queryRecordFiles(const std::string& date, int channelId, std::multiset<T_PLAYBACK_FILE_TIME>& fileList);

    // 多磁盘回放接口（按KISS原则简化实现）
    T_MDISK_PLAY_HADLE mdiskPlayOpen(const CHAR* disk_top, INT32 chn, const STimeDay* stTimeDay, INT32 speed, const CHAR* disk_path);
    BOOL mdiskPlayClose(T_MDISK_PLAY_HADLE handle, BOOL force);
    INT32 mdiskPlayReadFrame(T_MDISK_PLAY_HADLE handle, INT32 chn, CHAR* frame_buf, UINT32 buf_size, T_FRAME_HEADER* frame_header);
    BOOL mdiskPlaySeek(T_MDISK_PLAY_HADLE handle, const STimeDay* stTimeDay);

    // 向后兼容接口
    int mdisk_playback_test(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top);

private:
    SimplePlaybackManager();
    ~SimplePlaybackManager();

    // 禁用拷贝构造和赋值操作
    SimplePlaybackManager(const SimplePlaybackManager&) = delete;
    SimplePlaybackManager& operator=(const SimplePlaybackManager&) = delete;

    static SimplePlaybackManager* m_instance;
    static TCSLock m_instanceMutex;

    std::unique_ptr<PlaybackController> m_controller;
};


// 导出管理类 - 单例模式

// 导出状态结构体
typedef struct {
    BOOL running;               // 是否正在导出
    BOOL cancel_flag;           // 取消标志
    BOOL by_time;               // 是否按时间导出
    float progress;             // 导出进度 (0.0 - 1.0)
    INT32 chn;                  // 通道号
    UINT32 start_time;          // 开始时间 (按时间导出时使用)
    UINT32 end_time;            // 结束时间 (按时间导出时使用)
    CHAR source_file[512];      // 源文件路径 (按文件导出时使用)
    CHAR target_file[512];      // 目标文件路径
    CHAR disk_path[256];        // 磁盘路径
    pthread_t export_thread;    // 导出线程
} T_EXPORT_STATUS;


class ExportManager {
public:
    // 获取单例实例
    static ExportManager* getInstance();
    static void destroyInstance();

    // 导出功能
    bool exportRecordToAvi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path = NULL);
    bool exportRecordToAviByTime(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path = NULL);
    bool isExportRunning() const;
    void cancelExport();
    float getExportProgress() const;

private:
    ExportManager();
    ~ExportManager();

    // 禁用拷贝构造和赋值操作
    ExportManager(const ExportManager&) = delete;
    ExportManager& operator=(const ExportManager&) = delete;

    // 导出线程函数
    static void* exportThreadByFile(void* arg);
    static void* exportThreadByTime(void* arg);
    void exportThreadByFileWork();
    void exportThreadByTimeWork();

    // 成员变量
    T_EXPORT_STATUS m_exportStatus;     // 导出状态
    mutable TCSLock m_exportMutex;      // 导出锁

    // 单例相关
    static ExportManager* m_instance;
    static TCSLock m_instanceMutex;
};



// ========== 必要的工具函数声明 ==========

// 时间转换工具函数
time_t to_timet(const STimeDay* stTimeDay);

// ========== 顶层初始化接口 ==========

/**
 * 回放模块顶层初始化函数
 * 供主程序启动时调用，其他模块不得调用
 *
 * @return true 初始化成功, false 初始化失败
 */
bool playback_module_initialize();

/**
 * 回放模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void playback_module_cleanup();

/**
 * 检查回放模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool playback_module_is_initialized();

// ========== 向后兼容的全局函数声明 ==========

/**
 * 向后兼容的回放控制函数
 * 这些函数保持原有接口不变，内部调用新的SimplePlaybackManager
 */

// 停止回放
void stop_playback();

// 回放测试函数
int mdisk_playback_test(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed = 1, const CHAR* disk_top = nullptr);

// 定位回放到指定时间
bool seek_playback_to_time(UINT32 time);

// 设置回放速度
void set_playback_speed(INT32 speed);

// 获取当前回放时间
UINT32 get_playback_current_time();

// 暂停/恢复回放
void pause_playback(BOOL pause);

// 录像数据库查询
BOOL mdisk_rec_db_query(LPCSTR date, INT32 chn, std::multiset<T_PLAYBACK_FILE_TIME>& file_list);

// 时间转换工具函数
time_t mdisk_convert_utc(LPCSTR date, LPCSTR time_str);

// 字符串处理工具函数
void remove_colons(const char *src, char *dest);

// 通过文件导出
BOOL export_record_to_avi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path = NULL);

// 通过时间导出 (连续)
BOOL export_record_to_avi_by_time(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path = NULL);

// 是否正在导出
BOOL is_export_running();

// 取消导出
VOID cancel_export();



