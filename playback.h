#pragma once

#include <string>
#include <vector>
#include <pthread.h>
#include <atomic>
#include "low_delay_common.h"
#include "vs_comm_def.h"
#include "record.h"
#include "Thread/Lock.hpp"
#include <set>
#include <map>

// 定义录像数据库索引文件名
#define REC_DB_INDEX "index.db"

// 定义最大解码通道数
#define MAX_DECODE_CHANNELS 5

#define	MAX_PLAYBACK_SIZE	5*1024*1024


// 多磁盘回放上下文
typedef struct {
    FILE *fidx;             // 索引
    FILE *fvideo;           // 视频
    
    UINT32   fseq_day;      // 文件位于数据库第几条
    STimeDay stTimeDay;     // 日期(记录时只有日期有效)    
    T_INDEX_ENTRY index_entry;  // 录像文件索引
    T_FRAME_HEADER hdr;         // 录像头
    INT32    frame_size;        // 帧大小    
    UINT8    is_video;          // 是否视频帧
    time_t   f_st_time;         // 文件开始时间
    time_t   f_end_time;        // 文件结束时间
    UINT8    f_fps;             // 帧率
    UINT8    with_tz;           // 文件时间附加时区
    UINT32   videoWidth;        // 视频宽度
    UINT32   videoHeight;       // 视频高度
    CHAR     disk_top[256];     // 磁盘路径
} T_MDISK_PLAY_CONTEXT;

// 多磁盘回放句柄
typedef T_MDISK_PLAY_CONTEXT* T_MDISK_PLAY_HADLE;

// 回放控制结构体
typedef struct {
    INT32    enable;            // 使能
    UINT8    Run;               // 运行状态
    pthread_t play_thrd_id;     // 播放线程
    INT32    play_chn;          // 回放哪一路流
    INT32    vdec_chn;          // 解码通道
    INT32	 vo_chn;			// 播放通道
    time_t   start_time;        // 开始时间
    time_t   end_time;          // 结束时间
    time_t   seek_time;         // 定位时间
    INT32    play_speed;        // 播放速度
    INT32    play_pause;        // 暂停
    CHAR     *video_buff;       // 视频数据缓存
    CHAR     *audio_buff;       // 音频数据缓存
    INT8     read_frame;        // 读取帧标志
    INT8     checkKeyFrame;     // 检查关键帧标志
    T_MDISK_PLAY_HADLE mdisk_h; // 多磁盘回放句柄
    CHAR     disk_top[256];     // 磁盘全路径
} T_MDISK_PLAYBACK_CTRL;

// 导出状态结构体
typedef struct {
    BOOL running;               // 是否正在导出
    BOOL cancel_flag;           // 取消标志
    BOOL by_time;               // 是否按时间导出
    float progress;             // 导出进度 (0.0 - 1.0)
    INT32 chn;                  // 通道号
    UINT32 start_time;          // 开始时间 (按时间导出时使用)
    UINT32 end_time;            // 结束时间 (按时间导出时使用)
    CHAR source_file[512];      // 源文件路径 (按文件导出时使用)
    CHAR target_file[512];      // 目标文件路径
    CHAR disk_path[256];        // 磁盘路径
    pthread_t export_thread;    // 导出线程
} T_EXPORT_STATUS;

// 回放文件时间结构体
typedef struct {
    time_t st_time;             // 开始时间
    time_t end_time;            // 结束时间
    CHAR disk_top[128];			// 当前录像文件的磁盘路径
    CHAR file_crop_name[128];	// 用于显示的文件名
    CHAR file_path[512];        // 文件路径
    UINT32 file_size;           // 文件大小
    UINT8 event_type;           // 事件类型
} T_PLAYBACK_FILE_TIME;

// 回放会话类 - 抽象每次回放会话
class PlaybackSession {
public:
    PlaybackSession(int session_id);
    ~PlaybackSession();

    // 会话控制
    bool startSession(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top);
    void stopSession();
    void pauseSession(BOOL pause);
    void setSpeed(INT32 speed);
    bool seekToTime(UINT32 time);
    UINT32 getCurrentTime();

    // 状态查询
    bool isRunning() const { return m_ctrl.Run != 0; }
    bool isEnabled() const { return m_ctrl.enable != 0; }
    int getSessionId() const { return m_sessionId; }
    int getPlayChannel() const { return m_ctrl.play_chn; }

private:
    int m_sessionId;                    // 会话ID
    T_MDISK_PLAYBACK_CTRL m_ctrl;       // 回放控制结构
    mutable TCSLock m_sessionMutex;     // 会话锁

    // 内部方法
    static void* playbackThreadFunc(void* arg);
    void playbackThreadWork();
};

// 导出管理类 - 单例模式
class ExportManager {
public:
    // 获取单例实例
    static ExportManager* getInstance();
    static void destroyInstance();

    // 导出功能
    bool exportRecordToAvi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path = NULL);
    bool exportRecordToAviByTime(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path = NULL);
    bool isExportRunning() const;
    void cancelExport();
    float getExportProgress() const;

private:
    ExportManager();
    ~ExportManager();

    // 禁用拷贝构造和赋值操作
    ExportManager(const ExportManager&) = delete;
    ExportManager& operator=(const ExportManager&) = delete;

    // 导出线程函数
    static void* exportThreadByFile(void* arg);
    static void* exportThreadByTime(void* arg);
    void exportThreadByFileWork();
    void exportThreadByTimeWork();

    // 成员变量
    T_EXPORT_STATUS m_exportStatus;     // 导出状态
    mutable TCSLock m_exportMutex;      // 导出锁

    // 单例相关
    static ExportManager* m_instance;
    static TCSLock m_instanceMutex;
};

// 回放管理类 - 单例模式，管理多个回放会话
class PlaybackManager {
public:
    // 获取单例实例
    static PlaybackManager* getInstance();
    static void destroyInstance();

    // 模块初始化
    bool initializeModule();
    void cleanupModule();
    bool isModuleInitialized() const { return m_moduleInitialized; }

    // 会话管理
    PlaybackSession* createSession();
    void destroySession(PlaybackSession* session);
    PlaybackSession* getSession(int sessionId);
    void destroyAllSessions();

    // mdisk 相关功能
    T_MDISK_PLAY_HADLE mdiskPlayOpen(T_MDISK_PLAY_HADLE h, INT32 play_chn, STimeDay *stTimeDay, UINT8 with_tz, const CHAR* disk_path = NULL);
    INT32 mdiskPlayReadFrame(T_MDISK_PLAY_HADLE h, INT32 play_chn, CHAR *buf, UINT32 buf_len, UINT8 auto_next_file);
    BOOL mdiskPlaySeek(T_MDISK_PLAY_HADLE handle, UINT32 time);
    VOID mdiskPlayClose(T_MDISK_PLAY_HADLE handle, UINT8 free_p);
    BOOL mdiskRecDbQuery(LPCSTR date, INT32 chn, std::multiset<T_PLAYBACK_FILE_TIME>& file_list);

    // 回放控制（兼容接口）
    INT32 startPlayback(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top = NULL);
    VOID stopPlayback();
    VOID pausePlayback(BOOL pause);
    VOID setPlaybackSpeed(INT32 speed);
    BOOL seekPlaybackToTime(UINT32 time);
    UINT32 getPlaybackCurrentTime();

    // 测试功能
    INT32 mdiskPlaybackTest(INT32 chn, UINT32 start_time, UINT32 end_time, UINT8 event_type = 0, const CHAR* disk_top = NULL);

    // 事件类型过滤
    void setEventTypeFilter(UINT8 event_type) { m_eventTypeFilter = event_type; }
    UINT8 getEventTypeFilter() const { return m_eventTypeFilter; }

    // 状态查询
    bool hasActiveSession() const;

    // 内部方法（公开以便兼容性接口调用）
    void initPlaybackCtrl();

private:
    PlaybackManager();
    ~PlaybackManager();

    // 禁用拷贝构造和赋值操作
    PlaybackManager(const PlaybackManager&) = delete;
    PlaybackManager& operator=(const PlaybackManager&) = delete;
    int findAvailableSessionId();

    // 成员变量
    std::map<int, PlaybackSession*> m_sessions;  // 会话映射
    int m_currentPlaybackChannel;                // 当前回放通道
    UINT8 m_eventTypeFilter;                     // 事件类型过滤器
    bool m_moduleInitialized;                    // 模块初始化标志
    mutable TCSLock m_managerMutex;              // 管理器锁

    // 单例相关
    static PlaybackManager* m_instance;
    static TCSLock m_instanceMutex;
};


// 回放接口
void init_playback_ctrl();
INT32 start_playback(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top = NULL);
VOID stop_playback();
VOID pause_playback(BOOL pause);
VOID set_playback_speed(INT32 speed);
BOOL seek_playback_to_time(UINT32 time);
UINT32 get_playback_current_time();
    
// 回放测试函数
INT32 mdisk_playback_test(INT32 chn, UINT32 start_time, UINT32 end_time, UINT8 event_type = 0, const CHAR* disk_top = NULL);
    
// 多磁盘回放API
T_MDISK_PLAY_HADLE mdisk_play_open(T_MDISK_PLAY_HADLE h, INT32 play_chn, STimeDay *stTimeDay, UINT8 with_tz, const CHAR* disk_path = NULL);
INT32 mdisk_play_read_frame(T_MDISK_PLAY_HADLE h, INT32 play_chn, CHAR *buf, UINT32 buf_len, UINT8 auto_next_file);
BOOL mdisk_play_seek(T_MDISK_PLAY_HADLE handle, UINT32 time);
BOOL mdisk_play_decode_frame(INT32 decode_chn, UINT8* frame_data, UINT32 frame_size, UINT32 timestamp, INT32 codec);
VOID mdisk_play_close(T_MDISK_PLAY_HADLE handle, UINT8 free_p);


// 将 20250626 和 "HH:MM:SS" 转换为 UTC 时间戳
time_t mdisk_convert_utc(LPCSTR date, LPCSTR time_str);
// 去掉时间中间的冒号
void remove_colons(const char *src, char *dest) ;


// 播放时间结构体
typedef struct {
    char file_crop_name[32];   // 短文件名 (HH:MM:SS格式)
    char disk_top[256];        // 磁盘路径
    time_t st_time;            // 开始时间 (time_t格式)
    time_t end_time;           // 结束时间 (time_t格式)
} T_PLAYBACK_FILE_TIME_OLD;  // 重命名以避免冲突，这个定义可能是旧的


// 录像导出功能 - 注意：这些类型已经在上面定义过了，这里是重复定义


extern T_REC_WORK	   g_rec_work;

// 回放函数声明
BOOL mdisk_rec_db_query(LPCSTR date, INT32 chn, std::multiset<T_PLAYBACK_FILE_TIME>& file_list);

// 通过文件导出
BOOL export_record_to_avi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path = NULL);

// 通过时间导出 (连续)
BOOL export_record_to_avi_by_time(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path = NULL);

// 是否正在导出
BOOL is_export_running();

// 取消导出
VOID cancel_export();

// 录像文件信息结构
typedef struct {
    std::string file_path;  // 文件路径
    time_t start_time;      // 开始时间
    time_t end_time;        // 结束时间
    int record_type;        // 录像类型
    uint64_t file_size;     // 文件大小
} RecordFileInfo;



// 时间转换工具函数
time_t to_timet(const STimeDay* stTimeDay);

// ========== 顶层初始化接口 ==========

/**
 * 回放模块顶层初始化函数
 * 供主程序启动时调用，其他模块不得调用
 *
 * @return true 初始化成功, false 初始化失败
 */
bool playback_module_initialize();

/**
 * 回放模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void playback_module_cleanup();

/**
 * 检查回放模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool playback_module_is_initialized();


