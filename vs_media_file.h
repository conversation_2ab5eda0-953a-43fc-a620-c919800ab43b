/***************************************************************************
** 版权所有: 成都云创天下 Copyright (c) 2015-2020  ********************     
** 文件名称: E:\Work_Proj\Linux\8216\edit\ipcam\mstar_ipc\storage\vs_media_file.h
** 文件标识: 
** 内容摘要: 
** 当前版本: v1.0
** 作     者: 徐辉
** 完成日期: 2021年11月25日
** 修改记录: 
** 修改记录: 
** 修改日期: 
** 版 本 号: 
** 修 改 人: 
** 修改内容: 
***************************************************************************/
#ifndef VS_MEDIA_FILE_H
#define VS_MEDIA_FILE_H

#include "vs_head.h"
#include "vs_msg_def.h"
#include "include/list.h"
#include "BrdSDKDef.h"
#include <string>
#include <iostream>
#include <fstream>


/**
 * 在开始录像或是停止录像时回调, 回调中处理的事情不要太耗时.否则容易影响录像
 * @param  flag 	1表示开始录像; 0表示停止录像; <0表示停止录像但是文件有问题
 * @param  filename	文件名
 */
typedef VOID (*fn_record_callback)(INT32 flag, LPSTR filename);


// 磁盘信息
typedef struct 
{
	INT32	status;			// 状态: 1为正常,0未格式化,-1为损坏, -2未知
	UINT64	total_msize;	// 总空间
	UINT64	free_msize;		// 可用空间
	INT32	format_prog;	// 格式化进度
}T_DISK_INFO;

// 播放上下文
typedef struct
{
	HANDLE		av_file;	// av文件
	UINT32		fseq_day;	// 文件位于数据库第几条

	STimeDay	stTimeDay;	// 日期(记录时只有日期有效)
	
	FRAMEINFO_t a_frame_info;	// 音频帧信息
	INT32		aid;			// 音频id
	HANDLE 		audBsf;			// 音频扩展结构  
	
	FRAMEINFO_t v_frame_info;	// 视频帧信息	
	INT32		vid;			// 视频id
	INT32		frame_size;		// 帧大小
	HANDLE 		vidBsf;			// 视频扩展结构 
	
	UINT8		is_video;		// 是否视频帧
	time_t		f_st_time;		// 文件开始时间
	UINT8		f_fps;			// 帧率

	UINT8		with_tz;		// 文件时间附加时区

	UINT32		v_trackid;		// 录像文件视频trackid
	UINT32		a_trackid;		// 录像文件音频trackid
	UINT32		v_sampleid;		// 录像文件视频当前帧编号
	UINT32		a_sampleid;		// 录像文件音频当前帧编号
	
	UINT8		**vps;
	UINT32		*vpsSize;
	UINT8		**sps;
	UINT32		*spsSize;
	UINT8		**pps;
	UINT32		*ppsSize;
	
    UINT32 videoWidth;
    UINT32 videoHeight;
}T_PLAY_CONTEXT;
typedef T_PLAY_CONTEXT *T_PLAY_HADLE;


// 录像类型
typedef enum 
{
	RT_NORMAL	= 0,
	RT_MOTION_DETECT,
	RT_PERSON_DETECT,
	RT_SOUND_DETECT,
	RT_ALL_TYPE,
	
}VS_RECORD_TYPE;

// 一条记录
typedef struct 
{
	time_t	st_time;		// 开始时间
	time_t	end_time;		// 结束时间
	CHAR	file_name[80];	// 录像文件名
	CHAR	idx_name[80];	// 索引文件名
	INT8	rec_type;		// 录像类型	
	INT8	change_st;		// 修改开始时间
	INT8	reserve[2];		// 保留
	INT8	flag;			// 标志:
#define OR_FLAG_NONE	0		// 无效
#define OR_FLAG_REC		1		// 录制中
#define OR_FLAG_OK		2		// 打包OK
#define OR_FLAG_DEL		3		// 删除
}T_ONE_REC;

typedef struct 
{
	t_vs_list	flist;
	T_ONE_REC	one_rec;	
}T_ONE_REC_ITEM;

// 录像工作表
typedef struct 
{
	UINT8		enable_sche;		// 开启计划表标记[用于计划表]
	UINT8		start;				// 开启录像[在open与close之间变化]
	fn_record_callback	event_cb;	// 录像通知函数
	T_ONE_REC	one_rec;			// 录像记录
	struct {
		ULONG 	abs_end_time;		// 结束绝对时间
	} rec_info;
	UINT8		wait_key_frame;		// 等待关键帧标记
	HANDLE		avh;				// av文件句柄

	INT32		vid;				// 视频id
	HANDLE 		vidBsf;				// 视频扩展结构 
	
	INT32		aid;				// 音频id
	HANDLE		audBsf; 			// 音频扩展结构
	struct {
		INT32	videoCodec;				// 视频codec
		INT32	videoFps;				// 视频fps
		INT32 	videoWidth;				// 视频宽
		INT32 	videoHeight;			// 视频高
		INT32 	videoKbps;				// 位率
		UINT32	videoNums;				// 视频帧数量
	};	
	
	struct {
		UINT8	audioEnable;			// 音频开启标志
		INT32	audioCodec;				// 音频codec
		INT32	audioChannel;			// 音频通道数
		INT32 	audioSampleRate;		// 音频样本采样频率	
		INT32 	audioBitsPerSample;		// 音频每样本位数	
		UINT64	audioPts;				// 音频时间戳
	};
	
	TCSLock		avi_lock;			// 文件锁
	UINT8       chn;  // 记录属于哪个sensor的录像
}T_REC_WORK;

// 录像目录格式
#define	REC_DIR_FMT			"%04d%02d%02d"
#define	QfLeftRange(val, low, hight)	(((val) >= (low)) && ((val) < (hight)))

// 时间解析
#define LTIME_2_PARSE(l, pa)	{\
	time_t	lt = l;\
	struct tm my_tm;\
	struct tm *pTm = localtime_r(&lt, &my_tm);\
	pa.year  	= pTm->tm_year + 1900;\
	pa.month 	= pTm->tm_mon + 1;\
	pa.day   	= pTm->tm_mday;\
	pa.wday		= pTm->tm_wday;\
	pa.hour		= pTm->tm_hour;\
	pa.minute	= pTm->tm_min;\
	pa.second	= pTm->tm_sec;\
}

// zram 参数设置
class VmSettings 
{
private:
    static constexpr const char* swappinessPath = "/proc/sys/vm/swappiness";
    static constexpr const char* boostFactorPath = "/proc/sys/vm/watermark_boost_factor";
    static constexpr const char* zramPath = "/dev/zram0";

    int originalSwappiness = -1;
    int originalBoostFactor = -1;
    bool zramExists = false;
    bool restoreOnExit = true;

    int readValue(const std::string &path);
    void writeValue(const std::string &path, int value);
    bool checkZramExists();

public:
    explicit VmSettings(bool restore = true);
    ~VmSettings();
};



/**
 * mf初始化
 * @return   成功=OK；失败=FAIL
 */
INT32 mfile_init();

/**
 * mfile反初始化
 */
VOID mfile_uninit();

/**
 * 切换录像流
 * @param  stream_chn 	流通道号
 */
VOID mfile_switch_stream(UINT8	stream_chn);

/**
 * 设置录像勾子函数
 * @param  record_cb 	录像勾子函数, 不使用时可以置NULL
 */
VOID mfile_set_record_hook(fn_record_callback record_cb);

/** 
 * 发送消息
 * @param  eventType  事件类型
 * @param  delay_sec_close  持续多少秒后关闭，=0使用内置策略
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_push_msg(ALARM_EVENT_TYPE eventType, INT32 delay_sec_close = 0);

/**
 * 打开写文件
 * @param  rec_type  录像类型 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_open(VS_RECORD_TYPE rec_type);


/**
 * 关闭写文件
 */
VOID mfile_close(T_REC_WORK* p_rec_work=NULL);
void mfile_close_all();

/**
 * 写视频
 * @return   成功返回1,失败返回0
 */
UINT8 mfile_write_video(BYTE *buff , UINT32 size, UINT8 key_frame, UINT64 timestamp);


/**
 * 写音频
 * @return   成功返回1,失败返回0
 */
UINT8 mfile_write_audio(BYTE *buff , UINT32 size, UINT64 timestamp);



/**
 * 判断TF卡是否存在 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_tf_exist();

/**
 * 判断TF卡是否就绪(必须分区正常,mount成功) 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_tf_ready();


/**
 * 得到TF卡磁盘信息 
 * @return	 成功返回1,失败返回0
 */
UINT32 mfile_get_tf_status(T_DISK_INFO *diskinfo);

/**
 * 格式化tf卡
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_tf_format();

/**
 * 回放查询
 * @param  avSocket tutk套接字
 * @param  buf      请求缓冲
 * @return          无
 */
typedef INT32 (*fn_send_msg)(LPVOID avSocket, UINT32 cmd, CONST CHAR *buff, INT32 buff_size);
VOID mfile_tutk_query(LPVOID	avSocket,       CHAR *buf, fn_send_msg fn_send);
VOID mfile_tutk_query_file(LPVOID	avSocket,       CHAR *buf, fn_send_msg fn_send);

/**
 * 回放查询
 * @param  rec_type 	录像类型
 * @param  query_beg    开始时间
 * @param  query_end    结束时间
 * @param  f_list    	录像文件列表
 * @param  desc    		倒序
 * @return          记录条数 
 */
INT32 mfile_query_file_ex(int snridx, INT32 rec_type, time_t		query_beg, time_t query_end, t_vs_list *f_list, UINT8 desc = FALSE);
INT32 mfile_query_file(INT32 rec_type, time_t		query_beg, time_t query_end, t_vs_list *f_list, UINT8 desc = FALSE);

time_t to_timet(STimeDay *p);



/**
 * 回放日期区间列表查询
 * @param  avSocket tutk套接字
 * @param  buf      请求缓冲
 * @return          无
 */
VOID mfile_tutk_query_list(LPVOID	avSocket,       CHAR *buf, fn_send_msg fn_send);

/**
 * 打开文件, 根据stTimeDay定位文件
 * @param  old_h     old_h=NULL, 则分配句柄；old_h!=NULL,则根据定位决定是否关闭再重建；此句柄要返回给下面的三个函数使用
 * @param  stTimeDay 需要定位的时间
 * @return           成功返回非NULL
 */
T_PLAY_HADLE mfile_play_open_ex(int snridx, T_PLAY_HADLE old_h, STimeDay *stTimeDay, UINT8 with_tz = TRUE);
T_PLAY_HADLE mfile_play_open(T_PLAY_HADLE old_h, STimeDay *stTimeDay, UINT8 with_tz = TRUE);

/** 
 * 打开指定文件
 * @param  filename  [文件名]
 * @return           成功返回非NULL;失败返回NULL
 */
T_PLAY_HADLE mfile_play_file(LPCTSTR filename);


/**
 * 读数据
 * @param  h        上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  buf      缓冲
 * @param  buf_len  缓冲大小
 * @param  auto_next_file  自动读取下一个文件 
 * @return          返回读取到的数据大小；失败返回0
 */
INT32 mfile_play_read_frame(T_PLAY_HADLE h, CHAR *buf, UINT32 buf_len, UINT8 auto_next_file);

/**
 * 定位文件
 * @param  h        	上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  offset_sec   偏移多少秒(相当文件开始)
 * @return          	成功返回TRUE；失败返回FALSE
 */
UINT8 mfile_play_seek(T_PLAY_HADLE h, UINT32 offset_sec);


/**
 * 关闭句柄并释放资源
 * @param  h 		上下文句柄
 * @param  free_p 	释放资源标记
 */
VOID mfile_play_close(T_PLAY_HADLE h, UINT8 free_p);

/**
 * 录像纯音频文件
 * @param  filename  文件名 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_pcm_record(LPCSTR filename);

/** 
 * 写入纯音频数据
 * @param  buff  缓冲
 * @param  size  大小
 */
VOID mfile_pcm_audio(UINT8 *buff, UINT32 size);

/**
 * 停止纯音频记录
 */
VOID mfile_pcm_stop();

/** 
 * 重建指定路径下的数据文件
 * @param  path  路径
 */
void rebuild_dbfile(LPCSTR dir_path);

/**
 * 插入一条记录
 * @param  one_rec 记录
 * @return         成功为1；失败为0
 */
UINT8 rec_db_add(T_ONE_REC *one_rec);


/** 
 * 将数据库文件改为新的名称
 * @param  filename  新文件名
 */
VOID mfile_new_dbfile(LPCSTR filename);

/**
 * 从指定路径path中搜索大于more_than_date的目录,写入result中
 *  	但是不会搜索今天的
 * 成功返回>0的日期,失败返回0
 */
INT32 get_early_dir(LPCSTR path, INT32 *more_than_date);

enum {
	/* NFS */
	MFILE_FS_NFS = 0,
	/* samba3/cifs */
	MFILE_FS_SMB3,	
};
/** 
* 是否支持某种文件系统
* @param  fs_type  系统系统
* @return	成功返回1,失败返回0
*/
UINT8 mfile_is_support_fs(INT32 fs_type);

/** 
 * 得到录像目录
 * @return  根据当前录像状态返回路径
 */
LPCSTR get_record_path();

/** 
 * nas载入配置
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_nas_reload();

/** 
 * nas是否就绪
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_nas_ready();


#endif
