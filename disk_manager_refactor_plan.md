# DiskManager 重构方案

## 1. 职责分离重构

### 原始问题
- DiskManager类5360行，职责过重
- 单一类处理挂载、清理、切换、监控等所有功能
- 函数平均长度80-120行，维护困难

### 重构方案：拆分为5个专职类

```cpp
// 1. 磁盘发现和挂载管理
class DiskMountManager {
public:
    bool mountDisk(const std::string& device);
    bool unmountDisk(const std::string& path);
    std::vector<std::string> getAvailableDisks();
    bool isDiskMounted(const std::string& path);
    
private:
    std::map<std::string, MountInfo> m_mountedDisks;
    TCSLock m_mountLock;
};

// 2. 磁盘空间监控
class DiskSpaceMonitor {
public:
    double getDiskUsage(const std::string& path);
    bool isDiskFull(const std::string& path);
    void startMonitoring();
    void stopMonitoring();
    
private:
    void monitorThread();
    std::map<std::string, DiskSpaceInfo> m_spaceCache;
    std::atomic<bool> m_monitoring;
};

// 3. 磁盘清理管理
class DiskCleanupManager {
public:
    bool cleanupSpace(const std::string& path, uint64_t bytesToFree);
    void requestAsyncCleanup(int channelId, uint64_t bytesToFree);
    
private:
    void cleanupThread();
    std::queue<CleanupRequest> m_cleanupQueue;
    TCSLock m_queueLock;
};

// 4. 磁盘切换管理
class DiskSwitchManager {
public:
    bool switchToNextDisk();
    std::string findNextAvailableDisk(const std::string& currentDisk);
    
private:
    std::vector<std::string> m_diskPaths;
    size_t m_activeDiskIndex;
};

// 5. 统一协调器（简化的DiskManager）
class DiskManager {
public:
    static DiskManager* getInstance();
    
    // 简化的公共接口
    std::string getActiveDiskPath();
    bool hasAvailableDisk();
    bool ensureSpaceAvailable(uint64_t requiredSpace);
    
private:
    DiskMountManager m_mountManager;
    DiskSpaceMonitor m_spaceMonitor;
    DiskCleanupManager m_cleanupManager;
    DiskSwitchManager m_switchManager;
};
```

## 2. 接口简化

### 原始问题
- 接口参数过多，调用复杂
- 返回值类型不一致
- 错误处理分散

### 重构方案：统一简化接口

```cpp
// 统一的结果类型
enum class DiskResult {
    SUCCESS,
    DISK_NOT_FOUND,
    DISK_FULL,
    PERMISSION_DENIED,
    OPERATION_FAILED
};

// 统一的磁盘操作接口
class DiskOperations {
public:
    // 简化的磁盘操作接口
    DiskResult mount(const std::string& device);
    DiskResult unmount(const std::string& path);
    DiskResult cleanup(uint64_t bytesToFree);
    DiskResult switchDisk();
    
    // 简化的查询接口
    bool isAvailable(const std::string& path);
    double getUsagePercent(const std::string& path);
    uint64_t getFreeSpace(const std::string& path);
};
```

## 3. 状态管理简化

### 原始问题
- 复杂的条件判断嵌套6-8层
- 状态转换逻辑分散
- 难以理解和维护

### 重构方案：状态模式

```cpp
// 磁盘状态枚举
enum class DiskState {
    UNMOUNTED,
    MOUNTED,
    FULL,
    CLEANING,
    SWITCHING,
    ERROR
};

// 状态处理基类
class DiskStateHandler {
public:
    virtual ~DiskStateHandler() = default;
    virtual DiskResult handleMount() = 0;
    virtual DiskResult handleWrite() = 0;
    virtual DiskResult handleCleanup() = 0;
    virtual DiskState getNextState() = 0;
};

// 具体状态实现
class MountedState : public DiskStateHandler {
public:
    DiskResult handleMount() override { return DiskResult::SUCCESS; }
    DiskResult handleWrite() override { /* 检查空间并写入 */ }
    DiskResult handleCleanup() override { /* 执行清理 */ }
    DiskState getNextState() override { /* 根据条件返回下一状态 */ }
};

// 状态管理器
class DiskStateManager {
public:
    DiskResult executeOperation(DiskOperation op);
    DiskState getCurrentState() const { return m_currentState; }
    
private:
    DiskState m_currentState;
    std::map<DiskState, std::unique_ptr<DiskStateHandler>> m_handlers;
};
```

## 4. 重构实施步骤

### 第一阶段：接口提取（1-2天）
1. 提取DiskManager的公共接口
2. 创建DiskOperations统一接口类
3. 保持原有实现，仅添加新接口层

### 第二阶段：职责分离（3-5天）
1. 创建5个专职类的基本框架
2. 逐步迁移功能到对应的专职类
3. 保持向后兼容性

### 第三阶段：状态管理重构（2-3天）
1. 实现状态模式
2. 简化条件判断逻辑
3. 统一错误处理

### 第四阶段：测试和优化（2-3天）
1. 单元测试覆盖
2. 集成测试验证
3. 性能对比测试

## 5. 预期效果

### 代码量减少
- 原始：5360行 → 重构后：约2000行（减少60%）
- 平均函数长度：120行 → 30行（减少75%）

### 复杂度降低
- 条件嵌套：6-8层 → 2-3层
- 类职责：1个大类 → 5个专职类
- 接口参数：平均6个 → 平均2个

### 可维护性提升
- 职责清晰，易于理解
- 接口简化，易于使用
- 状态管理清晰，易于扩展
