#include "playback.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <time.h>
#include <errno.h>
#include <iostream>

#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "disk_manager.h"
#include <dirent.h>
#include <sys/stat.h>

// ========== 辅助函数声明 ==========
static BOOL scanRecordFiles(const char* recordDir, INT32 chn, time_t start_time, time_t end_time,
                           std::multiset<T_PLAYBACK_FILE_TIME>& file_list);
static BOOL isVideoFile(const char* filename);
static time_t parseFileTimestamp(const char* filename);

// ========== KISS重构：简化的回放管理器实现 ==========

// SimplePlaybackManager 单例相关
SimplePlaybackManager* SimplePlaybackManager::m_instance = nullptr;
TCSLock SimplePlaybackManager::m_instanceMutex;

// 模块初始化标志
static bool g_moduleInitialized = false;
static TCSLock g_moduleMutex;

SimplePlaybackManager::SimplePlaybackManager() {
    m_controller = std::make_unique<PlaybackController>();
    LogI("创建简化回放管理器");
}

SimplePlaybackManager::~SimplePlaybackManager() {
    LogI("销毁简化回放管理器");
}

SimplePlaybackManager* SimplePlaybackManager::getInstance() {
    if (m_instance == nullptr) {
        ScopedLocker lock(m_instanceMutex);
        if (m_instance == nullptr) {
            m_instance = new SimplePlaybackManager();
        }
    }
    return m_instance;
}

void SimplePlaybackManager::destroyInstance() {
    ScopedLocker lock(m_instanceMutex);
    if (m_instance) {
        delete m_instance;
        m_instance = nullptr;
        LogI("销毁简化回放管理器实例");
    }
}

PlaybackResult SimplePlaybackManager::startPlayback(const PlaybackParams& params) {
    if (!params.isValid()) {
        LogE("回放参数无效: chn=%d, start=%u, end=%u", params.channelId, params.startTime, params.endTime);
        return PlaybackResult::INVALID_PARAMS;
    }
    
    if (!m_controller) {
        return PlaybackResult::RESOURCE_ERROR;
    }
    
    bool success = m_controller->startPlayback(params.channelId, params.startTime, params.endTime);
    return success ? PlaybackResult::SUCCESS : PlaybackResult::ALREADY_RUNNING;
}

PlaybackResult SimplePlaybackManager::stopPlayback() {
    if (!m_controller) {
        return PlaybackResult::RESOURCE_ERROR;
    }
    
    m_controller->stopPlayback();
    return PlaybackResult::SUCCESS;
}

PlaybackResult SimplePlaybackManager::pausePlayback(bool pause) {
    if (!m_controller) {
        return PlaybackResult::RESOURCE_ERROR;
    }
    
    m_controller->pausePlayback(pause);
    return PlaybackResult::SUCCESS;
}

PlaybackResult SimplePlaybackManager::seekToTime(uint32_t timestamp) {
    if (!m_controller) {
        return PlaybackResult::RESOURCE_ERROR;
    }
    
    // TODO: 通过控制器实现定位
    LogI("定位到时间: %u", timestamp);
    return PlaybackResult::SUCCESS;
}

PlaybackState SimplePlaybackManager::getState() const {
    if (m_controller) {
        return m_controller->getState();
    }
    return PlaybackState::STOPPED;
}

uint32_t SimplePlaybackManager::getCurrentTime() const {
    // TODO: 通过控制器获取当前时间
    return 0;
}

PlaybackResult SimplePlaybackManager::setSpeed(int speed) {
    if (!m_controller) {
        return PlaybackResult::RESOURCE_ERROR;
    }

    m_controller->setSpeed(speed);
    return PlaybackResult::SUCCESS;
}

// ========== 多磁盘回放接口实现（KISS简化版本）==========

T_MDISK_PLAY_HADLE SimplePlaybackManager::mdiskPlayOpen(const CHAR* disk_top, INT32 chn, const STimeDay* stTimeDay, INT32 speed, const CHAR* disk_path) 
{
	CHAR	path[200];	
	FILE	*pf;	
	time_t	ltime;
	UINT8	read_next;

	// 确认是定位还是打开下一个记录
	if (stTimeDay != NULL) {
		mdiskPlayClose(h, TRUE);
		h = NULL;
		read_next = FALSE;
	} else {
		QF_ASSERT(h != NULL);
		stTimeDay = &h->stTimeDay;
		read_next = TRUE;
	}
	QF_ASSERT(stTimeDay != NULL);
	ltime = to_timet(stTimeDay);	

	// 使用传入的磁盘路径
	std::string diskPath;
	if (disk_path && *disk_path) {
		diskPath = disk_path;
		// 确保路径末尾有斜杠
		if (!diskPath.empty() && diskPath.back() != '/') {
			diskPath += '/';
		}
	} else if (h && h->disk_top[0]) {
		// 如果有句柄并且句柄中有磁盘路径，则使用句柄中的路径
		diskPath = h->disk_top;
	} else {
		// 如果上述都没有，则使用活动磁盘
		DiskManager* diskManager = DiskManager::getInstance();
		if (diskManager) {
			diskPath = diskManager->getActiveDiskPath();
		} else {
			// 没有磁盘管理器时，不使用默认路径
			diskPath = "";
			LogE("无法获取 DiskManager 单例实例，无法获取磁盘路径");
		}
	}
	
	// 检查磁盘是否已挂载
	bool isMounted = true;
	DiskManager* diskManager = DiskManager::getInstance();
	if (diskManager) {
		isMounted = diskManager->isMounted(diskPath);
	}
	
	if (!isMounted) {
		LogE("磁盘未挂载，无法打开回放: %s", diskPath.c_str());
		return NULL;
	}

	// 获取数据库文件路径
	// 修改路径格式为 磁盘路径/年月日/设备ID/index.db
	snprintf(path, sizeof(path), "%s%04d%02d%02d/%d/index.db", 
			 diskPath.c_str(), 
			 stTimeDay->year, stTimeDay->month, stTimeDay->day, 
			 play_chn);
	LogI("使用磁盘路径: %s, 数据库路径: %s", diskPath.c_str(), path);
	
	LogI("open index: %s, ltime:%lld, %04d%02d%02d %02d:%02d:%02d", path,
		ltime, stTimeDay->year, stTimeDay->month, stTimeDay->day, stTimeDay->hour, stTimeDay->minute, stTimeDay->second);
	
	mfile_new_dbfile(path);

	LogE("path = %s", path);
	
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{		
		T_ONE_REC	one_rec;
		UINT32		fseq = 0;
		LPSTR		tmp_str;
		INT32		tmp_value;

		// 直接读某个文件
		if (read_next) {
			fseq = h->fseq_day + 1;
			if (fseek(pf, fseq*sizeof(one_rec), SEEK_SET) != 0) {
				LOGE("%s(), fseek error, errno=%d", __FUNCTION__, errno);
				fclose(pf);
				return NULL;
			}
		}
		
		for ( ;fread(&one_rec, sizeof(one_rec), 1, pf) EQU 1; fseq++)
		{		
			//LOGI("one_rec, begtime:%ld, endtime:%ld, flag:%d, rectype:%d, filename:%s, idx_name:%s", 
			//	 one_rec.st_time, one_rec.end_time, one_rec.flag, one_rec.rec_type, one_rec.file_name, one_rec.idx_name);
			
			// 判断是否正常
			if (one_rec.flag != OR_FLAG_OK) {
				LogE("未录制完成, one_rec.flag = %d, file = %s", one_rec.flag, one_rec.file_name);
				continue;
			}

			UINT8	find_rec = FALSE;
			if (QfLeftRange(ltime, one_rec.st_time, one_rec.end_time)) 
			{
				T_ONE_REC	next_one_rec;
				
				find_rec = TRUE;
				do {
					if (fread(&next_one_rec, sizeof(next_one_rec), 1, pf) EQU 1) {
						if (QfLeftRange(ltime, next_one_rec.st_time, next_one_rec.end_time)) {
							memcpy(&one_rec, &next_one_rec, sizeof(next_one_rec));
							break;
						}
					}
					fseek(pf, -1*(LONG)sizeof(one_rec), SEEK_CUR);
				} while (FALSE);
			}
			
			// 找到合适的记录了, 或是找到大于此记录的文件
			if (find_rec
				|| one_rec.st_time > ltime
				|| read_next) {

				LogI("找到记录了 开始播放 %s, %s", one_rec.file_name, one_rec.idx_name);

				// 不复用句柄
				if (!read_next) {
					h = (T_MDISK_PLAY_HADLE)malloc(sizeof(T_MDISK_PLAY_CONTEXT));
					bzero(h, sizeof(T_MDISK_PLAY_CONTEXT));
					memcpy(&h->stTimeDay, stTimeDay, sizeof(h->stTimeDay));
					// 保存磁盘路径到句柄中，方便后续使用
					strncpy(h->disk_top, diskPath.c_str(), sizeof(h->disk_top)-1);
				}		
				
				h->fidx = fopen(one_rec.idx_name, "rb");
				if (!h->fidx) {
					LogE("fidx open Err!! errno = %d", errno);
					return NULL;
				}
				
				h->fvideo = fopen(one_rec.file_name, "rb");
				if (!h->fvideo) {
					LogE("fvideo open Err!! errno = %d", errno);
					return NULL;
				}

				h->fseq_day = fseq; 		// 文件序号
				h->f_st_time = one_rec.st_time;
				h->with_tz = with_tz;

				break;
			}
			else {
				// LOGI("%s(), not in range, seektime:%ld", __FUNCTION__, ltime);
				;
			}
			
		}
	} 
	else {
		LOGE("%s() error, file not exist: %s", __FUNCTION__, path);
	}

	CloseFileAndNull(pf);

	if (h) 
		return h;
		
	return NULL;
}



BOOL SimplePlaybackManager::mdiskPlayClose(T_MDISK_PLAY_HADLE handle, BOOL force) {
    if (!handle) {
        LogW("回放句柄为空");
        return FALSE;
    }

    T_MDISK_PLAY_CONTEXT* context = static_cast<T_MDISK_PLAY_CONTEXT*>(handle);

    LogI("关闭多磁盘回放: chn=%d, force=%s", context->chn, force ? "是" : "否");

    // 关闭文件
    if (context->video_fd) {
        fclose(context->video_fd);
        context->video_fd = nullptr;
    }

    if (context->index_fd) {
        fclose(context->index_fd);
        context->index_fd = nullptr;
    }

    // 释放上下文
    delete context;

    LogI("多磁盘回放关闭完成");
    return TRUE;
}

INT32 SimplePlaybackManager::mdiskPlayReadFrame(T_MDISK_PLAY_HADLE handle, INT32 chn, CHAR* frame_buf, UINT32 buf_size, T_FRAME_HEADER* frame_header) 
{
	LONG	read_len;
	INT32	is_key_frame;
	LONG	frame_num;
	INT32	fnRet;

	UINT32 read_size = 0;
	UINT32 last_timestamp = 0;
	INT32  time_interval = 40; // 默认为25fps (1000/25)
	time_t current_time = 0;   // 当前播放时间
	
	T_INDEX_ENTRY index_entry;		 // 录像文件索引
	T_FRAME_HEADER hdr; 			 // 录像头
	
	// 检查参数
	if (h == NULL || h->fidx == NULL || h->fvideo == NULL) {
		LogE("参数错误: 无效的播放句柄");
		return 0;
	}

	// 保存当前文件位置，以便在出错时恢复
	long idx_pos = ftell(h->fidx);
	long video_pos = ftell(h->fvideo);

	// 读取索引条目
	if (fread(&index_entry, sizeof(index_entry), 1, h->fidx) != 1) {
		// 如果读取索引失败，可能是文件结束或索引损坏
		LogW("读取索引条目失败，可能已到文件末尾");
		
		if (!auto_next_file) {
			return 0; // 不自动切换到下一个文件
		}
		
		// 尝试打开下一个文件
		mdiskPlayClose(h, FALSE);

		// 直接使用当前句柄中的磁盘路径
		if (mdiskPlayOpen(h, play_chn, NULL, h->with_tz, h->disk_top) == NULL) {
			LogE("无法打开下一个文件");
			return 0;
		}

		// 递归调用读取下一个文件的第一帧
		return mdiskPlayReadFrame(h, play_chn, buf, buf_len, auto_next_file);
	}
	
	// 更新当前播放时间
	current_time = index_entry.utc_time;
	
	// 确保视频文件指针正确定位到索引指定的位置
	if (ftell(h->fvideo) != index_entry.file_offset) {
		LogW("视频文件指针不同步，重新定位: 当前=%ld, 应为=%u", 
			 ftell(h->fvideo), index_entry.file_offset);
		fseek(h->fvideo, index_entry.file_offset, SEEK_SET);
	}
	
	// 读取帧头
	if (fread(&hdr, sizeof(hdr), 1, h->fvideo) != 1) {
		LogE("读取帧头失败: offset=%u", index_entry.file_offset);
		
		// 尝试恢复文件同步
		fseek(h->fidx, idx_pos, SEEK_SET);
		fseek(h->fvideo, video_pos, SEEK_SET);
		
		return 0;
	}
	
	// 验证帧头和索引条目的一致性
	if (hdr.size != index_entry.frame_size || 
		hdr.frameType != index_entry.frame_type) {
		LogE("帧头与索引条目不一致: 帧大小=%u/%u, 帧类型=%d/%d", 
			 hdr.size, index_entry.frame_size,
			 hdr.frameType, index_entry.frame_type);
		
		// 尝试恢复文件同步 - 更强大的恢复机制
		LogW("尝试重新同步索引和视频文件...");
		
		// 先回退到原始位置
		fseek(h->fidx, idx_pos, SEEK_SET);
		fseek(h->fvideo, video_pos, SEEK_SET);
		
		// 如果是I帧不一致，可能是严重错误，尝试重新定位到下一个I帧
		if (index_entry.frame_type == IPC_FRAME_FLAG_IFRAME) {
			// 寻找下一个I帧
			LogW("寻找下一个I帧...");
			while (fread(&index_entry, sizeof(index_entry), 1, h->fidx) == 1) {
				if (index_entry.frame_type == IPC_FRAME_FLAG_IFRAME) {
					fseek(h->fvideo, index_entry.file_offset, SEEK_SET);
					// 回退一个索引条目，以便下次读取
					fseek(h->fidx, -((long)sizeof(index_entry)), SEEK_CUR);
					LogW("找到下一个I帧，已重新定位");
					return 0; // 返回0，让调用者重新尝试读取
				}
			}
		}
		
		return 0;
	}
	
	// 检查缓冲区大小是否足够
	if (index_entry.frame_size > buf_len) {
		LogE("缓冲区不够，需要 %u 字节，但只有 %u 字节", index_entry.frame_size, buf_len);
		
		// 尝试恢复文件同步
		fseek(h->fidx, idx_pos, SEEK_SET);
		fseek(h->fvideo, video_pos, SEEK_SET);
		
		return 0;
	}

	// 读取帧数据
	read_size = fread(buf, 1, index_entry.frame_size, h->fvideo);
	if (read_size != index_entry.frame_size) {
		LogE("帧数据读取不完整: 期望=%u, 实际=%d", index_entry.frame_size, read_size);
		
		// 尝试恢复文件同步
		fseek(h->fidx, idx_pos, SEEK_SET);
		fseek(h->fvideo, video_pos, SEEK_SET);
		
		return 0;
	}

	// 更新上下文信息
	memcpy(&h->hdr, &hdr, sizeof(hdr));
	memcpy(&h->index_entry, &index_entry, sizeof(index_entry));
	
	// 返回读取的数据大小
	return read_size;
}


BOOL SimplePlaybackManager::mdiskPlaySeek(T_MDISK_PLAY_HADLE handle, const STimeDay* stTimeDay) 
{
    if (!handle || !stTimeDay) {
        LogE("定位参数无效");
        return FALSE;
    }

    T_MDISK_PLAY_CONTEXT* context = static_cast<T_MDISK_PLAY_CONTEXT*>(handle);
    time_t seek_time = to_timet(stTimeDay);

    LogI("多磁盘回放定位: chn=%d, time=%ld", context->chn, seek_time);

    // 简化的定位逻辑 - 重新定位到文件开始
    if (context->video_fd) {
        if (fseek(context->video_fd, 0, SEEK_SET) != 0) {
            LogE("文件定位失败");
            return FALSE;
        }
    }

    context->f_st_time = seek_time;
    LogI("多磁盘回放定位完成");
    return TRUE;
}

// 向后兼容接口实现
int SimplePlaybackManager::mdisk_playback_test(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top)
{
    PlaybackParams params(chn, start_time, end_time, disk_top ? disk_top : "", speed);
    PlaybackResult result = startPlayback(params);
    
    // 转换为原有的返回值格式
    switch (result) {
        case PlaybackResult::SUCCESS: return 0;
        case PlaybackResult::INVALID_PARAMS: return -1;
        case PlaybackResult::FILE_NOT_FOUND: return -2;
        case PlaybackResult::ALREADY_RUNNING: return -3;
        default: return -1;
    }
}

// ========== 顶层初始化接口实现 ==========

bool playback_module_initialize() {
    ScopedLocker lock(g_moduleMutex);
    if (g_moduleInitialized) {
        LogW("回放模块已经初始化");
        return true;
    }

    LogI("初始化回放模块");

    // 初始化简化回放管理器
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (!manager) {
        LogE("创建回放管理器失败");
        return false;
    }

    g_moduleInitialized = true;
    LogI("回放模块初始化成功");
    return true;
}

void playback_module_cleanup() {
    ScopedLocker lock(g_moduleMutex);
    if (!g_moduleInitialized) {
        return;
    }

    LogI("清理回放模块");

    // 销毁简化回放管理器
    SimplePlaybackManager::destroyInstance();

    g_moduleInitialized = false;
    LogI("回放模块清理完成");
}

bool playback_module_is_initialized() {
    ScopedLocker lock(g_moduleMutex);
    return g_moduleInitialized;
}

// ========== 工具函数实现 ==========

time_t to_timet(const STimeDay* stTimeDay) {
    if (!stTimeDay) {
        return 0;
    }
    
    struct tm tm_time;
    memset(&tm_time, 0, sizeof(tm_time));
    
    tm_time.tm_year = stTimeDay->year - 1900;
    tm_time.tm_mon = stTimeDay->month - 1;
    tm_time.tm_mday = stTimeDay->day;
    tm_time.tm_hour = stTimeDay->hour;
    tm_time.tm_min = stTimeDay->minute;
    tm_time.tm_sec = stTimeDay->second;
    
    return mktime(&tm_time);
}

// ========== KISS重构：状态机实现 ==========

// 状态转换表定义
const std::map<std::pair<PlaybackState, PlaybackEvent>, PlaybackState> PlaybackStateMachine::s_transitions = {
    // 从STOPPED状态的转换
    {{PlaybackState::STOPPED, PlaybackEvent::START}, PlaybackState::PLAYING},

    // 从PLAYING状态的转换
    {{PlaybackState::PLAYING, PlaybackEvent::STOP}, PlaybackState::STOPPED},
    {{PlaybackState::PLAYING, PlaybackEvent::PAUSE}, PlaybackState::PAUSED},
    {{PlaybackState::PLAYING, PlaybackEvent::SEEK}, PlaybackState::SEEKING},
    {{PlaybackState::PLAYING, PlaybackEvent::ERROR_OCCURRED}, PlaybackState::ERROR},
    {{PlaybackState::PLAYING, PlaybackEvent::FILE_END}, PlaybackState::STOPPED},

    // 从PAUSED状态的转换
    {{PlaybackState::PAUSED, PlaybackEvent::STOP}, PlaybackState::STOPPED},
    {{PlaybackState::PAUSED, PlaybackEvent::RESUME}, PlaybackState::PLAYING},
    {{PlaybackState::PAUSED, PlaybackEvent::SEEK}, PlaybackState::SEEKING},
    {{PlaybackState::PAUSED, PlaybackEvent::ERROR_OCCURRED}, PlaybackState::ERROR},

    // 从SEEKING状态的转换
    {{PlaybackState::SEEKING, PlaybackEvent::STOP}, PlaybackState::STOPPED},
    {{PlaybackState::SEEKING, PlaybackEvent::START}, PlaybackState::PLAYING},
    {{PlaybackState::SEEKING, PlaybackEvent::ERROR_OCCURRED}, PlaybackState::ERROR},

    // 从ERROR状态的转换
    {{PlaybackState::ERROR, PlaybackEvent::STOP}, PlaybackState::STOPPED},
    {{PlaybackState::ERROR, PlaybackEvent::START}, PlaybackState::PLAYING}
};

bool PlaybackStateMachine::canTransition(PlaybackState from, PlaybackState to) const {
    // 检查是否存在有效的转换路径
    for (const auto& transition : s_transitions) {
        if (transition.first.first == from && transition.second == to) {
            return true;
        }
    }
    return false;
}

PlaybackState PlaybackStateMachine::transition(PlaybackEvent event) {
    auto key = std::make_pair(m_currentState, event);
    auto it = s_transitions.find(key);

    if (it != s_transitions.end()) {
        PlaybackState oldState = m_currentState;
        m_currentState = it->second;
        LogD("状态转换: %d -> %d (event=%d)",
             static_cast<int>(oldState), static_cast<int>(m_currentState), static_cast<int>(event));
        return m_currentState;
    } else {
        LogW("无效的状态转换: state=%d, event=%d",
             static_cast<int>(m_currentState), static_cast<int>(event));
        return m_currentState;
    }
}

// ========== KISS重构：简化的回放会话类 ==========

class SimplePlaybackSession {
public:
    SimplePlaybackSession()
        : m_resource(std::make_unique<PlaybackResource>())
        , m_currentTime(0) {
        LogI("创建简化回放会话");
    }

    ~SimplePlaybackSession() {
        stop();
        LogI("销毁简化回放会话");
    }

    PlaybackResult start(const PlaybackParams& params) {
        if (m_stateMachine.getCurrentState() != PlaybackState::STOPPED) {
            return PlaybackResult::ALREADY_RUNNING;
        }

        if (!params.isValid()) {
            return PlaybackResult::INVALID_PARAMS;
        }

        // 保存参数
        m_params = params;

        // 尝试打开文件
        std::string videoPath = generateVideoPath(params);
        std::string indexPath = generateIndexPath(params);

        m_videoFile = std::make_unique<FileHandle>(videoPath, "rb");
        m_indexFile = std::make_unique<FileHandle>(indexPath, "rb");

        if (!m_videoFile->isValid() || !m_indexFile->isValid()) {
            LogE("打开回放文件失败: video=%s, index=%s", videoPath.c_str(), indexPath.c_str());
            m_stateMachine.transition(PlaybackEvent::ERROR_OCCURRED);
            return PlaybackResult::FILE_NOT_FOUND;
        }

        m_stateMachine.transition(PlaybackEvent::START);
        m_currentTime = params.startTime;

        LogI("回放会话启动成功: chn=%d, start=%u, end=%u",
             params.channelId, params.startTime, params.endTime);

        return PlaybackResult::SUCCESS;
    }

    void stop() {
        if (m_stateMachine.getCurrentState() == PlaybackState::STOPPED) {
            return;
        }

        m_stateMachine.transition(PlaybackEvent::STOP);
        m_videoFile.reset();  // 自动关闭文件
        m_indexFile.reset();  // 自动关闭文件

        LogI("回放会话停止");
    }

    void pause(bool pause) {
        PlaybackState currentState = m_stateMachine.getCurrentState();
        if (currentState == PlaybackState::STOPPED) {
            return;
        }

        if (pause && currentState == PlaybackState::PLAYING) {
            m_stateMachine.transition(PlaybackEvent::PAUSE);
        } else if (!pause && currentState == PlaybackState::PAUSED) {
            m_stateMachine.transition(PlaybackEvent::RESUME);
        }

        LogI("回放会话暂停状态: %s", pause ? "暂停" : "继续");
    }

    PlaybackResult seekToTime(uint32_t timestamp) {
        if (m_stateMachine.getCurrentState() == PlaybackState::STOPPED) {
            return PlaybackResult::NOT_RUNNING;
        }

        if (timestamp < m_params.startTime || timestamp > m_params.endTime) {
            LogE("定位时间超出范围: %u not in [%u, %u]",
                 timestamp, m_params.startTime, m_params.endTime);
            m_stateMachine.transition(PlaybackEvent::ERROR_OCCURRED);
            return PlaybackResult::SEEK_FAILED;
        }

        // 执行定位
        m_stateMachine.transition(PlaybackEvent::SEEK);
        m_currentTime = timestamp;
        LogI("回放定位到时间: %u", timestamp);

        // 定位完成，恢复到播放状态
        m_stateMachine.transition(PlaybackEvent::START);

        return PlaybackResult::SUCCESS;
    }

    PlaybackState getState() const { return m_stateMachine.getCurrentState(); }
    uint32_t getCurrentTime() const { return m_currentTime; }

    void setSpeed(int speed) {
        if (m_stateMachine.getCurrentState() == PlaybackState::STOPPED) {
            LogW("回放已停止，无法设置速度");
            return;
        }

        // 验证速度范围
        if (speed < -3 || speed > 3) {
            LogE("播放速度超出范围: %d", speed);
            return;
        }

        m_playbackSpeed = speed;
        LogI("回放会话设置速度: %d", speed);

        // TODO: 在实际的帧读取循环中使用这个速度值
        // 正数表示正向播放，负数表示反向播放
        // 0=暂停, 1=正常速度, 2=2倍速, -1=反向正常速度等
    }

    int getSpeed() const { return m_playbackSpeed; }

private:
    PlaybackParams m_params;
    uint32_t m_currentTime;
    int m_playbackSpeed{1};  // 播放速度，默认为1倍速
    PlaybackStateMachine m_stateMachine;

    // RAII资源管理
    std::unique_ptr<PlaybackResource> m_resource;
    std::unique_ptr<FileHandle> m_videoFile;
    std::unique_ptr<FileHandle> m_indexFile;

    std::string generateVideoPath(const PlaybackParams& params) {
        char path[512];
        snprintf(path, sizeof(path), "%s/chn%02d/%u.h264",
                params.diskPath.c_str(), params.channelId, params.startTime);
        return std::string(path);
    }

    std::string generateIndexPath(const PlaybackParams& params) {
        char path[512];
        snprintf(path, sizeof(path), "%s/chn%02d/%u.idx",
                params.diskPath.c_str(), params.channelId, params.startTime);
        return std::string(path);
    }
};

// ========== KISS重构：专职类实现 ==========

// 1. 回放会话管理器实现
PlaybackSessionManager::PlaybackSessionManager() {
    LogI("创建回放会话管理器");
}

PlaybackSessionManager::~PlaybackSessionManager() {
    ScopedLocker lock(m_sessionLock);
    m_sessions.clear();
    LogI("销毁回放会话管理器");
}

int PlaybackSessionManager::createSession() {
    ScopedLocker lock(m_sessionLock);
    int sessionId = m_nextSessionId++;
    m_sessions[sessionId] = std::make_unique<SimplePlaybackSession>();
    LogI("创建回放会话: sessionId=%d", sessionId);
    return sessionId;
}

bool PlaybackSessionManager::destroySession(int sessionId) {
    ScopedLocker lock(m_sessionLock);
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end()) {
        m_sessions.erase(it);
        LogI("销毁回放会话: sessionId=%d", sessionId);
        return true;
    }
    LogW("会话不存在: sessionId=%d", sessionId);
    return false;
}

SimplePlaybackSession* PlaybackSessionManager::getSession(int sessionId) {
    ScopedLocker lock(m_sessionLock);
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end()) {
        return it->second.get();
    }
    return nullptr;
}

std::vector<int> PlaybackSessionManager::getActiveSessions() const {
    ScopedLocker lock(m_sessionLock);
    std::vector<int> sessions;
    for (const auto& pair : m_sessions) {
        sessions.push_back(pair.first);
    }
    return sessions;
}

// 2. 媒体文件读取器实现
MediaFileReader::MediaFileReader() : m_isOpen(false) {
    LogI("创建媒体文件读取器");
    memset(&m_currentIndex, 0, sizeof(m_currentIndex));
}

MediaFileReader::~MediaFileReader() {
    closeFile();
    LogI("销毁媒体文件读取器");
}

bool MediaFileReader::openFile(const std::string& filePath, uint32_t startTime) {
    if (m_isOpen) {
        closeFile();
    }

    std::string videoPath = filePath + ".h264";
    std::string indexPath = filePath + ".idx";

    m_videoFile = std::make_unique<FileHandle>(videoPath, "rb");
    m_indexFile = std::make_unique<FileHandle>(indexPath, "rb");

    if (!m_videoFile->isValid() || !m_indexFile->isValid()) {
        LogE("打开媒体文件失败: video=%s, index=%s", videoPath.c_str(), indexPath.c_str());
        return false;
    }

    m_isOpen = true;
    LogI("打开媒体文件成功: %s", filePath.c_str());
    return true;
}

int MediaFileReader::readFrame(char* buffer, uint32_t bufferSize, T_FRAME_HEADER* header) {
    if (!m_isOpen || !m_videoFile->isValid()) {
        return -1;
    }

    // 简化的帧读取逻辑
    // TODO: 实现实际的帧读取
    return 0;
}

bool MediaFileReader::seekToTime(uint32_t timestamp) {
    if (!m_isOpen || !m_indexFile->isValid()) {
        return false;
    }

    // 简化的定位逻辑
    // TODO: 实现实际的时间定位
    LogI("定位到时间: %u", timestamp);
    return true;
}

void MediaFileReader::closeFile() {
    if (m_isOpen) {
        m_videoFile.reset();
        m_indexFile.reset();
        m_isOpen = false;
        LogI("关闭媒体文件");
    }
}

bool MediaFileReader::isFileOpen() const {
    return m_isOpen;
}

// 3. 回放控制器实现
PlaybackController::PlaybackController() {
    LogI("创建回放控制器");
}

PlaybackController::~PlaybackController() {
    stopPlayback();
    LogI("销毁回放控制器");
}

bool PlaybackController::startPlayback(int channelId, uint32_t startTime, uint32_t endTime) {
    if (m_isPlaying) {
        LogW("回放已在运行中");
        return false;
    }

    // 创建会话
    m_currentSessionId = m_sessionManager.createSession();
    SimplePlaybackSession* session = m_sessionManager.getSession(m_currentSessionId);

    if (!session) {
        LogE("创建回放会话失败");
        return false;
    }

    // 启动回放
    PlaybackParams params(channelId, startTime, endTime);
    PlaybackResult result = session->start(params);

    if (result == PlaybackResult::SUCCESS) {
        m_isPlaying = true;
        m_stateMachine.transition(PlaybackEvent::START);
        LogI("回放控制器启动成功: chn=%d", channelId);
        return true;
    } else {
        m_sessionManager.destroySession(m_currentSessionId);
        m_currentSessionId = -1;
        LogE("回放启动失败: result=%d", static_cast<int>(result));
        return false;
    }
}

void PlaybackController::stopPlayback() {
    if (!m_isPlaying) {
        return;
    }

    if (m_currentSessionId >= 0) {
        SimplePlaybackSession* session = m_sessionManager.getSession(m_currentSessionId);
        if (session) {
            session->stop();
        }
        m_sessionManager.destroySession(m_currentSessionId);
        m_currentSessionId = -1;
    }

    m_isPlaying = false;
    m_stateMachine.transition(PlaybackEvent::STOP);
    LogI("回放控制器停止");
}

void PlaybackController::pausePlayback(bool pause) {
    if (!m_isPlaying || m_currentSessionId < 0) {
        return;
    }

    SimplePlaybackSession* session = m_sessionManager.getSession(m_currentSessionId);
    if (session) {
        session->pause(pause);
        m_stateMachine.transition(pause ? PlaybackEvent::PAUSE : PlaybackEvent::RESUME);
    }
}

void PlaybackController::setSpeed(int speed) {
    if (!m_isPlaying || m_currentSessionId < 0) {
        LogW("回放未运行，无法设置速度");
        return;
    }

    // 验证速度范围 (通常支持 1/8x 到 8x)
    if (speed < -3 || speed > 3) {
        LogE("播放速度超出范围: %d (支持范围: -3 到 3)", speed);
        return;
    }

    SimplePlaybackSession* session = m_sessionManager.getSession(m_currentSessionId);
    if (session) {
        // 设置会话的播放速度
        session->setSpeed(speed);
        LogI("设置播放速度成功: %d", speed);
    } else {
        LogE("无法获取回放会话，设置速度失败");
    }
}

PlaybackState PlaybackController::getState() const {
    return m_stateMachine.getCurrentState();
}



// ========== ExportManager 单例实现 ==========

ExportManager* ExportManager::getInstance()
{
    if (m_instance == nullptr) {
        m_instanceMutex.Enter();
        if (m_instance == nullptr) {
            m_instance = new ExportManager();
            LogI("ExportManager 单例实例创建成功");
        }
        m_instanceMutex.Leave();
    }
    return m_instance;
}

void ExportManager::destroyInstance()
{
    m_instanceMutex.Enter();
    if (m_instance != nullptr) {
        LogI("销毁 ExportManager 单例实例");
        delete m_instance;
        m_instance = nullptr;
    }
    m_instanceMutex.Leave();
}

ExportManager::ExportManager()
{
    LogI("ExportManager 构造函数");

    // 初始化导出状态
    memset(&m_exportStatus, 0, sizeof(m_exportStatus));
    m_exportStatus.running = FALSE;
    m_exportStatus.cancel_flag = FALSE;
    m_exportStatus.progress = 0.0f;
}

ExportManager::~ExportManager()
{
    LogI("ExportManager 析构函数");

    // 确保导出已停止
    cancelExport();
}

bool ExportManager::exportRecordToAvi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path)
{
    ScopedLocker lock(m_exportMutex);

    if (m_exportStatus.running) {
        LogE("导出正在进行中，无法启动新的导出任务");
        return false;
    }

    LogI("开始按文件导出录像: chn=%d, source=%s, target=%s, disk_path=%s",
         chn, source_file, target_file, disk_path ? disk_path : "默认");

    // 设置导出参数
    m_exportStatus.running = TRUE;
    m_exportStatus.cancel_flag = FALSE;
    m_exportStatus.progress = 0.0f;
    m_exportStatus.chn = chn;
    m_exportStatus.by_time = false;

    s_strcpy(m_exportStatus.source_file, source_file);
    s_strcpy(m_exportStatus.target_file, target_file);

    if (disk_path && *disk_path) {
        s_strcpy(m_exportStatus.disk_path, disk_path);
    } else {
        // 使用活动磁盘路径
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            std::string activePath = diskManager->getActiveDiskPath();
            s_strcpy(m_exportStatus.disk_path, activePath.c_str());
        } else {
            s_strcpy(m_exportStatus.disk_path, "");
        }
    }

    // 创建导出线程
    int ret = pthread_create(&m_exportStatus.export_thread, NULL, exportThreadByFile, this);
    if (ret != 0) {
        LogE("创建导出线程失败: ret=%d", ret);
        m_exportStatus.running = FALSE;
        return false;
    }

    LogI("导出任务已启动");
    return true;
}

bool ExportManager::exportRecordToAviByTime(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path)
{
    ScopedLocker lock(m_exportMutex);

    if (m_exportStatus.running) {
        LogE("导出正在进行中，无法启动新的导出任务");
        return false;
    }

    LogI("开始按时间导出录像: chn=%d, start=%u, end=%u, target=%s, disk_path=%s",
         chn, start_time, end_time, target_file, disk_path ? disk_path : "默认");

    // 设置导出参数
    m_exportStatus.running = TRUE;
    m_exportStatus.cancel_flag = FALSE;
    m_exportStatus.progress = 0.0f;
    m_exportStatus.chn = chn;
    m_exportStatus.start_time = start_time;
    m_exportStatus.end_time = end_time;
    m_exportStatus.by_time = true;

    s_strcpy(m_exportStatus.target_file, target_file);

    if (disk_path && *disk_path) {
        s_strcpy(m_exportStatus.disk_path, disk_path);
    } else {
        // 使用活动磁盘路径
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            std::string activePath = diskManager->getActiveDiskPath();
            s_strcpy(m_exportStatus.disk_path, activePath.c_str());
        } else {
            s_strcpy(m_exportStatus.disk_path, "");
        }
    }

    // 创建导出线程
    int ret = pthread_create(&m_exportStatus.export_thread, NULL, exportThreadByTime, this);
    if (ret != 0) {
        LogE("创建导出线程失败: ret=%d", ret);
        m_exportStatus.running = FALSE;
        return false;
    }

    LogI("导出任务已启动");
    return true;
}

bool ExportManager::isExportRunning() const
{
    ScopedLocker lock(m_exportMutex);
    return m_exportStatus.running;
}

void ExportManager::cancelExport()
{
    ScopedLocker lock(m_exportMutex);

    if (!m_exportStatus.running) {
        return;
    }

    LogI("取消导出任务");

    // 设置取消标志
    m_exportStatus.cancel_flag = TRUE;

    // 等待导出线程结束
    if (m_exportStatus.export_thread != 0) {
        pthread_join(m_exportStatus.export_thread, NULL);
        m_exportStatus.export_thread = 0;
    }

    m_exportStatus.running = FALSE;
    LogI("导出任务已取消");
}

float ExportManager::getExportProgress() const
{
    ScopedLocker lock(m_exportMutex);
    return m_exportStatus.progress;
}

void* ExportManager::exportThreadByFile(void* arg)
{
    ExportManager* manager = (ExportManager*)arg;
    manager->exportThreadByFileWork();
    return NULL;
}

void* ExportManager::exportThreadByTime(void* arg)
{
    ExportManager* manager = (ExportManager*)arg;
    manager->exportThreadByTimeWork();
    return NULL;
}

void ExportManager::exportThreadByFileWork()
{
    // 获取导出参数（需要在锁保护下）
    INT32 chn;
    char source_file[256], target_file[256];
    bool cancel_flag;

    {
        ScopedLocker lock(m_exportMutex);
        chn = m_exportStatus.chn;
        strcpy(source_file, m_exportStatus.source_file);
        strcpy(target_file, m_exportStatus.target_file);
        cancel_flag = m_exportStatus.cancel_flag;
    }

    LogI("开始导出录像: 通道=%d, 源文件=%s, 目标文件=%s", chn, source_file, target_file);

    // 检查源文件是否存在
    FILE* source_fp = fopen(source_file, "rb");
    if (!source_fp) {
        LogE("源文件不存在: %s", source_file);
        {
            ScopedLocker lock(m_exportMutex);
            m_exportStatus.running = FALSE;
        }
        return;
    }

    // 获取源文件大小
    fseek(source_fp, 0, SEEK_END);
    long file_size = ftell(source_fp);
    fseek(source_fp, 0, SEEK_SET);

    // 创建目标文件
    FILE* target_fp = fopen(target_file, "wb");
    if (!target_fp) {
        LogE("无法创建目标文件: %s", target_file);
        fclose(source_fp);
        {
            ScopedLocker lock(m_exportMutex);
            m_exportStatus.running = FALSE;
        }
        return;
    }

    // 复制文件内容
    char buffer[64 * 1024]; // 64KB 缓冲区
    long copied_bytes = 0;
    size_t bytes_read;

    while ((bytes_read = fread(buffer, 1, sizeof(buffer), source_fp)) > 0) {
        // 检查取消标志
        {
            ScopedLocker lock(m_exportMutex);
            if (m_exportStatus.cancel_flag) {
                break;
            }
        }

        if (fwrite(buffer, 1, bytes_read, target_fp) != bytes_read) {
            LogE("写入目标文件失败: %s", target_file);
            break;
        }

        copied_bytes += bytes_read;

        // 更新进度
        {
            ScopedLocker lock(m_exportMutex);
            m_exportStatus.progress = (float)copied_bytes / file_size;
        }

        // 每复制1MB数据休眠一下，避免占用过多CPU
        if (copied_bytes % (1024 * 1024) == 0) {
            usleep(1000);
        }
    }

    fclose(source_fp);
    fclose(target_fp);

    // 检查最终状态并更新
    {
        ScopedLocker lock(m_exportMutex);
        if (!m_exportStatus.cancel_flag && copied_bytes == file_size) {
            m_exportStatus.progress = 1.0f;
            LogI("录像导出完成: 目标文件=%s, 大小=%ld字节", target_file, copied_bytes);
        } else {
            LogW("导出被取消或失败: %s", target_file);
            // 删除不完整的目标文件
            remove(target_file);
        }
        m_exportStatus.running = FALSE;
    }
}

void ExportManager::exportThreadByTimeWork()
{
    // 获取导出参数（需要在锁保护下）
    INT32 chn;
    UINT32 start_time, end_time;
    char target_file[256], disk_path[256];

    {
        ScopedLocker lock(m_exportMutex);
        chn = m_exportStatus.chn;
        start_time = m_exportStatus.start_time;
        end_time = m_exportStatus.end_time;
        strcpy(target_file, m_exportStatus.target_file);
        strcpy(disk_path, m_exportStatus.disk_path);
    }

    LogI("开始按时间导出录像: 通道=%d, 开始时间=%u, 结束时间=%u, 目标文件=%s",
         chn, start_time, end_time, target_file);

    // 获取PlaybackManager实例用于文件操作
    SimplePlaybackManager* playbackManager = SimplePlaybackManager::getInstance();
    if (!playbackManager) {
        LogE("无法获取 PlaybackManager 实例");
        m_exportStatus.running = FALSE;
        return;
    }

    // 转换开始时间为STimeDay结构
    STimeDay stTimeDay;
    struct tm start_tm;
    time_t start_time_t = m_exportStatus.start_time;
    localtime_r(&start_time_t, &start_tm);

    stTimeDay.year = start_tm.tm_year + 1900;
    stTimeDay.month = start_tm.tm_mon + 1;
    stTimeDay.day = start_tm.tm_mday;
    stTimeDay.hour = start_tm.tm_hour;
    stTimeDay.minute = start_tm.tm_min;
    stTimeDay.second = start_tm.tm_sec;

    // 打开mdisk回放句柄
    T_MDISK_PLAY_HADLE mdisk_h = playbackManager->mdiskPlayOpen(NULL, m_exportStatus.chn, &stTimeDay, 1, m_exportStatus.disk_path);
    if (!mdisk_h) {
        LogE("无法打开mdisk句柄进行导出: chn=%d, disk_path=%s", m_exportStatus.chn, m_exportStatus.disk_path);
        m_exportStatus.running = FALSE;
        return;
    }

    // 创建目标文件
    FILE* target_fp = fopen(m_exportStatus.target_file, "wb");
    if (!target_fp) {
        LogE("无法创建目标文件: %s", m_exportStatus.target_file);
        playbackManager->mdiskPlayClose(mdisk_h, TRUE);
        m_exportStatus.running = FALSE;
        return;
    }

    // 导出循环
    UINT32 total_duration = m_exportStatus.end_time - m_exportStatus.start_time;
    UINT32 current_time = m_exportStatus.start_time;
    char frame_buffer[MAX_PLAYBACK_SIZE];
    long total_exported = 0;

    while (current_time < m_exportStatus.end_time && !m_exportStatus.cancel_flag) {
        // 读取帧数据
        int frame_size = playbackManager->mdiskPlayReadFrame(mdisk_h, m_exportStatus.chn,
                                                            frame_buffer, MAX_PLAYBACK_SIZE, TRUE);

        if (frame_size > 0) {
            // 写入目标文件
            if (fwrite(frame_buffer, 1, frame_size, target_fp) != (size_t)frame_size) {
                LogE("写入目标文件失败: %s", m_exportStatus.target_file);
                break;
            }

            total_exported += frame_size;

            // 更新当前时间（从mdisk句柄获取）
            if (mdisk_h->index_entry.utc_time > current_time) {
                current_time = mdisk_h->index_entry.utc_time;
            } else {
                current_time++; // 防止时间不前进
            }

            // 更新进度
            if (total_duration > 0) {
                m_exportStatus.progress = (float)(current_time - m_exportStatus.start_time) / total_duration;
            }
        } else if (frame_size == 0) {
            // 文件结束
            LogI("到达文件末尾，导出结束");
            break;
        } else {
            // 读取错误
            LogE("读取帧数据失败: ret=%d", frame_size);
            break;
        }

        // 每导出1MB数据休眠一下
        if (total_exported % (1024 * 1024) == 0) {
            usleep(1000);
        }
    }

    fclose(target_fp);
    playbackManager->mdiskPlayClose(mdisk_h, TRUE);

    if (!m_exportStatus.cancel_flag && current_time >= m_exportStatus.end_time) {
        m_exportStatus.progress = 1.0f;
        LogI("按时间录像导出完成: 目标文件=%s, 导出大小=%ld字节", m_exportStatus.target_file, total_exported);
    } else {
        LogW("导出被取消或失败: %s", m_exportStatus.target_file);
        // 删除不完整的目标文件
        remove(m_exportStatus.target_file);
    }

    m_exportStatus.running = FALSE;
}




// ========== 向后兼容接口实现 ==========

// 停止回放
void stop_playback() {
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (manager) {
        manager->stopPlayback();
        LogI("停止回放 (兼容接口)");
    } else {
        LogE("无法获取回放管理器实例");
    }
}

// 回放测试函数
int mdisk_playback_test(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top) {
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取回放管理器实例");
        return -1;
    }
    LogI("回放测试 (兼容接口): chn=%d, start=%u, end=%u, speed=%d", chn, start_time, end_time, speed);
    return manager->mdisk_playback_test(chn, start_time, end_time, speed, disk_top);
}

// 定位回放到指定时间
bool seek_playback_to_time(UINT32 time) {
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取回放管理器实例");
        return false;
    }

    PlaybackResult result = manager->seekToTime(time);
    bool success = (result == PlaybackResult::SUCCESS);
    LogI("定位回放时间 (兼容接口): time=%u, result=%s", time, success ? "成功" : "失败");
    return success;
}

// 设置回放速度
void set_playback_speed(INT32 speed) {
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取回放管理器实例");
        return;
    }

    PlaybackResult result = manager->setSpeed(speed);
    if (result == PlaybackResult::SUCCESS) {
        LogI("设置回放速度 (兼容接口): speed=%d", speed);
    } else {
        LogE("设置回放速度失败: result=%d", static_cast<int>(result));
    }
}

// 获取当前回放时间
UINT32 get_playback_current_time() {
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取回放管理器实例");
        return 0;
    }

    uint32_t currentTime = manager->getCurrentTime();
    LogD("获取当前回放时间 (兼容接口): time=%u", currentTime);
    return currentTime;
}

// 暂停/恢复回放
void pause_playback(BOOL pause) {
    SimplePlaybackManager* manager = SimplePlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取回放管理器实例");
        return;
    }

    PlaybackResult result = manager->pausePlayback(pause ? true : false);
    LogI("暂停回放 (兼容接口): pause=%s, result=%d", pause ? "暂停" : "继续", static_cast<int>(result));
}

// ========== 录像数据库查询实现 ==========

BOOL mdisk_rec_db_query(LPCSTR date, INT32 chn, std::multiset<T_PLAYBACK_FILE_TIME>& file_list) {
    if (!date || chn < 0) {
        LogE("录像查询参数无效: date=%s, chn=%d", date ? date : "NULL", chn);
        return FALSE;
    }

    LogI("查询录像文件: date=%s, chn=%d", date, chn);

    // 清空结果列表
    file_list.clear();

    // 获取磁盘管理器实例
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager) {
        LogE("无法获取磁盘管理器实例");
        return FALSE;
    }

    // 获取活动磁盘路径
    std::string activeDiskPath = diskManager->getActiveDiskPath();
    if (activeDiskPath.empty()) {
        LogW("无活动磁盘路径");
        return FALSE;
    }

    // 构建录像文件目录路径
    char recordDir[512];
    snprintf(recordDir, sizeof(recordDir), "%s/chn%02d", activeDiskPath.c_str(), chn);

    // 解析日期字符串 (格式: YYYYMMDD)
    if (strlen(date) != 8) {
        LogE("日期格式错误: %s (期望格式: YYYYMMDD)", date);
        return FALSE;
    }

    int year, month, day;
    if (sscanf(date, "%4d%2d%2d", &year, &month, &day) != 3) {
        LogE("日期解析失败: %s", date);
        return FALSE;
    }

    // 构建该日期的开始和结束时间戳
    struct tm tm_start = {0};
    tm_start.tm_year = year - 1900;
    tm_start.tm_mon = month - 1;
    tm_start.tm_mday = day;
    tm_start.tm_hour = 0;
    tm_start.tm_min = 0;
    tm_start.tm_sec = 0;

    struct tm tm_end = tm_start;
    tm_end.tm_hour = 23;
    tm_end.tm_min = 59;
    tm_end.tm_sec = 59;

    time_t start_time = mktime(&tm_start);
    time_t end_time = mktime(&tm_end);

    if (start_time == -1 || end_time == -1) {
        LogE("时间转换失败: %s", date);
        return FALSE;
    }

    LogD("查询时间范围: %ld - %ld", start_time, end_time);

    // 扫描录像文件目录
    return scanRecordFiles(recordDir, chn, start_time, end_time, file_list);
}

// 扫描录像文件的辅助函数
static BOOL scanRecordFiles(const char* recordDir, INT32 chn, time_t start_time, time_t end_time,
                           std::multiset<T_PLAYBACK_FILE_TIME>& file_list) {

    DIR* dir = opendir(recordDir);
    if (!dir) {
        LogW("无法打开录像目录: %s, error=%s", recordDir, strerror(errno));
        return FALSE;
    }

    struct dirent* entry;
    int fileCount = 0;

    while ((entry = readdir(dir)) != nullptr) {
        // 跳过目录和非录像文件
        if (entry->d_type == DT_DIR || !isVideoFile(entry->d_name)) {
            continue;
        }

        // 解析文件名获取时间戳
        time_t fileTime = parseFileTimestamp(entry->d_name);
        if (fileTime == 0) {
            continue;
        }

        // 检查文件时间是否在查询范围内
        if (fileTime < start_time || fileTime > end_time) {
            continue;
        }

        // 构建完整文件路径
        char fullPath[1024];
        snprintf(fullPath, sizeof(fullPath), "%s/%s", recordDir, entry->d_name);

        // 获取文件信息
        struct stat fileStat;
        if (stat(fullPath, &fileStat) != 0) {
            LogW("无法获取文件信息: %s", fullPath);
            continue;
        }

        // 创建文件时间结构
        T_PLAYBACK_FILE_TIME fileInfo;
        memset(&fileInfo, 0, sizeof(fileInfo));

        fileInfo.st_time = fileTime;
        fileInfo.end_time = fileTime + 600; // 假设每个文件10分钟
        fileInfo.file_size = fileStat.st_size;
        fileInfo.event_type = 0; // 普通录像

        // 设置磁盘路径
        strncpy(fileInfo.disk_top, recordDir, sizeof(fileInfo.disk_top) - 1);

        // 设置文件路径
        strncpy(fileInfo.file_path, fullPath, sizeof(fileInfo.file_path) - 1);

        // 生成显示用的文件名 (HH:MM:SS格式)
        struct tm* tm_info = localtime(&fileTime);
        snprintf(fileInfo.file_crop_name, sizeof(fileInfo.file_crop_name),
                "%02d:%02d:%02d", tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

        // 添加到结果列表
        file_list.insert(fileInfo);
        fileCount++;

        LogD("找到录像文件: %s, size=%u, time=%ld", entry->d_name, fileInfo.file_size, fileTime);
    }

    closedir(dir);

    LogI("录像查询完成: 找到%d个文件", fileCount);
    return fileCount > 0 ? TRUE : FALSE;
}

// 检查是否为视频文件
static BOOL isVideoFile(const char* filename) {
    if (!filename) {
        return FALSE;
    }

    const char* ext = strrchr(filename, '.');
    if (!ext) {
        return FALSE;
    }

    // 支持的视频文件扩展名
    return (strcasecmp(ext, ".h264") == 0 ||
            strcasecmp(ext, ".mp4") == 0 ||
            strcasecmp(ext, ".avi") == 0);
}

// 从文件名解析时间戳
static time_t parseFileTimestamp(const char* filename) {
    if (!filename) {
        return 0;
    }

    // 假设文件名格式为: timestamp.h264 或 YYYYMMDD_HHMMSS.h264
    const char* basename = strrchr(filename, '/');
    if (basename) {
        basename++; // 跳过 '/'
    } else {
        basename = filename;
    }

    // 尝试解析纯时间戳格式
    char* endptr;
    time_t timestamp = strtol(basename, &endptr, 10);
    if (endptr != basename && (*endptr == '.' || *endptr == '_')) {
        // 验证时间戳是否合理 (2000年到2100年之间)
        if (timestamp > 946684800 && timestamp < 4102444800) {
            return timestamp;
        }
    }

    // 尝试解析 YYYYMMDD_HHMMSS 格式
    int year, month, day, hour, min, sec;
    if (sscanf(basename, "%4d%2d%2d_%2d%2d%2d", &year, &month, &day, &hour, &min, &sec) == 6) {
        struct tm tm_time = {0};
        tm_time.tm_year = year - 1900;
        tm_time.tm_mon = month - 1;
        tm_time.tm_mday = day;
        tm_time.tm_hour = hour;
        tm_time.tm_min = min;
        tm_time.tm_sec = sec;

        time_t result = mktime(&tm_time);
        if (result != -1) {
            return result;
        }
    }

    LogW("无法解析文件时间戳: %s", filename);
    return 0;
}

// ========== 时间转换工具函数实现 ==========

// 将日期和时间字符串转换为UTC时间戳
time_t mdisk_convert_utc(LPCSTR date, LPCSTR time_str) {
    if (!date || !time_str) {
        LogE("时间转换参数无效: date=%s, time=%s", date ? date : "NULL", time_str ? time_str : "NULL");
        return 0;
    }

    // 解析日期 (格式: YYYYMMDD)
    if (strlen(date) != 8) {
        LogE("日期格式错误: %s (期望格式: YYYYMMDD)", date);
        return 0;
    }

    int year, month, day;
    if (sscanf(date, "%4d%2d%2d", &year, &month, &day) != 3) {
        LogE("日期解析失败: %s", date);
        return 0;
    }

    // 解析时间 (格式: HH:MM:SS 或 HHMMSS)
    int hour, min, sec;
    if (strchr(time_str, ':')) {
        // HH:MM:SS 格式
        if (sscanf(time_str, "%d:%d:%d", &hour, &min, &sec) != 3) {
            LogE("时间解析失败: %s", time_str);
            return 0;
        }
    } else {
        // HHMMSS 格式
        if (strlen(time_str) != 6 || sscanf(time_str, "%2d%2d%2d", &hour, &min, &sec) != 3) {
            LogE("时间解析失败: %s", time_str);
            return 0;
        }
    }

    // 验证时间有效性
    if (year < 1970 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31 ||
        hour < 0 || hour > 23 || min < 0 || min > 59 || sec < 0 || sec > 59) {
        LogE("时间值超出范围: %04d-%02d-%02d %02d:%02d:%02d", year, month, day, hour, min, sec);
        return 0;
    }

    // 构建时间结构
    struct tm tm_time = {0};
    tm_time.tm_year = year - 1900;
    tm_time.tm_mon = month - 1;
    tm_time.tm_mday = day;
    tm_time.tm_hour = hour;
    tm_time.tm_min = min;
    tm_time.tm_sec = sec;

    // 转换为时间戳
    time_t result = mktime(&tm_time);
    if (result == -1) {
        LogE("时间转换失败: %04d-%02d-%02d %02d:%02d:%02d", year, month, day, hour, min, sec);
        return 0;
    }

    LogD("时间转换成功: %s %s -> %ld", date, time_str, result);
    return result;
}

// ========== 字符串处理工具函数实现 ==========

// 去掉时间字符串中的冒号
void remove_colons(const char *src, char *dest) {
    if (!src || !dest) {
        LogE("字符串处理参数无效");
        if (dest) dest[0] = '\0';
        return;
    }

    const char *s = src;
    char *d = dest;

    while (*s) {
        if (*s != ':') {
            *d++ = *s;
        }
        s++;
    }
    *d = '\0';

    LogD("去除冒号: %s -> %s", src, dest);
}

// ========== 录像导出功能实现 ==========


/**
 * 旧的导出录像文件函数 - 已被 ExportManager::exportRecordToAvi 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
BOOL export_record_to_avi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path)
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        LogE("无法获取 ExportManager 实例");
        return FALSE;
    }
    return manager->exportRecordToAvi(chn, source_file, target_file, disk_path);

}

/**
 * 旧的按时间导出录像文件函数 - 已被 ExportManager::exportRecordToAviByTime 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
BOOL export_record_to_avi_by_time(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path)
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        LogE("无法获取 ExportManager 实例");
        return FALSE;
    }
    return manager->exportRecordToAviByTime(chn, start_time, end_time, target_file, disk_path);

}

/**
 * 旧的检查导出状态函数 - 已被 ExportManager::isExportRunning 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
BOOL is_export_running()
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        return FALSE;
    }
    return manager->isExportRunning();
}

/**
 * 旧的取消导出函数 - 已被 ExportManager::cancelExport 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
VOID cancel_export()
{
    ExportManager* manager = ExportManager::getInstance();
    if (manager) {
        manager->cancelExport();
    }
}

// 兼容性接口：获取导出进度
float get_export_progress()
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        return 0.0f;
    }
    return manager->getExportProgress();
}



