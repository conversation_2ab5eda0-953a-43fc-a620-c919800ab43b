#include "playback.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <time.h>
#include <errno.h>
#include <iostream>

#include "vs_comm_def.h"
#include "channel.h"
#include "vs_media_file.h"
#include "avilib.h"
#include "record.h"

// 定义最大解码通道数
#define MAX_DECODE_CHANNELS 5

// ========== 单例静态成员初始化 ==========

// PlaybackManager 单例相关
PlaybackManager* PlaybackManager::m_instance = nullptr;
TCSLock PlaybackManager::m_instanceMutex;

// ExportManager 单例相关
ExportManager* ExportManager::m_instance = nullptr;
TCSLock ExportManager::m_instanceMutex;

// ========== PlaybackSession 类实现 ==========

PlaybackSession::PlaybackSession(int session_id) : m_sessionId(session_id)
{
    LogI("创建回放会话: session_id = %d", session_id);

    // 初始化回放控制结构
    memset(&m_ctrl, 0, sizeof(T_MDISK_PLAYBACK_CTRL));
    m_ctrl.enable = 0;
    m_ctrl.Run = 0;
    m_ctrl.play_chn = -1;
    m_ctrl.vdec_chn = session_id;
    m_ctrl.vo_chn = session_id;
    m_ctrl.play_speed = TUTK_REPLAY_SPEED_NORMAL;
    m_ctrl.play_pause = 0;
    m_ctrl.video_buff = (CHAR*)malloc(MAX_PLAYBACK_SIZE);
    m_ctrl.audio_buff = (CHAR*)malloc(64 * 1024);
    m_ctrl.mdisk_h = NULL;

    // 获取可用的磁盘路径
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager && diskManager->hasAvailableDisk()) {
        std::string activeDiskPath = diskManager->getActiveDiskPath();
        if (!activeDiskPath.empty()) {
            s_strcpy(m_ctrl.disk_top, activeDiskPath.c_str());
            LogI("会话 %d 初始化磁盘路径: %s", session_id, m_ctrl.disk_top);
        } else {
            s_strcpy(m_ctrl.disk_top, "");
            LogW("会话 %d 无法获取活动磁盘路径", session_id);
        }
    } else {
        s_strcpy(m_ctrl.disk_top, "");
        LogW("会话 %d 无可用磁盘", session_id);
    }
}

PlaybackSession::~PlaybackSession()
{
    LogI("销毁回放会话: session_id = %d", m_sessionId);

    // 停止会话
    stopSession();

    // 释放缓冲区
    if (m_ctrl.video_buff) {
        free(m_ctrl.video_buff);
        m_ctrl.video_buff = NULL;
    }
    if (m_ctrl.audio_buff) {
        free(m_ctrl.audio_buff);
        m_ctrl.audio_buff = NULL;
    }
}

bool PlaybackSession::startSession(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top)
{
    ScopedLocker lock(m_sessionMutex);

    LogI("启动回放会话: session_id=%d, chn=%d, start_time=%u, end_time=%u, speed=%d",
         m_sessionId, chn, start_time, end_time, speed);

    // 停止当前会话（如果正在运行）
    stopSession();

    // 设置回放参数
    m_ctrl.enable = 1;
    m_ctrl.play_chn = chn;
    m_ctrl.start_time = start_time;
    m_ctrl.end_time = end_time;
    m_ctrl.play_speed = speed;
    m_ctrl.play_pause = 0;
    m_ctrl.seek_time = 0;
    m_ctrl.read_frame = 0;
    m_ctrl.checkKeyFrame = 1;
    m_ctrl.Run = 1;

    // 设置磁盘路径
    if (disk_top && *disk_top) {
        std::string diskPath = disk_top;
        // 确保路径末尾有斜杠
        if (!diskPath.empty() && diskPath.back() != '/') {
            diskPath += '/';
        }
        s_strcpy(m_ctrl.disk_top, diskPath.c_str());
    } else {
        // 如果未提供磁盘路径，使用活动磁盘
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            std::string activePath = diskManager->getActiveDiskPath();
            s_strcpy(m_ctrl.disk_top, activePath.c_str());
        } else {
            s_strcpy(m_ctrl.disk_top, "");
            LogE("磁盘管理器未初始化，无法设置磁盘路径");
        }
    }
    LogI("设置回放路径: %s", m_ctrl.disk_top);

    // 创建回放线程
    int ret = pthread_create(&m_ctrl.play_thrd_id, NULL, playbackThreadFunc, this);
    if (ret != 0) {
        LogE("创建回放线程失败: session_id=%d, chn=%d, ret=%d", m_sessionId, chn, ret);
        m_ctrl.enable = 0;
        m_ctrl.Run = 0;
        return false;
    }

    return true;
}

void PlaybackSession::stopSession()
{
    ScopedLocker lock(m_sessionMutex);

    if (!m_ctrl.Run) {
        return; // 已经停止
    }

    LogI("停止回放会话: session_id=%d", m_sessionId);

    // 设置停止标志
    m_ctrl.Run = 0;
    m_ctrl.enable = 0;

    // 等待线程结束
    if (m_ctrl.play_thrd_id != 0) {
        pthread_join(m_ctrl.play_thrd_id, NULL);
        m_ctrl.play_thrd_id = 0;
    }

    // 关闭mdisk句柄
    if (m_ctrl.mdisk_h) {
        PlaybackManager* manager = PlaybackManager::getInstance();
        if (manager) {
            manager->mdiskPlayClose(m_ctrl.mdisk_h, TRUE);
        }
        m_ctrl.mdisk_h = NULL;
    }

    LogI("回放会话已停止: session_id=%d", m_sessionId);
}

void PlaybackSession::pauseSession(BOOL pause)
{
    ScopedLocker lock(m_sessionMutex);

    if (m_ctrl.enable && m_ctrl.Run) {
        m_ctrl.play_pause = pause;
        LogI("%s回放会话: session_id=%d, play_chn=%d",
             pause ? "暂停" : "恢复", m_sessionId, m_ctrl.play_chn);
    }
}

void PlaybackSession::setSpeed(INT32 speed)
{
    ScopedLocker lock(m_sessionMutex);

    if (m_ctrl.enable && m_ctrl.Run) {
        m_ctrl.play_speed = speed;
        LogI("设置回放速度: session_id=%d, speed=%d", m_sessionId, speed);
    }
}

bool PlaybackSession::seekToTime(UINT32 time)
{
    ScopedLocker lock(m_sessionMutex);

    if (!m_ctrl.enable || !m_ctrl.Run) {
        LogE("回放会话未运行，无法定位: session_id=%d", m_sessionId);
        return false;
    }

    m_ctrl.seek_time = time;
    LogI("设置回放定位时间: session_id=%d, time=%u", m_sessionId, time);
    return true;
}

UINT32 PlaybackSession::getCurrentTime()
{
    ScopedLocker lock(m_sessionMutex);

    if (m_ctrl.mdisk_h) {
        return m_ctrl.mdisk_h->index_entry.utc_time;
    }
    return 0;
}

void* PlaybackSession::playbackThreadFunc(void* arg)
{
    PlaybackSession* session = (PlaybackSession*)arg;
    session->playbackThreadWork();
    return NULL;
}

void PlaybackSession::playbackThreadWork()
{
    LogI("回放线程开始: session_id=%d, play_chn=%d", m_sessionId, m_ctrl.play_chn);

    int vdec_chn = m_ctrl.vdec_chn;
    int vo_chn = m_ctrl.vo_chn;
    int play_chn = m_ctrl.play_chn;
    int last_codec = -1;

    // 确保磁盘路径末尾有斜杠
    std::string diskPath = m_ctrl.disk_top;
    if (!diskPath.empty() && diskPath.back() != '/') {
        diskPath += '/';
        strncpy(m_ctrl.disk_top, diskPath.c_str(), sizeof(m_ctrl.disk_top)-1);
    }

    // 检查磁盘是否已挂载
    bool isMounted = true;
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager) {
        isMounted = diskManager->isMounted(m_ctrl.disk_top);
    }

    if (!isMounted) {
        LogE("回放线程启动失败: 磁盘未挂载: %s", m_ctrl.disk_top);
        m_ctrl.Run = 0;
        return;
    }

    // 转换开始时间为STimeDay结构
    STimeDay stTimeDay;
    struct tm start_tm;
    time_t start_time = m_ctrl.start_time;
    localtime_r(&start_time, &start_tm);

    stTimeDay.year = start_tm.tm_year + 1900;
    stTimeDay.month = start_tm.tm_mon + 1;
    stTimeDay.day = start_tm.tm_mday;
    stTimeDay.hour = start_tm.tm_hour;
    stTimeDay.minute = start_tm.tm_min;
    stTimeDay.second = start_tm.tm_sec;

    // 打开mdisk回放句柄
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取 PlaybackManager 实例");
        m_ctrl.Run = 0;
        return;
    }

    m_ctrl.mdisk_h = manager->mdiskPlayOpen(NULL, play_chn, &stTimeDay, 1, m_ctrl.disk_top);
    if (!m_ctrl.mdisk_h) {
        LogE("回放线程启动失败: 无法打开mdisk句柄: play_chn=%d, 磁盘路径=%s",
             play_chn, m_ctrl.disk_top);
        m_ctrl.Run = 0;
        return;
    }

    // 如果有seek请求，先定位
    if (m_ctrl.seek_time > 0) {
        if (!manager->mdiskPlaySeek(m_ctrl.mdisk_h, m_ctrl.seek_time)) {
            LogE("回放定位失败: play_chn=%d, time=%ld", play_chn, m_ctrl.seek_time);
        }
        m_ctrl.seek_time = 0;
    }

    // 回放主循环
    m_ctrl.read_frame = 1;
    m_ctrl.checkKeyFrame = 1;

    vs_hisi_vdec_start_chn(vdec_chn);
    vs_hisi_vo_show_chn(vo_chn);

    LogI("开始回放: session_id=%d, 通道=%d, 解码通道=%d, 磁盘路径=%s",
         m_sessionId, play_chn, vdec_chn, m_ctrl.disk_top);

    // 主回放循环
    while (m_ctrl.Run) {
        if (m_ctrl.play_pause) {
            usleep(40000); // 暂停时休眠40ms
            continue;
        }

        // 检查是否有seek请求
        if (m_ctrl.seek_time > 0) {
            if (manager->mdiskPlaySeek(m_ctrl.mdisk_h, m_ctrl.seek_time)) {
                m_ctrl.checkKeyFrame = 1; // seek后必须从关键帧开始
            } else {
                LogE("回放定位失败: play_chn=%d, time=%ld", play_chn, m_ctrl.seek_time);
            }
            m_ctrl.seek_time = 0;
        }

        // 读取帧数据
        if (m_ctrl.read_frame) {
            int frame_size = manager->mdiskPlayReadFrame(m_ctrl.mdisk_h, play_chn,
                                                       m_ctrl.video_buff, MAX_PLAYBACK_SIZE, TRUE);

            // 检查是否读取到帧
            if (frame_size > 0) {
                T_MDISK_PLAY_CONTEXT* mdisk_ctx = m_ctrl.mdisk_h;

                // 检查结束时间（当end_time不为0时）
                if (m_ctrl.end_time > 0 && mdisk_ctx->index_entry.utc_time >= m_ctrl.end_time) {
                    LogI("回放到达结束时间: session_id=%d, current_time=%u, end_time=%u",
                         m_sessionId, mdisk_ctx->index_entry.utc_time, m_ctrl.end_time);
                    break;
                }

                // 检查是否需要跳过非关键帧
                if (m_ctrl.checkKeyFrame && mdisk_ctx->index_entry.frame_type != IPC_FRAME_FLAG_IFRAME) {
                    LogW("等待关键帧: frame_type = %d", mdisk_ctx->index_entry.frame_type);
                    continue; // 跳过非关键帧
                }

                // 关键帧检查通过
                if (mdisk_ctx->index_entry.frame_type == IPC_FRAME_FLAG_IFRAME) {
                    m_ctrl.checkKeyFrame = 0;
                }

                // 检查事件类型过滤
                PlaybackManager* playbackManager = PlaybackManager::getInstance();
                if (playbackManager) {
                    UINT8 event_filter = playbackManager->getEventTypeFilter();
                    if (event_filter > 0) {
                        // 获取当前帧的事件类型
                        UINT8 frame_event_type = mdisk_ctx->index_entry.event_type;

                        // 如果事件类型不匹配，跳过该帧
                        if (frame_event_type != event_filter) {
                            continue;
                        }
                    }
                }

				// 检查编解码器是否改变
                if (last_codec != mdisk_ctx->hdr.codec) {
                    if (mdisk_ctx->hdr.codec == MEDIA_CODEC_VIDEO_HEVC) {
                        vs_hisi_vdec_set_protocol(vdec_chn, TRUE);
                        LogI("设置H265编解码器: session_id=%d", m_sessionId);
                        last_codec = mdisk_ctx->hdr.codec;
                    }
                    else if (mdisk_ctx->hdr.codec == MEDIA_CODEC_VIDEO_H264) {
                        vs_hisi_vdec_set_protocol(vdec_chn, FALSE);
                        LogI("设置H264编解码器: session_id=%d", m_sessionId);
                        last_codec = mdisk_ctx->hdr.codec;
                    }
                }

                // 发送到解码播放
                vs_hisi_vdec_send_frame(vdec_chn, vo_chn, m_ctrl.video_buff,
                                        mdisk_ctx->index_entry.frame_size,
                                        mdisk_ctx->index_entry.time_stamp, FALSE, FALSE);

                // 根据播放速度控制延时
                int delay_ms = 0;
                switch (m_ctrl.play_speed) {
                    case TUTK_REPLAY_SPEED_SLOW_EIGHT:   delay_ms = 80; break;
                    case TUTK_REPLAY_SPEED_SLOW_FOUR:    delay_ms = 40; break;
                    case TUTK_REPLAY_SPEED_SLOW_TWO:     delay_ms = 20; break;
                    case TUTK_REPLAY_SPEED_NORMAL:       delay_ms = 10; break;
                    case TUTK_REPLAY_SPEED_QUICK_TWO:    delay_ms = 5;  break;
                    case TUTK_REPLAY_SPEED_QUICK_FOUR:   delay_ms = 2;  break;
                    default:                             delay_ms = 1;  break;
                }

                usleep(delay_ms * 1000);
            }
            else if (frame_size == 0) {
                // 文件结束
                LogI("回放结束: session_id=%d, play_chn=%d", m_sessionId, play_chn);
                break;
            }
            else {
                // 读取错误
                LogE("回放读取帧错误: session_id=%d, play_chn=%d, ret=%d",
                     m_sessionId, play_chn, frame_size);
                break;
            }
        }
    }

    // 清理资源
    vs_hisi_vo_hide_chn(vo_chn);
    vs_hisi_vdec_stop_chn(vdec_chn);

    if (m_ctrl.mdisk_h) {
        manager->mdiskPlayClose(m_ctrl.mdisk_h, TRUE);
        m_ctrl.mdisk_h = NULL;
    }

    m_ctrl.Run = 0;
    LogI("回放线程结束: session_id=%d", m_sessionId);
}

// ========== ExportManager 单例实现 ==========

ExportManager* ExportManager::getInstance()
{
    if (m_instance == nullptr) {
        m_instanceMutex.Enter();
        if (m_instance == nullptr) {
            m_instance = new ExportManager();
            LogI("ExportManager 单例实例创建成功");
        }
        m_instanceMutex.Leave();
    }
    return m_instance;
}

void ExportManager::destroyInstance()
{
    m_instanceMutex.Enter();
    if (m_instance != nullptr) {
        LogI("销毁 ExportManager 单例实例");
        delete m_instance;
        m_instance = nullptr;
    }
    m_instanceMutex.Leave();
}

ExportManager::ExportManager()
{
    LogI("ExportManager 构造函数");

    // 初始化导出状态
    memset(&m_exportStatus, 0, sizeof(m_exportStatus));
    m_exportStatus.running = FALSE;
    m_exportStatus.cancel_flag = FALSE;
    m_exportStatus.progress = 0.0f;
}

ExportManager::~ExportManager()
{
    LogI("ExportManager 析构函数");

    // 确保导出已停止
    cancelExport();
}

bool ExportManager::exportRecordToAvi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path)
{
    ScopedLocker lock(m_exportMutex);

    if (m_exportStatus.running) {
        LogE("导出正在进行中，无法启动新的导出任务");
        return false;
    }

    LogI("开始按文件导出录像: chn=%d, source=%s, target=%s, disk_path=%s",
         chn, source_file, target_file, disk_path ? disk_path : "默认");

    // 设置导出参数
    m_exportStatus.running = TRUE;
    m_exportStatus.cancel_flag = FALSE;
    m_exportStatus.progress = 0.0f;
    m_exportStatus.chn = chn;
    m_exportStatus.by_time = false;

    s_strcpy(m_exportStatus.source_file, source_file);
    s_strcpy(m_exportStatus.target_file, target_file);

    if (disk_path && *disk_path) {
        s_strcpy(m_exportStatus.disk_path, disk_path);
    } else {
        // 使用活动磁盘路径
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            std::string activePath = diskManager->getActiveDiskPath();
            s_strcpy(m_exportStatus.disk_path, activePath.c_str());
        } else {
            s_strcpy(m_exportStatus.disk_path, "");
        }
    }

    // 创建导出线程
    int ret = pthread_create(&m_exportStatus.export_thread, NULL, exportThreadByFile, this);
    if (ret != 0) {
        LogE("创建导出线程失败: ret=%d", ret);
        m_exportStatus.running = FALSE;
        return false;
    }

    LogI("导出任务已启动");
    return true;
}

bool ExportManager::exportRecordToAviByTime(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path)
{
    ScopedLocker lock(m_exportMutex);

    if (m_exportStatus.running) {
        LogE("导出正在进行中，无法启动新的导出任务");
        return false;
    }

    LogI("开始按时间导出录像: chn=%d, start=%u, end=%u, target=%s, disk_path=%s",
         chn, start_time, end_time, target_file, disk_path ? disk_path : "默认");

    // 设置导出参数
    m_exportStatus.running = TRUE;
    m_exportStatus.cancel_flag = FALSE;
    m_exportStatus.progress = 0.0f;
    m_exportStatus.chn = chn;
    m_exportStatus.start_time = start_time;
    m_exportStatus.end_time = end_time;
    m_exportStatus.by_time = true;

    s_strcpy(m_exportStatus.target_file, target_file);

    if (disk_path && *disk_path) {
        s_strcpy(m_exportStatus.disk_path, disk_path);
    } else {
        // 使用活动磁盘路径
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            std::string activePath = diskManager->getActiveDiskPath();
            s_strcpy(m_exportStatus.disk_path, activePath.c_str());
        } else {
            s_strcpy(m_exportStatus.disk_path, "");
        }
    }

    // 创建导出线程
    int ret = pthread_create(&m_exportStatus.export_thread, NULL, exportThreadByTime, this);
    if (ret != 0) {
        LogE("创建导出线程失败: ret=%d", ret);
        m_exportStatus.running = FALSE;
        return false;
    }

    LogI("导出任务已启动");
    return true;
}

bool ExportManager::isExportRunning() const
{
    ScopedLocker lock(m_exportMutex);
    return m_exportStatus.running;
}

void ExportManager::cancelExport()
{
    ScopedLocker lock(m_exportMutex);

    if (!m_exportStatus.running) {
        return;
    }

    LogI("取消导出任务");

    // 设置取消标志
    m_exportStatus.cancel_flag = TRUE;

    // 等待导出线程结束
    if (m_exportStatus.export_thread != 0) {
        pthread_join(m_exportStatus.export_thread, NULL);
        m_exportStatus.export_thread = 0;
    }

    m_exportStatus.running = FALSE;
    LogI("导出任务已取消");
}

float ExportManager::getExportProgress() const
{
    ScopedLocker lock(m_exportMutex);
    return m_exportStatus.progress;
}

void* ExportManager::exportThreadByFile(void* arg)
{
    ExportManager* manager = (ExportManager*)arg;
    manager->exportThreadByFileWork();
    return NULL;
}

void* ExportManager::exportThreadByTime(void* arg)
{
    ExportManager* manager = (ExportManager*)arg;
    manager->exportThreadByTimeWork();
    return NULL;
}

void ExportManager::exportThreadByFileWork()
{
    // 获取导出参数（需要在锁保护下）
    INT32 chn;
    char source_file[256], target_file[256];
    bool cancel_flag;

    {
        ScopedLocker lock(m_exportMutex);
        chn = m_exportStatus.chn;
        strcpy(source_file, m_exportStatus.source_file);
        strcpy(target_file, m_exportStatus.target_file);
        cancel_flag = m_exportStatus.cancel_flag;
    }

    LogI("开始导出录像: 通道=%d, 源文件=%s, 目标文件=%s", chn, source_file, target_file);

    // 检查源文件是否存在
    FILE* source_fp = fopen(source_file, "rb");
    if (!source_fp) {
        LogE("源文件不存在: %s", source_file);
        {
            ScopedLocker lock(m_exportMutex);
            m_exportStatus.running = FALSE;
        }
        return;
    }

    // 获取源文件大小
    fseek(source_fp, 0, SEEK_END);
    long file_size = ftell(source_fp);
    fseek(source_fp, 0, SEEK_SET);

    // 创建目标文件
    FILE* target_fp = fopen(target_file, "wb");
    if (!target_fp) {
        LogE("无法创建目标文件: %s", target_file);
        fclose(source_fp);
        {
            ScopedLocker lock(m_exportMutex);
            m_exportStatus.running = FALSE;
        }
        return;
    }

    // 复制文件内容
    char buffer[64 * 1024]; // 64KB 缓冲区
    long copied_bytes = 0;
    size_t bytes_read;

    while ((bytes_read = fread(buffer, 1, sizeof(buffer), source_fp)) > 0) {
        // 检查取消标志
        {
            ScopedLocker lock(m_exportMutex);
            if (m_exportStatus.cancel_flag) {
                break;
            }
        }

        if (fwrite(buffer, 1, bytes_read, target_fp) != bytes_read) {
            LogE("写入目标文件失败: %s", target_file);
            break;
        }

        copied_bytes += bytes_read;

        // 更新进度
        {
            ScopedLocker lock(m_exportMutex);
            m_exportStatus.progress = (float)copied_bytes / file_size;
        }

        // 每复制1MB数据休眠一下，避免占用过多CPU
        if (copied_bytes % (1024 * 1024) == 0) {
            usleep(1000);
        }
    }

    fclose(source_fp);
    fclose(target_fp);

    // 检查最终状态并更新
    {
        ScopedLocker lock(m_exportMutex);
        if (!m_exportStatus.cancel_flag && copied_bytes == file_size) {
            m_exportStatus.progress = 1.0f;
            LogI("录像导出完成: 目标文件=%s, 大小=%ld字节", target_file, copied_bytes);
        } else {
            LogW("导出被取消或失败: %s", target_file);
            // 删除不完整的目标文件
            remove(target_file);
        }
        m_exportStatus.running = FALSE;
    }
}

void ExportManager::exportThreadByTimeWork()
{
    // 获取导出参数（需要在锁保护下）
    INT32 chn;
    UINT32 start_time, end_time;
    char target_file[256], disk_path[256];

    {
        ScopedLocker lock(m_exportMutex);
        chn = m_exportStatus.chn;
        start_time = m_exportStatus.start_time;
        end_time = m_exportStatus.end_time;
        strcpy(target_file, m_exportStatus.target_file);
        strcpy(disk_path, m_exportStatus.disk_path);
    }

    LogI("开始按时间导出录像: 通道=%d, 开始时间=%u, 结束时间=%u, 目标文件=%s",
         chn, start_time, end_time, target_file);

    // 获取PlaybackManager实例用于文件操作
    PlaybackManager* playbackManager = PlaybackManager::getInstance();
    if (!playbackManager) {
        LogE("无法获取 PlaybackManager 实例");
        m_exportStatus.running = FALSE;
        return;
    }

    // 转换开始时间为STimeDay结构
    STimeDay stTimeDay;
    struct tm start_tm;
    time_t start_time_t = m_exportStatus.start_time;
    localtime_r(&start_time_t, &start_tm);

    stTimeDay.year = start_tm.tm_year + 1900;
    stTimeDay.month = start_tm.tm_mon + 1;
    stTimeDay.day = start_tm.tm_mday;
    stTimeDay.hour = start_tm.tm_hour;
    stTimeDay.minute = start_tm.tm_min;
    stTimeDay.second = start_tm.tm_sec;

    // 打开mdisk回放句柄
    T_MDISK_PLAY_HADLE mdisk_h = playbackManager->mdiskPlayOpen(NULL, m_exportStatus.chn, &stTimeDay, 1, m_exportStatus.disk_path);
    if (!mdisk_h) {
        LogE("无法打开mdisk句柄进行导出: chn=%d, disk_path=%s", m_exportStatus.chn, m_exportStatus.disk_path);
        m_exportStatus.running = FALSE;
        return;
    }

    // 创建目标文件
    FILE* target_fp = fopen(m_exportStatus.target_file, "wb");
    if (!target_fp) {
        LogE("无法创建目标文件: %s", m_exportStatus.target_file);
        playbackManager->mdiskPlayClose(mdisk_h, TRUE);
        m_exportStatus.running = FALSE;
        return;
    }

    // 导出循环
    UINT32 total_duration = m_exportStatus.end_time - m_exportStatus.start_time;
    UINT32 current_time = m_exportStatus.start_time;
    char frame_buffer[MAX_PLAYBACK_SIZE];
    long total_exported = 0;

    while (current_time < m_exportStatus.end_time && !m_exportStatus.cancel_flag) {
        // 读取帧数据
        int frame_size = playbackManager->mdiskPlayReadFrame(mdisk_h, m_exportStatus.chn,
                                                            frame_buffer, MAX_PLAYBACK_SIZE, TRUE);

        if (frame_size > 0) {
            // 写入目标文件
            if (fwrite(frame_buffer, 1, frame_size, target_fp) != (size_t)frame_size) {
                LogE("写入目标文件失败: %s", m_exportStatus.target_file);
                break;
            }

            total_exported += frame_size;

            // 更新当前时间（从mdisk句柄获取）
            if (mdisk_h->index_entry.utc_time > current_time) {
                current_time = mdisk_h->index_entry.utc_time;
            } else {
                current_time++; // 防止时间不前进
            }

            // 更新进度
            if (total_duration > 0) {
                m_exportStatus.progress = (float)(current_time - m_exportStatus.start_time) / total_duration;
            }
        } else if (frame_size == 0) {
            // 文件结束
            LogI("到达文件末尾，导出结束");
            break;
        } else {
            // 读取错误
            LogE("读取帧数据失败: ret=%d", frame_size);
            break;
        }

        // 每导出1MB数据休眠一下
        if (total_exported % (1024 * 1024) == 0) {
            usleep(1000);
        }
    }

    fclose(target_fp);
    playbackManager->mdiskPlayClose(mdisk_h, TRUE);

    if (!m_exportStatus.cancel_flag && current_time >= m_exportStatus.end_time) {
        m_exportStatus.progress = 1.0f;
        LogI("按时间录像导出完成: 目标文件=%s, 导出大小=%ld字节", m_exportStatus.target_file, total_exported);
    } else {
        LogW("导出被取消或失败: %s", m_exportStatus.target_file);
        // 删除不完整的目标文件
        remove(m_exportStatus.target_file);
    }

    m_exportStatus.running = FALSE;
}

// ========== PlaybackManager 单例实现 ==========

PlaybackManager* PlaybackManager::getInstance()
{
    if (m_instance == nullptr) {
        m_instanceMutex.Enter();
        if (m_instance == nullptr) {
            m_instance = new PlaybackManager();
            LogI("PlaybackManager 单例实例创建成功");
        }
        m_instanceMutex.Leave();
    }
    return m_instance;
}

void PlaybackManager::destroyInstance()
{
    m_instanceMutex.Enter();
    if (m_instance != nullptr) {
        LogI("销毁 PlaybackManager 单例实例");
        delete m_instance;
        m_instance = nullptr;
    }
    m_instanceMutex.Leave();
}

PlaybackManager::PlaybackManager() : m_currentPlaybackChannel(-1), m_eventTypeFilter(0), m_moduleInitialized(false)
{
    LogI("PlaybackManager 构造函数");
}

PlaybackManager::~PlaybackManager()
{
    LogI("PlaybackManager 析构函数");

    // 清理所有会话
    destroyAllSessions();
}



// ========== PlaybackManager 核心方法实现 ==========

bool PlaybackManager::initializeModule()
{
    ScopedLocker lock(m_managerMutex);

    if (m_moduleInitialized) {
        LogW("回放模块已经初始化过，跳过重复初始化");
        return true;
    }

    LogI("======== 回放模块初始化开始 ========");

    // 初始化回放控制
    initPlaybackCtrl();

    m_moduleInitialized = true;

    LogI("回放模块初始化成功");
    LogI("最大解码通道数: %d", MAX_DECODE_CHANNELS);
    LogI("======== 回放模块初始化结束 ========");

    return true;
}

void PlaybackManager::cleanupModule()
{
    ScopedLocker lock(m_managerMutex);

    if (!m_moduleInitialized) {
        LogI("回放模块未初始化，无需清理");
        return;
    }

    LogI("======== 回放模块清理开始 ========");

    // 销毁所有会话
    destroyAllSessions();

    m_moduleInitialized = false;

    LogI("回放模块清理完成");
    LogI("======== 回放模块清理结束 ========");
}

void PlaybackManager::initPlaybackCtrl()
{
    LogI("初始化回放控制: 最大解码通道数=%d", MAX_DECODE_CHANNELS);
    // 现在回放控制由 PlaybackSession 对象管理，这里只做日志记录
}

PlaybackSession* PlaybackManager::createSession()
{
    ScopedLocker lock(m_managerMutex);

    int sessionId = findAvailableSessionId();
    if (sessionId < 0) {
        LogE("无法创建回放会话: 已达到最大会话数 %d", MAX_DECODE_CHANNELS);
        return nullptr;
    }

    PlaybackSession* session = new PlaybackSession(sessionId);
    m_sessions[sessionId] = session;

    LogI("创建回放会话成功: session_id = %d", sessionId);
    return session;
}

void PlaybackManager::destroySession(PlaybackSession* session)
{
    if (!session) {
        return;
    }

    ScopedLocker lock(m_managerMutex);

    int sessionId = session->getSessionId();
    auto it = m_sessions.find(sessionId);
    if (it != m_sessions.end()) {
        delete session;
        m_sessions.erase(it);
        LogI("销毁回放会话: session_id = %d", sessionId);
    }
}

PlaybackSession* PlaybackManager::getSession(int sessionId)
{
    ScopedLocker lock(m_managerMutex);

    auto it = m_sessions.find(sessionId);
    return (it != m_sessions.end()) ? it->second : nullptr;
}

void PlaybackManager::destroyAllSessions()
{
    ScopedLocker lock(m_managerMutex);

    LogI("销毁所有回放会话: 当前会话数 = %zu", m_sessions.size());

    for (auto& pair : m_sessions) {
        delete pair.second;
    }
    m_sessions.clear();
}

int PlaybackManager::findAvailableSessionId()
{
    for (int i = 0; i < MAX_DECODE_CHANNELS; i++) {
        if (m_sessions.find(i) == m_sessions.end()) {
            return i;
        }
    }
    return -1;
}

bool PlaybackManager::hasActiveSession() const
{
    for (const auto& pair : m_sessions) {
        PlaybackSession* session = pair.second;
        if (session && session->isRunning()) {
            return true;
        }
    }
    return false;
}

VOID PlaybackManager::setPlaybackSpeed(INT32 speed)
{
    ScopedLocker lock(m_managerMutex);

    LogI("设置所有回放会话速度: speed=%d", speed);

    // 设置所有活动回放会话的速度
    for (auto& pair : m_sessions) {
        PlaybackSession* session = pair.second;
        if (session && session->isRunning()) {
            session->setSpeed(speed);
        }
    }
}

BOOL PlaybackManager::seekPlaybackToTime(UINT32 time)
{
    ScopedLocker lock(m_managerMutex);

    LogI("定位所有回放会话到时间: time=%u", time);

    bool result = false;
    // 定位所有活动回放会话
    for (auto& pair : m_sessions) {
        PlaybackSession* session = pair.second;
        if (session && session->isRunning()) {
            if (session->seekToTime(time)) {
                result = true;
            }
        }
    }
    return result;
}

UINT32 PlaybackManager::getPlaybackCurrentTime()
{
    ScopedLocker lock(m_managerMutex);

    // 返回第一个活动回放会话的当前时间
    for (auto& pair : m_sessions) {
        PlaybackSession* session = pair.second;
        if (session && session->isRunning()) {
            return session->getCurrentTime();
        }
    }
    return 0;
}

INT32 PlaybackManager::mdiskPlaybackTest(INT32 chn, UINT32 start_time, UINT32 end_time, UINT8 event_type, const CHAR* disk_top)
{
    LogI("回放测试: chn=%d, start=%u, end=%u, event_type=%d, disk_top=%s",
         chn, start_time, end_time, event_type, disk_top ? disk_top : "未指定");

    // 设置事件类型过滤器
    if (event_type > 0) {
        setEventTypeFilter(event_type);
        LogI("设置事件类型过滤器: chn=%d, event_type=%d", chn, event_type);
    }

    // 启动回放，使用指定的磁盘路径
    return startPlayback(chn, start_time, end_time, TUTK_REPLAY_SPEED_NORMAL, disk_top);
}


// ========== PlaybackManager mdisk 相关方法实现 ==========

/**
 * 打开文件, 根据stTimeDay定位文件
 * @param  old_h     old_h=NULL, 则分配句柄；old_h!=NULL,则根据定位决定是否关闭再重建；此句柄要返回给下面的三个函数使用
 * @param  stTimeDay 需要定位的时间
 * @param  disk_path 磁盘路径，为NULL时使用默认路径
 * @return           成功返回非0
 */
T_MDISK_PLAY_HADLE PlaybackManager::mdiskPlayOpen(T_MDISK_PLAY_HADLE h, INT32 play_chn, STimeDay *stTimeDay, UINT8 with_tz, const CHAR* disk_path)
{
	CHAR	path[200];	
	FILE	*pf;	
	time_t	ltime;
	UINT8	read_next;

	// 确认是定位还是打开下一个记录
	if (stTimeDay != NULL) {
		mdiskPlayClose(h, TRUE);
		h = NULL;
		read_next = FALSE;
	} else {
		QF_ASSERT(h != NULL);
		stTimeDay = &h->stTimeDay;
		read_next = TRUE;
	}
	QF_ASSERT(stTimeDay != NULL);
	ltime = to_timet(stTimeDay);	

    // 使用传入的磁盘路径
    std::string diskPath;
    if (disk_path && *disk_path) {
        diskPath = disk_path;
        // 确保路径末尾有斜杠
        if (!diskPath.empty() && diskPath.back() != '/') {
            diskPath += '/';
        }
    } else if (h && h->disk_top[0]) {
        // 如果有句柄并且句柄中有磁盘路径，则使用句柄中的路径
        diskPath = h->disk_top;
    } else {
        // 如果上述都没有，则使用活动磁盘
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            diskPath = diskManager->getActiveDiskPath();
        } else {
            // 没有磁盘管理器时，不使用默认路径
            diskPath = "";
            LogE("无法获取 DiskManager 单例实例，无法获取磁盘路径");
        }
    }
    
    // 检查磁盘是否已挂载
    bool isMounted = true;
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager) {
        isMounted = diskManager->isMounted(diskPath);
    }
    
    if (!isMounted) {
        LogE("磁盘未挂载，无法打开回放: %s", diskPath.c_str());
        return NULL;
    }

	// 获取数据库文件路径
	// 修改路径格式为 磁盘路径/年月日/设备ID/index.db
    snprintf(path, sizeof(path), "%s%04d%02d%02d/%d/index.db", 
             diskPath.c_str(), 
             stTimeDay->year, stTimeDay->month, stTimeDay->day, 
             play_chn);
    LogI("使用磁盘路径: %s, 数据库路径: %s", diskPath.c_str(), path);
	
	LogI("open index: %s, ltime:%lld, %04d%02d%02d %02d:%02d:%02d", path,
		ltime, stTimeDay->year, stTimeDay->month, stTimeDay->day, stTimeDay->hour, stTimeDay->minute, stTimeDay->second);
	
	mfile_new_dbfile(path);

	LogE("path = %s", path);
	
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{		
		T_ONE_REC	one_rec;
		UINT32		fseq = 0;
		LPSTR		tmp_str;
		INT32		tmp_value;

		// 直接读某个文件
		if (read_next) {
			fseq = h->fseq_day + 1;
			if (fseek(pf, fseq*sizeof(one_rec), SEEK_SET) != 0) {
				LOGE("%s(), fseek error, errno=%d", __FUNCTION__, errno);
				fclose(pf);
				return NULL;
			}
		}
		
		for ( ;fread(&one_rec, sizeof(one_rec), 1, pf) EQU 1; fseq++)
		{		
			//LOGI("one_rec, begtime:%ld, endtime:%ld, flag:%d, rectype:%d, filename:%s, idx_name:%s", 
			//	 one_rec.st_time, one_rec.end_time, one_rec.flag, one_rec.rec_type, one_rec.file_name, one_rec.idx_name);
			
			// 判断是否正常
			if (one_rec.flag != OR_FLAG_OK) {
				LogE("未录制完成, one_rec.flag = %d, file = %s", one_rec.flag, one_rec.file_name);
				continue;
			}

			UINT8	find_rec = FALSE;
			if (QfLeftRange(ltime, one_rec.st_time, one_rec.end_time)) 
			{
				T_ONE_REC	next_one_rec;
				
				find_rec = TRUE;
				do {
					if (fread(&next_one_rec, sizeof(next_one_rec), 1, pf) EQU 1) {
						if (QfLeftRange(ltime, next_one_rec.st_time, next_one_rec.end_time)) {
							memcpy(&one_rec, &next_one_rec, sizeof(next_one_rec));
							break;
						}
					}
					fseek(pf, -1*(LONG)sizeof(one_rec), SEEK_CUR);
				} while (FALSE);
			}
			
			// 找到合适的记录了, 或是找到大于此记录的文件
			if (find_rec
				|| one_rec.st_time > ltime
				|| read_next) {

				LogI("找到记录了 开始播放 %s, %s", one_rec.file_name, one_rec.idx_name);

				// 不复用句柄
				if (!read_next) {
					h = (T_MDISK_PLAY_HADLE)malloc(sizeof(T_MDISK_PLAY_CONTEXT));
					bzero(h, sizeof(T_MDISK_PLAY_CONTEXT));
					memcpy(&h->stTimeDay, stTimeDay, sizeof(h->stTimeDay));
                    // 保存磁盘路径到句柄中，方便后续使用
                    strncpy(h->disk_top, diskPath.c_str(), sizeof(h->disk_top)-1);
				}		
				
				h->fidx = fopen(one_rec.idx_name, "rb");
				if (!h->fidx) {
			        LogE("fidx open Err!! errno = %d", errno);
			        return NULL;
			    }
				
   	 			h->fvideo = fopen(one_rec.file_name, "rb");
				if (!h->fvideo) {
			        LogE("fvideo open Err!! errno = %d", errno);
			        return NULL;
			    }

				h->fseq_day = fseq;			// 文件序号
				h->f_st_time = one_rec.st_time;
				h->with_tz = with_tz;

				break;
			}
			else {
				// LOGI("%s(), not in range, seektime:%ld", __FUNCTION__, ltime);
				;
			}
			
		}
	} 
	else {
		LOGE("%s() error, file not exist: %s", __FUNCTION__, path);
	}

	CloseFileAndNull(pf);

	if (h) 
		return h;
		
	return NULL;
}


/**
 * 读数据
 * @param  h        上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  buf      缓冲
 * @param  buf_len  缓冲大小
 * @param  auto_next_file  自动读取下一个文件
 * @return          返回读取到的数据大小；失败返回0
 */
INT32 PlaybackManager::mdiskPlayReadFrame(T_MDISK_PLAY_HADLE h, INT32 play_chn, CHAR *buf, UINT32 buf_len, UINT8 auto_next_file)
{
    LONG 	read_len;
    INT32 	is_key_frame;
    LONG	frame_num;
    INT32	fnRet;

    UINT32 read_size = 0;
    UINT32 last_timestamp = 0;
    INT32  time_interval = 40; // 默认为25fps (1000/25)
    time_t current_time = 0;   // 当前播放时间
    
    T_INDEX_ENTRY index_entry;       // 录像文件索引
    T_FRAME_HEADER hdr;              // 录像头
    
    // 检查参数
    if (h == NULL || h->fidx == NULL || h->fvideo == NULL) {
        LogE("参数错误: 无效的播放句柄");
        return 0;
    }

    // 保存当前文件位置，以便在出错时恢复
    long idx_pos = ftell(h->fidx);
    long video_pos = ftell(h->fvideo);

    // 读取索引条目
    if (fread(&index_entry, sizeof(index_entry), 1, h->fidx) != 1) {
        // 如果读取索引失败，可能是文件结束或索引损坏
        LogW("读取索引条目失败，可能已到文件末尾");
        
        if (!auto_next_file) {
            return 0; // 不自动切换到下一个文件
        }
        
        // 尝试打开下一个文件
        mdiskPlayClose(h, FALSE);

        // 直接使用当前句柄中的磁盘路径
        if (mdiskPlayOpen(h, play_chn, NULL, h->with_tz, h->disk_top) == NULL) {
            LogE("无法打开下一个文件");
            return 0;
        }

        // 递归调用读取下一个文件的第一帧
        return mdiskPlayReadFrame(h, play_chn, buf, buf_len, auto_next_file);
    }
    
    // 更新当前播放时间
    current_time = index_entry.utc_time;
    
    // 确保视频文件指针正确定位到索引指定的位置
    if (ftell(h->fvideo) != index_entry.file_offset) {
        LogW("视频文件指针不同步，重新定位: 当前=%ld, 应为=%u", 
             ftell(h->fvideo), index_entry.file_offset);
        fseek(h->fvideo, index_entry.file_offset, SEEK_SET);
    }
    
    // 读取帧头
    if (fread(&hdr, sizeof(hdr), 1, h->fvideo) != 1) {
        LogE("读取帧头失败: offset=%u", index_entry.file_offset);
        
        // 尝试恢复文件同步
        fseek(h->fidx, idx_pos, SEEK_SET);
        fseek(h->fvideo, video_pos, SEEK_SET);
        
        return 0;
    }
    
    // 验证帧头和索引条目的一致性
    if (hdr.size != index_entry.frame_size || 
        hdr.frameType != index_entry.frame_type) {
        LogE("帧头与索引条目不一致: 帧大小=%u/%u, 帧类型=%d/%d", 
             hdr.size, index_entry.frame_size,
             hdr.frameType, index_entry.frame_type);
        
        // 尝试恢复文件同步 - 更强大的恢复机制
        LogW("尝试重新同步索引和视频文件...");
        
        // 先回退到原始位置
        fseek(h->fidx, idx_pos, SEEK_SET);
        fseek(h->fvideo, video_pos, SEEK_SET);
        
        // 如果是I帧不一致，可能是严重错误，尝试重新定位到下一个I帧
        if (index_entry.frame_type == IPC_FRAME_FLAG_IFRAME) {
            // 寻找下一个I帧
            LogW("寻找下一个I帧...");
            while (fread(&index_entry, sizeof(index_entry), 1, h->fidx) == 1) {
                if (index_entry.frame_type == IPC_FRAME_FLAG_IFRAME) {
                    fseek(h->fvideo, index_entry.file_offset, SEEK_SET);
                    // 回退一个索引条目，以便下次读取
                    fseek(h->fidx, -((long)sizeof(index_entry)), SEEK_CUR);
                    LogW("找到下一个I帧，已重新定位");
                    return 0; // 返回0，让调用者重新尝试读取
                }
            }
        }
        
        return 0;
    }
    
    // 检查缓冲区大小是否足够
    if (index_entry.frame_size > buf_len) {
        LogE("缓冲区不够，需要 %u 字节，但只有 %u 字节", index_entry.frame_size, buf_len);
        
        // 尝试恢复文件同步
        fseek(h->fidx, idx_pos, SEEK_SET);
        fseek(h->fvideo, video_pos, SEEK_SET);
        
        return 0;
    }

    // 读取帧数据
    read_size = fread(buf, 1, index_entry.frame_size, h->fvideo);
    if (read_size != index_entry.frame_size) {
        LogE("帧数据读取不完整: 期望=%u, 实际=%d", index_entry.frame_size, read_size);
        
        // 尝试恢复文件同步
        fseek(h->fidx, idx_pos, SEEK_SET);
        fseek(h->fvideo, video_pos, SEEK_SET);
        
        return 0;
    }

    // 更新上下文信息
    memcpy(&h->hdr, &hdr, sizeof(hdr));
    memcpy(&h->index_entry, &index_entry, sizeof(index_entry));
    
    // 返回读取的数据大小
    return read_size;
}



/**
 * 定位到指定时间
 * @param handle     播放句柄
 * @param time       定位时间
 * @return           成功返回TRUE，失败返回FALSE
 */
BOOL PlaybackManager::mdiskPlaySeek(T_MDISK_PLAY_HADLE handle, UINT32 time)
{
    if (handle == NULL || handle->fidx == NULL || handle->fvideo == NULL) {
        LogE("播放句柄无效");
        return FALSE;
    }
    
    LogI("开始定位: 目标时间=%u", time);
    
    // 保存当前文件位置
    long idx_pos = ftell(handle->fidx);
    long video_pos = ftell(handle->fvideo);
    
    // 重置文件指针到开头
    fseek(handle->fidx, 0, SEEK_SET);
    
    // 读取索引条目并查找最接近的I帧
    T_INDEX_ENTRY idx_entry;
    BOOL found = FALSE;
    time_t closest_time = 0;
    time_t target_time = time;
    long target_idx_pos = 0;
    
    // 查找最接近的I帧
    while (fread(&idx_entry, sizeof(idx_entry), 1, handle->fidx) == 1) {
        // 记录当前索引文件位置
        long current_idx_pos = ftell(handle->fidx);
        
        // 如果是I帧且时间小于等于目标时间，更新找到的位置
        if (idx_entry.frame_type == IPC_FRAME_FLAG_IFRAME) {
            if (idx_entry.utc_time <= target_time) {
                // 更新找到的最佳位置
                closest_time = idx_entry.utc_time;
                target_idx_pos = current_idx_pos - sizeof(idx_entry); // 回退一个索引条目
                found = TRUE;
            } else if (found) {
                // 已经找到一个小于等于目标时间的I帧，且当前I帧大于目标时间，结束搜索
                break;
            }
        }
    }
    
    if (!found) {
        LogE("未找到合适的I帧，定位失败");
        // 恢复原始文件位置
        fseek(handle->fidx, idx_pos, SEEK_SET);
        fseek(handle->fvideo, video_pos, SEEK_SET);
        return FALSE;
    }
    
    // 定位到找到的I帧位置
    fseek(handle->fidx, target_idx_pos, SEEK_SET);
    
    // 读取索引条目以获取视频文件偏移
    if (fread(&idx_entry, sizeof(idx_entry), 1, handle->fidx) != 1) {
        LogE("读取I帧索引条目失败");
        // 恢复原始文件位置
        fseek(handle->fidx, idx_pos, SEEK_SET);
        fseek(handle->fvideo, video_pos, SEEK_SET);
        return FALSE;
    }
    
    // 确认是否为I帧
    if (idx_entry.frame_type != IPC_FRAME_FLAG_IFRAME) {
        LogE("定位到的不是I帧，frame_type=%d", idx_entry.frame_type);
        // 恢复原始文件位置
        fseek(handle->fidx, idx_pos, SEEK_SET);
        fseek(handle->fvideo, video_pos, SEEK_SET);
        return FALSE;
    }
    
    // 定位视频文件到I帧位置
    fseek(handle->fvideo, idx_entry.file_offset, SEEK_SET);
    
    // 将文件指针回退到索引条目位置，以便下一次读取
    fseek(handle->fidx, target_idx_pos, SEEK_SET);
    
    LogI("定位成功: 目标时间=%u, 实际时间=%ld, 视频偏移=%u", 
         time, idx_entry.utc_time, idx_entry.file_offset);
    
    return TRUE;
}


/**
* 关闭句柄并释放资源
* @param  h 	   上下文句柄
* @param  free_p   释放资源标记
*/
VOID PlaybackManager::mdiskPlayClose(T_MDISK_PLAY_HADLE handle, UINT8 free_p)
{
    if (!handle) {
        return;
    }

	CloseFileAndNull(handle->fvideo);
	CloseFileAndNull(handle->fidx);

    if (free_p)
		free(handle);

    LogI("播放句柄已关闭");
}




// 去掉时间中间的冒号
void remove_colons(const char *src, char *dest) 
{
    int j = 0;
    for (int i = 0; src[i] != '\0'; i++) {
        if (src[i] != ':') {
            dest[j++] = src[i];
        }
    }
    dest[j] = '\0';
}



// 将 20250626 和 "HH:MM:SS" 转换为 UTC 时间戳
time_t mdisk_convert_utc(LPCSTR date, LPCSTR time_str)
{
    if (!date || !time_str || strlen(date) != 8) {
        return -1;
    }

    struct tm tm_time;
    memset(&tm_time, 0, sizeof(tm_time));

    // 解析年月日
    CHAR year_str[5] = {0};
    CHAR month_str[3] = {0};
    CHAR day_str[3] = {0};

    strncpy(year_str, date, 4);
    strncpy(month_str, date + 4, 2);
    strncpy(day_str, date + 6, 2);

    tm_time.tm_year = atoi(year_str) - 1900;
    tm_time.tm_mon  = atoi(month_str) - 1;
    tm_time.tm_mday = atoi(day_str);

    // 解析时间 HH:MM:SS
    int hour = 0, min = 0, sec = 0;
    if (sscanf(time_str, "%d:%d:%d", &hour, &min, &sec) != 3) {
        return -1;
    }

    tm_time.tm_hour = hour;
    tm_time.tm_min  = min;
    tm_time.tm_sec  = sec;

    // 北京时间 -> UTC：减去 8 小时
    time_t local_ts = mktime(&tm_time);
    if (local_ts == -1) {
        return -1;
    }


	return local_ts;

	// return local_ts + 8 * 3600;
}


// 添加录像数据库查询函数 (multiset 版本)
// 为T_PLAYBACK_FILE_TIME定义比较运算符，用于multiset排序
bool operator<(const T_PLAYBACK_FILE_TIME& a, const T_PLAYBACK_FILE_TIME& b) {
    // 使用time_t进行排序
    return a.st_time < b.st_time;
}

BOOL PlaybackManager::mdiskRecDbQuery(LPCSTR date, INT32 chn, std::multiset<T_PLAYBACK_FILE_TIME>& file_list)
{
    file_list.clear();
    
    // 记录数量
    int record_count = 0;
    
    // 获取DISK_MAPPING中定义的所有挂载点
    MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
    
    // 遍历所有磁盘
    for (int i = 0; i < MAX_DISK_COUNT; i++) {
        if (disks[i].mountpoint && *disks[i].mountpoint) {
            std::string diskPath = disks[i].mountpoint;
            
            // 确保路径末尾有斜杠
            if (!diskPath.empty() && diskPath.back() != '/') {
                diskPath += '/';
            }
            
            // 检查是否已挂载（如果磁盘管理器已初始化）
            bool isMounted = true;
            DiskManager* diskManager = DiskManager::getInstance();
            if (diskManager) {
                isMounted = diskManager->isMounted(diskPath);
            }
            
            if (!isMounted) {
                LogW("磁盘未挂载，跳过: %s", diskPath.c_str());
                continue;
            }
            
            
            // 构建数据库路径 disk_path/date/chn/index.db
            char db_path[256];
            snprintf(db_path, sizeof(db_path), "%s%s/%d/%s", 
                    diskPath.c_str(), date, chn, REC_DB_INDEX);
            
            LogI("查询录像数据库: %s", db_path);
            
            // 检查文件是否存在
            FILE *pf = fopen(db_path, "rb");
            if (pf == NULL) {
                LogW("未找到录像数据库文件: %s", db_path);
                continue;
            }
            
            T_ONE_REC one_rec;
            
            
            // 读取记录并添加到multiset中
            while (fread(&one_rec, sizeof(T_ONE_REC), 1, pf) == 1) 
            {
                // 跳过无效或已删除的记录
                if (one_rec.flag != OR_FLAG_OK) {
                    continue;
                }
                
                // 转换时间戳为可读时间格式
                char st_time_str[32] = {0};
                char duration_str[32] = {0};
                
                struct tm* tm_info;
                if (one_rec.st_time > 0) {
                    tm_info = localtime(&one_rec.st_time);
                    strftime(st_time_str, sizeof(st_time_str), "%Y-%m-%d %H:%M:%S", tm_info);
                } else {
                    strcpy(st_time_str, "未开始");
                    continue; // 跳过无效开始时间的记录
                }
                
                T_PLAYBACK_FILE_TIME file_time;
                memset(&file_time, 0, sizeof(T_PLAYBACK_FILE_TIME));
                
                // 计算录像时长（如果已结束）
                if (one_rec.end_time > one_rec.st_time) {
                    int duration_seconds = (int)(one_rec.end_time - one_rec.st_time);
                    int hours = duration_seconds / 3600;
                    int minutes = (duration_seconds % 3600) / 60;
                    int seconds = duration_seconds % 60;
                    snprintf(duration_str, sizeof(duration_str), "%02d:%02d:%02d", 
                            hours, minutes, seconds);
                    
                    // 设置time_t格式的时间
                    file_time.st_time = one_rec.st_time;
                    file_time.end_time = one_rec.end_time;
                } else {
                    strcpy(duration_str, "未知");
                    
                    // 设置time_t格式的时间 (结束时间可能无效)
                    file_time.st_time = one_rec.st_time;
                    file_time.end_time = one_rec.st_time; // 使用开始时间作为结束时间的默认值
                }
                
                // 从文件名路径中提取时间部分
                const char* filename = one_rec.file_name;
                const char* time_part = strrchr(filename, '/');
                
                if (time_part && strlen(time_part) > 1) {
                    // 跳过斜杠 '/'
                    time_part++;
                    
                    // 提取时间部分 (前6位数字)
                    char raw_time[7] = {0};
                    strncpy(raw_time, time_part, 6);
                    
                    // 格式化为 HH:MM:SS 保存到file_crop_name
                    if (strlen(raw_time) == 6) {
                        snprintf(file_time.file_crop_name, sizeof(file_time.file_crop_name), "%c%c:%c%c:%c%c",
                                raw_time[0], raw_time[1], raw_time[2], raw_time[3], raw_time[4], raw_time[5]);
                    }
                }
                
                // 如果未能从文件名提取时间，则使用开始时间
                if (strlen(file_time.file_crop_name) == 0) {
                    struct tm* tm_info = localtime(&one_rec.st_time);
                    if (tm_info) {
                        strftime(file_time.file_crop_name, sizeof(file_time.file_crop_name), "%H:%M:%S", tm_info);
                    }
                }
                
                // 保存磁盘路径 - 使用当前遍历的磁盘路径
                s_strcpy(file_time.disk_top, diskPath.c_str());
                
                // 添加到multiset中 - 自动按时间排序
                file_list.insert(file_time);
                
                //LogW("disk_top = %s, st_time = %ld, end_time = %ld", file_time.disk_top, file_time.st_time, file_time.end_time);
                
                record_count++;
            }
            
            fclose(pf);
            LogI("----- 磁盘 %s 录像数据库内容结束，找到 %d 条记录 -----", diskPath.c_str(), record_count);
        }
    }
    
    return (record_count > 0);
}




// ========== PlaybackManager 回放控制方法实现 ==========

INT32 PlaybackManager::startPlayback(INT32 chn, UINT32 start_time, UINT32 end_time, INT32 speed, const CHAR* disk_top)
{
    ScopedLocker lock(m_managerMutex);

    // 检查回放模块是否已初始化
    if (!m_moduleInitialized) {
        LogE("回放模块未初始化，请先调用 playback_module_initialize()");
        return -1;
    }

    // 初始化回放控制
    initPlaybackCtrl();

    // 停止当前回放
    stopPlayback();
    
    // 创建新的回放会话
    PlaybackSession* session = createSession();
    if (!session) {
        LogE("无法创建回放会话: chn=%d", chn);
        return -1;
    }

    // 启动回放会话
    if (!session->startSession(chn, start_time, end_time, speed, disk_top)) {
        LogE("启动回放会话失败: chn=%d", chn);
        destroySession(session);
        return -1;
    }

    // 记录当前回放通道
    m_currentPlaybackChannel = chn;

    LogI("启动回放成功: chn=%d", chn);
    return 0;
}

VOID PlaybackManager::stopPlayback()
{
    ScopedLocker lock(m_managerMutex);

    // 检查回放模块是否已初始化
    if (!m_moduleInitialized) {
        LogE("回放模块未初始化，请先调用 playback_module_initialize()");
        return;
    }

    // 检查是否有活动的回放
    if (m_currentPlaybackChannel < 0) {
		LogE("无回放!");
        return;
    }

    LogI("停止所有回放会话");

    // 停止所有活动的回放会话
    destroyAllSessions();

    m_currentPlaybackChannel = -1;
}

VOID PlaybackManager::pausePlayback(BOOL pause)
{
    ScopedLocker lock(m_managerMutex);

    // 检查是否有活动的回放
    if (m_currentPlaybackChannel < 0) {
		LogE("无回放!");
        return;
    }

    LogI("%s所有回放会话", pause ? "暂停" : "恢复");

    // 暂停/恢复所有活动的回放会话
    for (auto& pair : m_sessions) {
        PlaybackSession* session = pair.second;
        if (session && session->isRunning()) {
            session->pauseSession(pause);
        }
    }
}

// 旧的设置回放速度函数 - 已被 PlaybackManager::setPlaybackSpeed 替代
// 保留用于兼容性，但实际调用新的实现
VOID set_playback_speed(INT32 speed)
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (manager) {
        manager->setPlaybackSpeed(speed);
    }
}

// 旧的定位回放函数 - 已被 PlaybackManager::seekPlaybackToTime 替代
// 保留用于兼容性，但实际调用新的实现
BOOL seek_playback_to_time(UINT32 time)
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        return FALSE;
    }
    return manager->seekPlaybackToTime(time);
}

// 旧的获取当前回放时间函数 - 已被 PlaybackManager::getPlaybackCurrentTime 替代
// 保留用于兼容性，但实际调用新的实现
UINT32 get_playback_current_time()
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        return 0;
    }
    return manager->getPlaybackCurrentTime();
}

INT32 mdisk_playback_test(INT32 chn, UINT32 start_time, UINT32 end_time, UINT8 event_type, const CHAR* disk_top)
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取 PlaybackManager 实例");
        return -1;
    }
    return manager->mdiskPlaybackTest(chn, start_time, end_time, event_type, disk_top);
}


/**
 * 旧的导出录像文件函数 - 已被 ExportManager::exportRecordToAvi 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
BOOL export_record_to_avi(INT32 chn, const CHAR* source_file, const CHAR* target_file, const CHAR* disk_path)
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        LogE("无法获取 ExportManager 实例");
        return FALSE;
    }
    return manager->exportRecordToAvi(chn, source_file, target_file, disk_path);

}

/**
 * 旧的按时间导出录像文件函数 - 已被 ExportManager::exportRecordToAviByTime 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
BOOL export_record_to_avi_by_time(INT32 chn, UINT32 start_time, UINT32 end_time, const CHAR* target_file, const CHAR* disk_path)
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        LogE("无法获取 ExportManager 实例");
        return FALSE;
    }
    return manager->exportRecordToAviByTime(chn, start_time, end_time, target_file, disk_path);

}

/**
 * 旧的检查导出状态函数 - 已被 ExportManager::isExportRunning 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
BOOL is_export_running()
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        return FALSE;
    }
    return manager->isExportRunning();
}

/**
 * 旧的取消导出函数 - 已被 ExportManager::cancelExport 替代
 * 保留用于向后兼容，但实际调用新的实现
 */
VOID cancel_export()
{
    ExportManager* manager = ExportManager::getInstance();
    if (manager) {
        manager->cancelExport();
    }
}


/**
 * 回放模块顶层初始化函数
 * 供主程序启动时调用，其他模块不得调用
 *
 * @return true 初始化成功, false 初始化失败
 */
bool playback_module_initialize()
{
    LogI("======== 回放模块顶层初始化开始 ========");

    // 初始化 PlaybackManager
    PlaybackManager* playbackManager = PlaybackManager::getInstance();
    if (!playbackManager) {
        LogE("无法获取 PlaybackManager 实例");
        return false;
    }

    if (!playbackManager->initializeModule()) {
        LogE("PlaybackManager 初始化失败");
        return false;
    }

    // 初始化 ExportManager
    ExportManager* exportManager = ExportManager::getInstance();
    if (!exportManager) {
        LogE("无法获取 ExportManager 实例");
        return false;
    }

    LogI("回放模块初始化成功");
    LogI("最大解码通道数: %d", MAX_DECODE_CHANNELS);
    LogI("======== 回放模块顶层初始化结束 ========");

    return true;
}

/**
 * 回放模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void playback_module_cleanup()
{
    LogI("======== 回放模块顶层清理开始 ========");

    // 清理 PlaybackManager
    PlaybackManager* playbackManager = PlaybackManager::getInstance();
    if (playbackManager) {
        playbackManager->cleanupModule();
        PlaybackManager::destroyInstance();
    }

    // 清理 ExportManager
    ExportManager* exportManager = ExportManager::getInstance();
    if (exportManager) {
        exportManager->cancelExport();
        ExportManager::destroyInstance();
    }

    LogI("回放模块清理完成");
    LogI("======== 回放模块顶层清理结束 ========");
}

/**
 * 检查回放模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool playback_module_is_initialized()
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    return manager ? manager->isModuleInitialized() : false;
}


// 兼容性接口：停止回放
VOID stop_playback()
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (manager) {
        manager->stopPlayback();
    }
}

// 兼容性接口：暂停/恢复回放
VOID pause_playback(BOOL pause)
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (manager) {
        manager->pausePlayback(pause);
    }
}

// ========== 兼容性接口函数 ==========
BOOL mdisk_rec_db_query(LPCSTR date, INT32 chn, std::multiset<T_PLAYBACK_FILE_TIME>& file_list)
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        LogE("无法获取 PlaybackManager 实例");
        return FALSE;
    }
    return manager->mdiskRecDbQuery(date, chn, file_list);
}

// 兼容性接口：解码帧数据
BOOL mdisk_play_decode_frame(INT32 decode_chn, UINT8* frame_data, UINT32 frame_size, UINT32 timestamp, INT32 codec)
{
    if (!frame_data || frame_size == 0) {
        LogE("无效的帧数据参数");
        return FALSE;
    }

    // 设置编解码器协议
    if (codec == MEDIA_CODEC_VIDEO_HEVC) {
        vs_hisi_vdec_set_protocol(decode_chn, TRUE);  // H265
    } else if (codec == MEDIA_CODEC_VIDEO_H264) {
        vs_hisi_vdec_set_protocol(decode_chn, FALSE); // H264
    }

    // 发送帧数据到解码器
    INT32 result = vs_hisi_vdec_send_frame(decode_chn, decode_chn, (CHAR*)frame_data,
                                          frame_size, timestamp, FALSE, FALSE);

    return (result == 0) ? TRUE : FALSE;
}

// 兼容性接口：检查回放是否正在运行
BOOL is_playback_running()
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        return FALSE;
    }

    // 检查是否有活跃的回放会话
    return manager->hasActiveSession() ? TRUE : FALSE;
}

// 兼容性接口：获取回放状态
INT32 get_playback_status()
{
    PlaybackManager* manager = PlaybackManager::getInstance();
    if (!manager) {
        return -1; // 错误状态
    }

    if (manager->hasActiveSession()) {
        return 1; // 正在回放
    } else {
        return 0; // 停止状态
    }
}

// 兼容性接口：获取导出进度
float get_export_progress()
{
    ExportManager* manager = ExportManager::getInstance();
    if (!manager) {
        return 0.0f;
    }
    return manager->getExportProgress();
}



