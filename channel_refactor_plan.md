# ChannelManager 重构方案

## 1. 职责简化重构

### 原始问题
- Channel类承担过多职责
- 与其他4个模块都有强依赖
- 接口复杂，难以使用

### 重构方案：简化为协调器模式

```cpp
// 简化的通道接口
class SimpleChannel {
public:
    SimpleChannel(int channelId);
    ~SimpleChannel();
    
    // 核心功能接口（简化）
    bool startRecord();
    void stopRecord();
    bool pushFrame(const FrameData& frame);
    
    // 状态查询接口
    bool isRecording() const;
    int getChannelId() const { return m_channelId; }
    
private:
    int m_channelId;
    
    // 使用组合而非继承，降低耦合
    std::unique_ptr<SimpleRecordChannel> m_recorder;
    std::unique_ptr<SimpleBufferManager> m_buffer;
    
    // 移除对PlaybackManager和DiskManager的直接依赖
    // 通过全局单例访问
};

// 简化的通道管理器
class SimpleChannelManager {
public:
    static SimpleChannelManager* getInstance();
    
    // 简化的通道管理接口
    SimpleChannel* getChannel(int channelId);
    bool destroyChannel(int channelId);
    std::vector<int> getActiveChannels() const;
    
private:
    std::map<int, std::unique_ptr<SimpleChannel>> m_channels;
    mutable std::mutex m_mutex;
};
```

## 2. 依赖关系解耦

### 原始问题
- Channel直接依赖所有其他模块
- 模块间耦合度过高
- 难以独立测试和维护

### 重构方案：依赖注入和接口抽象

```cpp
// 抽象接口定义
class IRecorder {
public:
    virtual ~IRecorder() = default;
    virtual bool startRecord() = 0;
    virtual void stopRecord() = 0;
    virtual bool isRecording() const = 0;
};

class IBufferManager {
public:
    virtual ~IBufferManager() = default;
    virtual bool pushFrame(const FrameData& frame) = 0;
    virtual bool readFrame(FrameData& frame) = 0;
};

// 使用依赖注入的通道类
class DecoupledChannel {
public:
    DecoupledChannel(int channelId, 
                    std::unique_ptr<IRecorder> recorder,
                    std::unique_ptr<IBufferManager> buffer)
        : m_channelId(channelId)
        , m_recorder(std::move(recorder))
        , m_buffer(std::move(buffer)) {}
    
    bool startRecord() {
        return m_recorder->startRecord();
    }
    
    bool pushFrame(const FrameData& frame) {
        return m_buffer->pushFrame(frame);
    }
    
private:
    int m_channelId;
    std::unique_ptr<IRecorder> m_recorder;
    std::unique_ptr<IBufferManager> m_buffer;
};

// 工厂类负责创建和注入依赖
class ChannelFactory {
public:
    static std::unique_ptr<DecoupledChannel> createChannel(int channelId) {
        auto recorder = std::make_unique<SimpleRecordChannel>(channelId);
        auto buffer = std::make_unique<SimpleBufferManager>(channelId);
        
        return std::make_unique<DecoupledChannel>(channelId, 
                                                 std::move(recorder), 
                                                 std::move(buffer));
    }
};
```

## 3. 接口简化

### 原始问题
- 接口方法过多
- 参数复杂
- 使用困难

### 重构方案：最小化接口设计

```cpp
// 统一的帧数据结构
struct FrameData {
    uint32_t streamType;
    uint32_t frameType;
    uint32_t timestamp;
    uint32_t size;
    std::vector<uint8_t> data;
    
    // 便利构造函数
    FrameData(uint32_t stream, const T_FRAME_HEADER* header, const void* frameData)
        : streamType(stream)
        , frameType(header->frameType)
        , timestamp(header->timeStamp)
        , size(header->size)
        , data(static_cast<const uint8_t*>(frameData), 
               static_cast<const uint8_t*>(frameData) + header->size) {}
};

// 最小化的通道接口
class MinimalChannel {
public:
    MinimalChannel(int channelId) : m_channelId(channelId) {}
    
    // 只保留最核心的3个接口
    bool start() { return m_recorder.start(); }
    void stop() { m_recorder.stop(); }
    bool pushFrame(const FrameData& frame) { return m_buffer.pushFrame(frame); }
    
    // 状态查询
    bool isActive() const { return m_recorder.isRecording(); }
    
private:
    int m_channelId;
    SimpleRecordChannel m_recorder{m_channelId};
    SimpleBufferManager m_buffer{m_channelId};
};

// 最小化的管理器接口
class MinimalChannelManager {
public:
    static MinimalChannelManager& getInstance() {
        static MinimalChannelManager instance;
        return instance;
    }
    
    // 只保留最核心的接口
    MinimalChannel* getChannel(int channelId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_channels.find(channelId);
        if (it == m_channels.end()) {
            auto channel = std::make_unique<MinimalChannel>(channelId);
            auto* ptr = channel.get();
            m_channels[channelId] = std::move(channel);
            return ptr;
        }
        return it->second.get();
    }
    
private:
    std::map<int, std::unique_ptr<MinimalChannel>> m_channels;
    std::mutex m_mutex;
};
```

## 4. 配置管理简化

### 原始问题
- 预录像参数分散
- 配置接口复杂
- 参数验证不统一

### 重构方案：配置对象模式

```cpp
// 统一的通道配置
struct ChannelConfig {
    bool preRecordEnabled{false};
    int preRecordDuration{10};
    int preRecordBufferSize{300};
    uint32_t streamMask{0x01};
    
    // 配置验证
    bool isValid() const {
        return preRecordDuration > 0 && 
               preRecordDuration <= 60 &&
               preRecordBufferSize > 0;
    }
    
    // 默认配置
    static ChannelConfig getDefault() {
        return ChannelConfig{};
    }
};

// 配置化的通道类
class ConfigurableChannel {
public:
    ConfigurableChannel(int channelId, const ChannelConfig& config = ChannelConfig::getDefault())
        : m_channelId(channelId), m_config(config) {
        
        if (!m_config.isValid()) {
            throw std::invalid_argument("Invalid channel configuration");
        }
    }
    
    bool start() {
        if (m_config.preRecordEnabled) {
            return startWithPreRecord();
        } else {
            return startNormal();
        }
    }
    
    void updateConfig(const ChannelConfig& config) {
        if (config.isValid()) {
            m_config = config;
        }
    }
    
private:
    int m_channelId;
    ChannelConfig m_config;
    
    bool startNormal() { /* 普通录像启动逻辑 */ return true; }
    bool startWithPreRecord() { /* 预录像启动逻辑 */ return true; }
};
```

## 5. 重构实施步骤

### 第一阶段：接口简化（1天）
1. 定义FrameData统一数据结构
2. 创建MinimalChannel接口
3. 保持向后兼容

### 第二阶段：依赖解耦（2天）
1. 定义抽象接口
2. 实现依赖注入
3. 创建工厂类

### 第三阶段：配置管理（1天）
1. 实现ChannelConfig
2. 统一配置验证
3. 简化配置接口

### 第四阶段：管理器简化（1天）
1. 简化ChannelManager
2. 减少管理接口
3. 优化资源管理

## 6. 预期效果

### 代码量减少
- 原始：478行 → 重构后：约200行（减少58%）
- Channel类：200行 → 50行（减少75%）

### 复杂度降低
- 依赖关系：强耦合 → 接口抽象
- 接口数量：15个方法 → 3个核心方法
- 配置管理：分散参数 → 统一配置对象

### 可维护性提升
- 职责单一，易于理解
- 依赖解耦，易于测试
- 接口简化，易于使用
