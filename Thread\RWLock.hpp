/*******************************************************************************
*
* 功能：  实现在对代码段或是数据加锁
*
* 说明：  对于函数的调用：in表示传入,out表示数据带出
*		  不建议直接使用函数Enter与Leave，直接使用ScopedLocker定议变量的方式	
*
* 注意:	  多个线程可以同时读(包括递归读)，但同时只有一个线程可以写(包括递归读)
*		  相当于 [读写锁] + [递归锁]
*		  小心在写锁中调用读锁(会死锁)，此情况应修改代码设计。
*
* 作者：  徐辉  
* 
*******************************************************************************/
#ifndef RW_LOCK_HPP
#define RW_LOCK_HPP

#include "MyTypes.h"
#include "Lock.hpp"

//==============================================================================
//								读写锁
//==============================================================================

class TRWLock
{
private:

	pthread_rwlock_t	m_tRWLockData;	
	pthread_t			m_tThreadData;

public:

	TRWLock()
	{
		pthread_rwlockattr_t	tRWLockAttr;

		//m_tRWLockData = PTHREAD_RWLOCK_INITIALIZER;
		pthread_rwlockattr_init(&tRWLockAttr);
		pthread_rwlock_init(&m_tRWLockData, &tRWLockAttr);		
		m_tThreadData = 0;
	}

	~TRWLock()
	{
		pthread_rwlock_destroy(&m_tRWLockData);
	}

	void LockRead(void)
	{
		pthread_rwlock_rdlock(&m_tRWLockData);	
		m_tThreadData = ::pthread_self();
	}

	void UnlockRead(void)
	{
		m_tThreadData = 0;
		pthread_rwlock_unlock(&m_tRWLockData);	
	}

	void LockWrite(void)
	{
		pthread_rwlock_wrlock(&m_tRWLockData);	
		m_tThreadData = ::pthread_self();
	}

	void UnlockWrite(void)
	{
		m_tThreadData = 0;
		pthread_rwlock_unlock(&m_tRWLockData);	
	}

	bool IsSameHolder(void)
	{
		if (m_tThreadData EQU 0)
		{
			return false;
		}
		return pthread_equal(m_tThreadData, ::pthread_self()) != 0 ? true : false;
	}

};
//==============================================================================
//								读区域锁
//==============================================================================

class ScopedReadLocker
{
public:

	explicit ScopedReadLocker(TRWLock &tALock): m_tLocker(tALock), m_bLockedRead(true)
	{
//		m_tLocker.LockRead(); ，下面版本暂时保留
		m_bLockedRead = !m_tLocker.IsSameHolder();
		if (m_bLockedRead)
		{
			m_tLocker.LockRead();
		}
	}

    // 增加 bIgnore是为了简单化程序(激活重载),暂时保留
    explicit ScopedReadLocker(TRWLock &tALock, bool bIgnore)
		: m_tLocker(tALock), m_bLockedRead(true)
	{
		UNUSED(bIgnore);
		m_bLockedRead = !m_tLocker.IsSameHolder();
		if (m_bLockedRead)
		{
			m_tLocker.LockRead();
		}
	}

	~ScopedReadLocker()
	{
		if (m_bLockedRead)
		{
			m_tLocker.UnlockRead();
		}		
	}

private:

	friend class ScopedWriteLocker;

	TRWLock		&m_tLocker;
	bool		m_bLockedRead;
};

//==============================================================================
//								写区域锁
//==============================================================================

class ScopedWriteLocker
{
public:

	explicit ScopedWriteLocker(TRWLock &tALock)
		: m_tLocker(tALock), m_bIsReader(false)
	{
		m_tLocker.LockWrite();
	}
	explicit ScopedWriteLocker(ScopedReadLocker &tReadLock)
		: m_tLocker(tReadLock.m_tLocker), m_bIsReader(true)
	{
		m_tLocker.UnlockRead();
		m_tLocker.LockWrite();
	}

	~ScopedWriteLocker()
	{
		m_tLocker.UnlockWrite();
		if (m_bIsReader)
		{
			m_tLocker.LockRead();
		}
	}

private:

	TRWLock		&m_tLocker;
	bool		m_bIsReader;

};


#endif
