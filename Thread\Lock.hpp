/*******************************************************************************
*
* 功能：  实现在对代码段或是数据加锁
*
* 说明：  对于函数的调用：in表示传入,out表示数据带出
*		  不建议直接使用函数Enter与Leave，直接使用ScopedLocker定议变量的方式	
*
* 作者：  徐辉  
* 
*******************************************************************************/

#ifndef LOCK_HPP
#define LOCK_HPP

#include <string.h>
#include "MyTypes.h"
#include "MacroFunc.h"
#ifdef LINUX
#include <pthread.h>
#include <time.h>
#include <sys/time.h>
#endif
#include "Debug/OutTrace.hpp"

class TLock
{
public:
	// 进入临界区
	virtual bool Enter(void) = 0;
	// 退出临界区
	virtual void Leave(void) = 0;
	// 是否处于锁定状态
	virtual bool IsLock(void) = 0;	
};

#ifdef LINUX
static pthread_mutexattr_t  *s_ptMutexAttr=NULL;
static pthread_once_t		s_tMutexAttrInit = PTHREAD_ONCE_INIT;
static void MutexAttrInit()
{
	if (s_ptMutexAttr EQU NULL)
	{
		s_ptMutexAttr = new pthread_mutexattr_t;
		::memset(s_ptMutexAttr, 0x00, sizeof(pthread_mutexattr_t));
		pthread_mutexattr_init(s_ptMutexAttr);
		pthread_mutexattr_settype(s_ptMutexAttr, PTHREAD_MUTEX_RECURSIVE);	// 这样可以自己不用检测是否死锁
		//pthread_mutexattr_settype(s_ptMutexAttr, PTHREAD_MUTEX_NORMAL);	// 递归时，如果下面的检测无效，将会死锁
	}
}
#endif

//==============================================================================
//								临界区锁
//==============================================================================
class TCSLock: public TLock
{
public:

	TCSLock()
	{
#ifdef WIN32
		m_lpCS =  new CRITICAL_SECTION;
		InitializeCriticalSection(m_lpCS);
#elif defined(LINUX)
    	if (s_ptMutexAttr EQU NULL)
    	{
    		(void)pthread_once(&s_tMutexAttrInit, MutexAttrInit);
    	}					
		pthread_mutex_init(&m_tMutex, s_ptMutexAttr);
#endif
	};    

	~TCSLock()
	{
#ifdef WIN32
		if (m_lpCS != NULL)
		{
			DeleteCriticalSection(m_lpCS);
            delete m_lpCS;
            m_lpCS = NULL;
		}	
#elif defined(LINUX)
		pthread_mutex_destroy(&m_tMutex);
#endif
	};


	// 进入临界区
	virtual bool Enter(void)
	{
#ifdef WIN32
		EnterCriticalSection(m_lpCS);
#elif defined(LINUX)
		
		if (pthread_mutex_lock(&m_tMutex) != 0)
		{
 			LOGE("Enter() error=%d*************** [%p] curr[%d]\n", 
 				errno, this, (int)::pthread_self());
			return false;
		}		
#endif

		return true;		
	};

	// 退出临界区
	virtual void Leave(void)
	{
#ifdef WIN32
		LeaveCriticalSection(m_lpCS);
#elif defined(LINUX)
  		(void)pthread_mutex_unlock(&m_tMutex);
#endif
	};

    // 是否处于锁定状态
    virtual bool IsLock(void)
    {
#ifdef WIN32
		if (m_lpCS->LockCount >= 0)
        {
			return true;
        }

        return false;
#elif defined(LINUX)
  		
		if (pthread_mutex_trylock(&m_tMutex) EQU 0)
		{
			this->Leave();
			return false;
		}
		return true;
#endif
    };

    // 尝试锁定
    virtual bool TryLock(void)
    {
		return (pthread_mutex_trylock(&m_tMutex) EQU 0);
    };

#if defined(LINUX)	

	// 尝试锁定
    bool TimeLock(UINT32 msec)
    {
		struct timespec time_out;
		
    	clock_gettime(CLOCK_REALTIME, &time_out);
	    time_out.tv_sec  += msec/1000;
		time_out.tv_nsec += (msec%1000)*1000*1000;
		
		return (pthread_mutex_timedlock(&m_tMutex, &time_out) EQU 0);
    }
	
#endif

private:

#ifdef WIN32
	LPCRITICAL_SECTION	m_lpCS;		// 临界区对像
#elif defined(LINUX)
	pthread_mutex_t		m_tMutex;	// 互斥	
	friend class TCondition;
#endif
};

//==============================================================================
//								互斥锁
//==============================================================================
#ifdef WIN32
class TMutexLock: public TLock
{
public:

#ifndef UNICODE
	TMutexLock(CHAR *pszName = NULL)
	{
		// 初始状态为可进入
		m_hMutexHdl = CreateMutexA(NULL, FALSE, pszName);
	};
#else
	TMutexLock(WCHAR *pszName = NULL)
	{
		// 初始状态为可进入
		m_hMutexHdl = CreateMutexW(NULL, FALSE, pszName);
	};
#endif	// #ifndef UNICODE
	
    ~TMutexLock()
    {
		if (IsLock())
        {
			// 退出互斥
			Leave();
        }

        // 关闭句柄
		if (m_hMutexHdl != NULL)
        {
			CloseHandle(m_hMutexHdl);
            m_hMutexHdl = NULL;
        }
    };

    // 进入互斥
    virtual void Enter(void)
    {
		WaitForSingleObject(m_hMutexHdl, INFINITE);
    };

    // 退出互斥
    virtual void Leave(void)
    {
		ReleaseMutex(m_hMutexHdl);
    };

    // 是否处于锁定状态,如果锁定和非锁定在同一线程中没有用
    virtual bool IsLock(void)
    {
		DWORD	dwFlag = 0;

        dwFlag = WaitForSingleObject(m_hMutexHdl, 0);
        if (dwFlag == WAIT_OBJECT_0
			|| dwFlag == WAIT_ABANDONED)
        {
			// 没有锁定			
			return FALSE;
        }

        // 锁定
        return TRUE;
    };

private:

	HANDLE		m_hMutexHdl;	// 互斥句柄
};
#endif

//==============================================================================
//								区域锁
//==============================================================================

class ScopedLocker
{
public:

	explicit ScopedLocker(TLock &tALock): m_tLocker(tALock), m_locked(false)
	{
		m_locked = m_tLocker.Enter();
	}

	~ScopedLocker()
	{
		if (m_locked)
		{
			m_tLocker.Leave();
		}
	}
	
	// 获取内部锁指针
	TLock* GetLockPtr() { return &m_tLocker; }

private:

	TLock&	m_tLocker;
	bool	m_locked;
};

//==============================================================================
//								自动锁
//==============================================================================

#ifdef LINUX

class AutoLocker
{
public:

	explicit AutoLocker(pthread_mutex_t *ptALock): m_ptMutex(ptALock)
	{
		pthread_mutex_lock(m_ptMutex);
	}

	~AutoLocker()
	{
		pthread_mutex_unlock(m_ptMutex);
	}

private:

	pthread_mutex_t	*m_ptMutex;
};

#endif

#endif
