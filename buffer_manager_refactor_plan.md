# BufferManager 重构方案

## 1. 接口统一重构

### 原始问题
- 读写接口设计不对称
- 参数计算逻辑复杂
- 错误处理不一致

### 重构方案：统一接口设计

```cpp
// 统一的缓冲区配置
struct BufferConfig {
    uint32_t frameCount{100};      // 缓存帧数
    uint32_t frameSize{1024*1024}; // 每帧大小
    uint32_t cacheDuration{10};    // 缓存时长(秒)
    
    // 根据码流类型自动计算配置
    static BufferConfig forStream(uint32_t streamType) {
        BufferConfig config;
        switch (streamType) {
            case 0: // 主码流
                config.frameCount = 100;
                config.frameSize = 1024*1024;
                break;
            case 1: // 子码流
                config.frameCount = 300;
                config.frameSize = 100*1024;
                break;
            default:
                config.frameCount = 50;
                config.frameSize = 200*1024;
                break;
        }
        return config;
    }
};

// 统一的缓冲区操作结果
enum class BufferResult {
    SUCCESS,
    BUFFER_FULL,
    BUFFER_EMPTY,
    INVALID_HANDLE,
    ALLOCATION_FAILED
};

// 简化的缓冲区管理器
class SimpleBufferManager {
public:
    SimpleBufferManager(int channelId);
    ~SimpleBufferManager();
    
    // 统一的写入接口
    BufferResult openWriter(uint32_t streamType);
    BufferResult writeFrame(uint32_t streamType, const T_FRAME_HEADER* header, const void* data);
    void closeWriter(uint32_t streamType);
    
    // 统一的读取接口
    BufferResult openReader(uint32_t streamType);
    BufferResult readFrame(T_FRAME_HEADER** header, char** data);
    void closeReader();
    
private:
    int m_channelId;
    std::map<uint32_t, HANDLE> m_writers;
    HANDLE m_reader{INVALID_HANDLE_VALUE};
    std::map<uint32_t, BufferConfig> m_configs;
};
```

## 2. 参数计算简化

### 原始问题
- getMbCacheNum函数逻辑复杂
- 硬编码的魔法数字
- 计算公式难以理解

### 重构方案：配置化参数管理

```cpp
// 缓冲区参数配置表
class BufferConfigTable {
public:
    struct StreamConfig {
        uint32_t frameRate;
        uint32_t bitrate;
        uint32_t frameSize;
        uint32_t cacheSeconds;
    };
    
    static const StreamConfig& getConfig(uint32_t streamType) {
        static const std::map<uint32_t, StreamConfig> configs = {
            {0, {25, 4*1024*1024, 1024*1024, 10}},  // 主码流：25fps, 4Mbps, 1MB/frame, 10s
            {1, {25, 1*1024*1024, 100*1024, 10}},   // 子码流：25fps, 1Mbps, 100KB/frame, 10s
            {2, {25, 512*1024, 50*1024, 10}}        // 第三码流：25fps, 512Kbps, 50KB/frame, 10s
        };
        
        auto it = configs.find(streamType);
        if (it != configs.end()) {
            return it->second;
        }
        
        // 默认配置
        static const StreamConfig defaultConfig = {25, 2*1024*1024, 200*1024, 10};
        return defaultConfig;
    }
    
    // 计算缓冲区参数
    static BufferConfig calculateBuffer(uint32_t streamType) {
        const auto& config = getConfig(streamType);
        
        BufferConfig bufferConfig;
        bufferConfig.frameCount = config.frameRate * config.cacheSeconds;
        bufferConfig.frameSize = config.frameSize;
        bufferConfig.cacheDuration = config.cacheSeconds;
        
        return bufferConfig;
    }
};
```

## 3. 错误处理统一

### 原始问题
- 错误处理分散在各个函数中
- 返回值类型不一致
- 错误信息不够详细

### 重构方案：统一错误处理机制

```cpp
// 错误信息结构
struct BufferError {
    BufferResult code;
    std::string message;
    int channelId;
    uint32_t streamType;
    
    BufferError(BufferResult c, const std::string& msg, int chn = -1, uint32_t stream = 0)
        : code(c), message(msg), channelId(chn), streamType(stream) {}
};

// 结果包装器
template<typename T>
class BufferResultWrapper {
public:
    BufferResultWrapper(T value) : m_value(std::move(value)), m_hasError(false) {}
    BufferResultWrapper(BufferError error) : m_error(std::move(error)), m_hasError(true) {}
    
    bool isSuccess() const { return !m_hasError; }
    const T& getValue() const { return m_value; }
    const BufferError& getError() const { return m_error; }
    
private:
    T m_value;
    BufferError m_error{BufferResult::SUCCESS, ""};
    bool m_hasError;
};

// 使用统一错误处理的缓冲区管理器
class RobustBufferManager {
public:
    BufferResultWrapper<HANDLE> openWriter(uint32_t streamType);
    BufferResultWrapper<bool> writeFrame(uint32_t streamType, const T_FRAME_HEADER* header, const void* data);
    BufferResultWrapper<FrameData> readFrame();
    
private:
    BufferError createError(BufferResult code, const std::string& operation, uint32_t streamType = 0);
};
```

## 4. 资源管理简化

### 原始问题
- 句柄管理分散
- 资源泄漏风险
- 初始化和清理逻辑复杂

### 重构方案：RAII资源管理

```cpp
// 自动句柄管理
class BufferHandle {
public:
    BufferHandle(HANDLE handle) : m_handle(handle) {}
    ~BufferHandle() {
        if (m_handle != INVALID_HANDLE_VALUE) {
            mb_writer_close(m_handle);
        }
    }
    
    HANDLE get() const { return m_handle; }
    bool isValid() const { return m_handle != INVALID_HANDLE_VALUE; }
    
    // 禁止拷贝，允许移动
    BufferHandle(const BufferHandle&) = delete;
    BufferHandle& operator=(const BufferHandle&) = delete;
    BufferHandle(BufferHandle&& other) noexcept : m_handle(other.m_handle) {
        other.m_handle = INVALID_HANDLE_VALUE;
    }
    
private:
    HANDLE m_handle;
};

// 自动资源管理的缓冲区管理器
class AutoBufferManager {
public:
    AutoBufferManager(int channelId) : m_channelId(channelId) {}
    
    BufferResult openWriter(uint32_t streamType) {
        auto config = BufferConfigTable::calculateBuffer(streamType);
        HANDLE handle = mb_writer_open2(static_cast<MB_TYPE>(streamType), 
                                       config.frameSize, config.frameCount, m_channelId);
        
        if (handle == INVALID_HANDLE_VALUE) {
            return BufferResult::ALLOCATION_FAILED;
        }
        
        m_writers[streamType] = std::make_unique<BufferHandle>(handle);
        return BufferResult::SUCCESS;
    }
    
    void closeWriter(uint32_t streamType) {
        m_writers.erase(streamType); // 自动调用析构函数关闭句柄
    }
    
private:
    int m_channelId;
    std::map<uint32_t, std::unique_ptr<BufferHandle>> m_writers;
    std::unique_ptr<BufferHandle> m_reader;
};
```

## 5. 重构实施步骤

### 第一阶段：配置化重构（1天）
1. 实现BufferConfigTable
2. 替换硬编码参数
3. 简化参数计算逻辑

### 第二阶段：接口统一（1天）
1. 定义统一的结果类型
2. 重构读写接口
3. 保持向后兼容

### 第三阶段：错误处理统一（1天）
1. 实现统一错误处理机制
2. 替换分散的错误处理代码
3. 增强错误信息

### 第四阶段：资源管理重构（1天）
1. 实现RAII资源管理
2. 替换手动句柄管理
3. 确保无资源泄漏

## 6. 预期效果

### 代码量减少
- 原始：524行 → 重构后：约300行（减少43%）
- getMbCacheNum函数：40行 → 配置表查询（减少90%）

### 复杂度降低
- 参数计算：复杂公式 → 配置表查询
- 错误处理：分散处理 → 统一机制
- 资源管理：手动管理 → RAII自动管理

### 可维护性提升
- 配置化参数，易于调整
- 统一接口，易于使用
- 自动资源管理，避免泄漏
