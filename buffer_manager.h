#pragma once

#include <string>
#include <vector>
#include <map>
#include <pthread.h>
#include "low_delay_common.h"
#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "vs_media_buffer.h"

// 常量定义
#define MAX_FRAME_DATA_SIZE   (2*1024*1024)

// 帧读取上下文
typedef struct _T_READ_FRAME_CONTEXT {
    BOOL checkKeyFrame;     // 是否需要等待关键帧
    BOOL first_trans;       // 是否第一次传输
    UINT64 timestamp;       // 当前时间戳
    HANDLE audio_handle;    // 音频句柄
} T_READ_FRAME_CONTEXT;

// 计算缓冲区参数的辅助函数
void calculateBufferParams(UINT8 streamChn, UINT32 *cache_num, UINT32 *mb_size);

// 缓冲区管理类
class BufferManager {
public:
    BufferManager(int channel_id);
    ~BufferManager();
    
    void init();
    INT32 isBufferOpen(UINT32 streamChn);
    HANDLE getBufferHandle(UINT32 streamChn);
    
    INT32 openWriteBuffer(UINT32 streamChn);
    void closeWriteBuffer(UINT32 streamChn);
    void closeAllWriteBuffers();
    
    static INT32 createFrameHeader(T_FRAME_HEADER *dst_hdr, FRAMEINFO_t* hdr, uint32_t size);
    INT32 pushFrame(UINT32 streamChn, FRAMEINFO_t* hdr, void* data, uint32_t size);
    
    INT32 startVencData(INT32 chn, UINT32 streamChn); // 修改：添加chn参数
    void stopVencData(UINT32 streamChn);
    
    // 打开实时流
    HANDLE openReaderStream(UINT32 streamChn = 0);
	// 打开缓存流
	HANDLE openPreReaderStream(UINT32 streamChn = 0);
	
    void closeReaderStream();
    INT32 readFrame(T_FRAME_HEADER** frame_hdr, CHAR** buffer, bool* is_key_frame = nullptr, UINT64* out_timestamp = nullptr);
    void resetReaderPosition();
	void resetPreReaderPosition();
    void setAudioHandle(HANDLE audio_handle);
    
private:
    
    INT32 chn;                // 通道号
    bool  initialized;        // 初始化标志
    INT32 mb_sec;             // 缓存秒数
    INT32 videoFps;           // 视频帧率
    INT32 bitrate;            // 码率

    // 写入相关
    UINT32 frame_seq;         // 帧序号
    HANDLE write_handles[VS_MAX_STREAM_NUM];         // 写入句柄
    INT32  video_mb_cache_sec[VS_MAX_STREAM_NUM];     // 视频缓存秒数
    
    // 读取相关
    HANDLE reader_handle;                         // 读取句柄
    T_READ_FRAME_CONTEXT read_context;             // 读取上下文

    // 获取缓冲区类型的函数
    MB_TYPE getBufferType(INT32 channelId, UINT32 streamChn);
    VOID getMbCacheNum(UINT8 streamChn, UINT32 *cache_num, UINT32 *mb_size);
};



// 开启缓冲区
INT32 start_venc_data(INT32 chn, UINT32 streamChn);


// 关闭缓冲区
VOID stop_venc_data(INT32 chn, UINT32 streamChn);

// 推送流 到MB中
VOID push_frame_mb(INT32 chn, UINT32 streamChn, FRAMEINFO_t *hdr, LPVOID data, UINT32 size);



