/***************************************************************************
** 版权所有: 成都云创天下 Copyright (c) 2015-2020  ********************     
** 文件名称: X:\mpp\sample\vsipc\storage\vs_media_file.cpp
** 文件标识: 
** 内容摘要: 
			1.每天0点强制切换文件
			2.
** 当前版本: v1.0
** 作     者: 徐辉
** 完成日期: 2018年3月15日
** 修改记录: 
** 修改记录: 
** 修改日期: 
** 版 本 号: 
** 修 改 人: 
** 修改内容: 
***************************************************************************/

#include <unistd.h>
#include <sys/stat.h>
#include <sys/vfs.h>
#include <sys/types.h>
#include <dirent.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/file.h>
#include <linux/types.h>
#include <linux/netlink.h>
#include <sys/mount.h>
#include <errno.h>
#include <fcntl.h>
#include <map>
#include <list>
using namespace std;

#include "vs_media_file.h"
#include "include/Thread/Lock.hpp"
#include "json/vs_cJSON_utils.h"
#include "utils/vs_logdb_no.h"
#include "utils/vs_logdb.h"
#include "utils/vs_media_buffer.h"
#include "utils/vs_utils.h"

// TF卡的设备文件
static struct {
	CHAR	dev_file_short[16];
	CHAR	dev_file[32];
	CHAR	dev_file_p1_short[16];
	CHAR	dev_file_p1[32];
	UINT8	emmc_vfat;	// emmc强制使用vfat
} sg_tf_dev_file; 

// TF卡设备文件
#define TF_DEV_FILE_SHORT		sg_tf_dev_file.dev_file_short
#define TF_DEV_FILE				sg_tf_dev_file.dev_file
#define TF_DEV_FILE_P1_SHORT	sg_tf_dev_file.dev_file_p1_short
#define TF_DEV_FILE_P1			sg_tf_dev_file.dev_file_p1

// file systems name
#define VS_FS_SMB3	"smb3"
#define VS_FS_NFS	"nfs"

#define	ONE_AVFILE_TM_LEN	300 // 5分钟
#define ALARM_REC_TM_LEN	20 	// 20秒


T_REC_WORK		g_rec_work = {
		enable_sche:FALSE,
		start:FALSE,
		event_cb:NULL,		
		one_rec:{0,	0,
			{0},
			{0},
			RT_NORMAL,
			0,
			{0,0},
			OR_FLAG_NONE,
			
		},
		wait_key_frame:TRUE,
		avh:NULL,
		vid:INVALID_VALUE,
		vidBsf:NULL,
		aid:INVALID_VALUE,
		audBsf:NULL
	};
	
		
static pthread_t		g_workThread = INVALID_HANDLE_VALUE;	// 工作线程
static pthread_t		g_mediaThread = INVALID_HANDLE_VALUE;	// 获取数据线程
static UINT8			g_tf_format = 0;			// tf卡格式化标记
static UINT8			g_tf_is_ready	= FALSE;	// tf卡就绪状态 
static union{
	struct {
		UINT32	enable:1;
		UINT32	mount_ok:1;
	};
	UINT32		status;
} 						g_nas_point;				// nas结点 

//static UINT32			g_sdio_devno = 0;			// sd卡设置号
INT32					g_pcm_fd = INVALID_VALUE;	// pcm记录文件
static UINT8			g_module_media_file_run = FALSE;		// 模块运行标记

/**
 * 打开写文件
 * @param  rec_type  录像类型 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_open(VS_RECORD_TYPE rec_type);

/**
 * 写视频
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_write_video(BYTE *buff , UINT32 size, UINT8 key_frame, UINT64 timestamp);

/**
 * 写音频
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_write_audio(BYTE *buff , UINT32 size, UINT64 timestamp);

/**
 * 切换文件
 * @return	 无
 */
VOID mfile_switch();

/**
 * 时间转换
 */
time_t to_timet(STimeDay *p)
{
	struct tm temp;

	QfSet0(&temp, sizeof(temp));
	temp.tm_year = p->year - 1900;
	temp.tm_mon	 = p->month - 1;
	temp.tm_mday = p->day;
	temp.tm_hour = p->hour;
	temp.tm_min  = p->minute;
	temp.tm_sec  = p->second;

	return mktime(&temp);
}


/** 
 * 卸载文件系统
 * @param  mpath  挂载路径
 */
void umount_fs(LPCSTR fs_name, LPCSTR mpath)
{
	FILE	*pfile;	
	
	pfile = fopen("/proc/mounts", "r");
	if (NULL != pfile) {
		char	*line = NULL;
		size_t	nums = 0;
		
		while (getline(&line, &nums, pfile) != -1){
			if (strstr(line, mpath) && strstr(line, fs_name)) {				
				LogI("umount(%s)", mpath);
//				umount(mpath);
				if (umount(mpath) EQU 0){
					//rewind(pfile);
					Sleep(5);
				}
			}

			free(line);
			line = NULL;
			nums = 0;
		}	
		fclose(pfile);
		
	}

}


/**
 * tf卡挂载状态
 *  挂载上返回1, 挂载为可写返回2, 没有挂载返回0
 */
enum {
	// 未知
	VS_MOUNT_UNKNOW = 0,
	// 只读
	VS_MOUNT_READ_ONLY,
	// 挂载正常
	VS_MOUNT_OK,
};
#define	 tf_mount_stat()	mount_stat(SDCARD_MOUNT_PATH, TF_DEV_FILE)
UINT8 mount_stat(LPCSTR src_dev, LPCSTR mpath) {
	FILE	*pfile;

	pfile = fopen("/proc/mounts", "r");
	if (NULL != pfile) {
		char	*line = NULL;
		size_t	nums = 0;		
		
		while (getline(&line, &nums, pfile) != -1){
			if (strstr(line, src_dev) && strstr(line, mpath)) {
				nums = 0;

				// 判断是否为rw
				if (strstr(line, " rw,")) {
					free(line);
					fclose(pfile);
				
					return VS_MOUNT_OK;	
				}

				free(line);
				fclose(pfile);
				
				return VS_MOUNT_READ_ONLY;
			}

			free(line);
			line = NULL;
			nums = 0;
		}	
		fclose(pfile);
		
	}

	return VS_MOUNT_UNKNOW;
}


/**
 * 判断TF卡否存在(并且分区也存在) 
 * @return	 成功返回1,失败返回0
 */
UINT8 sdcard_exist()
{
	return file_exist(TF_DEV_FILE_P1) ? TRUE:FALSE;
}



#if 0
/**
 * tf卡分区
 * @return	 成功返回1,失败返回0
 */
static UINT8 sdcard_fdisk(UINT8	is_ext4)
{
	INT32	r_file = INVALID_VALUE, w_file = INVALID_VALUE; //
	CHAR	cmdline[256];
	INT32	fnRet;

	QfSet0(cmdline, sizeof(cmdline));
	sprintf(cmdline, "fdisk %s 2>&1", TF_DEV_FILE);
	pid_t	pid = ps_open(cmdline, &r_file, &w_file);
				
	if (pid) {

		do {
//			LogW("开始检测");
			ps_read_string(r_file, "Command (m for help):");
		
//			LogW("开始删除分区");

			// 删除分区
			for (int i = 10; i >= 1; i--) {
				ps_write(w_file, "d\n");
				if (ps_reade_string(r_file, "Command (m for help):", "No partition is defined yet!") >= 0)
					break;

				for (;i >= 1; i--) {
					sprintf(cmdline, "%d\n", i);
					ps_write(w_file, cmdline);
					fnRet = ps_reade_string(r_file, "Command (m for help):", "Value is out of range");
					if (fnRet EQU 0)
						continue;					
					break;
				}
			}

			ps_write(w_file, "p\n");
			ps_read_string(r_file, "Command (m for help):");
			
//			LogW("创建分区");
			/*one parimary partiton*/
			ps_write(w_file, "n\n");
			ps_read_string(r_file, "primary partition (1-4)");
			ps_write(w_file, "p\n");
			ps_read_string(r_file, "Partition number (1-4):");
			ps_write(w_file, "1\n");
			ps_read_string(r_file, "First");
			ps_write(w_file, "\n");
			ps_read_string(r_file, "Last");
			ps_write(w_file, "\n");
			ps_read_string(r_file, "Command (m for help):");

//			LogW("FAT32/EXT4");
			/*changed fs ID for FAT32 or linux*/
			ps_write(w_file, "t\n");
			ps_read_string(r_file, "Hex code"); 			
			ps_write(w_file, is_ext4 ?  "83\n" : "c\n");
			ps_read_string(r_file, "Command (m for help):");

//			LogW("save分区");
			/*save*/
			ps_write(w_file, "w\n");			

//			LogW("退出");
			ps_write(w_file, "\n\n");
			ps_read_string(r_file, "Calling ioctl");

		}while (FALSE);
		LOGI("操作结束");	
		ps_close(pid, r_file, w_file);
	}	

	return TRUE;
	
}

#endif

/**
 * 得到tf卡大小,单位M,不到1M按1M返回 
 * @return	 成功返回正常大小,失败返回默认大小
 */
UINT32 tf_dev_total_size(UINT32 def_size = 0)
{
	FILE	*pfile;	

	pfile = fopen("/proc/partitions", "r");
	if (NULL != pfile) {
		char	*line = NULL;
		size_t	nums = 0;	
		UINT64	nums_total = 0;	
		
		while (getline(&line, &nums, pfile) != -1){
			if (strstr(line, TF_DEV_FILE_SHORT)) {
				nums = 0;

				// 得到大小
				if (sscanf(line, "%*s%*s%llu", &nums_total) > 0) {
					if (nums_total > 0) {
						def_size = nums_total>>10;	
						LOGI_NF("total: %lluK, %dM", nums_total, def_size);
					}
				}				

				free(line);
				fclose(pfile);
				
				if (def_size < 1)
					def_size = 1;
				
				return def_size;
			}

			free(line);
			line = NULL;
			nums = 0;
		}	
		fclose(pfile);
		
	}

	return def_size;
}


/**
 * tf卡格式化进度, 假的
 */
INT32 tf_format_progress(UINT32 total = 0)
{
	static time_t	s_time = 0;
	static INT32	s_format_prog = 0;
	static UINT32	s_total = 128<<10; 	// 128G
	static UINT32	s_gpers =  (3<<10);	// 每秒格式化3G

	if (total) {
		
		s_time 	= get_app_uptime();
		s_total	= total; 
		s_format_prog = 0;
		//if (total > (64<<10)) {
			s_gpers = (1<<10);	// 每秒格式化1G
//		} 
//		else {
//			s_gpers = (3<<10); 	// 每秒格式化3G
//		}
		
	} else {
		
		if (!g_tf_format) {
			
			s_format_prog = 100;
			
		} else {

			if (g_tf_format EQU 2) {	// 可能还要做准确工作
				s_format_prog = 1;	
				s_time 	= get_app_uptime();
			}
			else {
				INT32	cost = get_app_uptime() - s_time;			
				
				s_format_prog = cost * s_gpers * 100 / s_total; 
				LOGI_NF("total: %dM, cost: %dsec, prog: %d%%", s_total, cost, s_format_prog);
				if (s_format_prog > 99)
					s_format_prog = 99;	
			}
		}	
	}

	return s_format_prog;
}


/** 
 * 重建指定路径下的数据文件
 * @param  path  路径
 */
void rebuild_dbfile(LPCSTR dir_path)
{
	DIR 			*dir;
	struct dirent	*file;
	struct stat 	tStat;	
	char			dest[260];	
	int				val6[6];	
	int				fnRet;
	T_ONE_REC		*one_rec = NULL;
	map<time_t, T_ONE_REC*>	mapDbIndex;

	dir = opendir(dir_path);
	if (!dir) {
		LogE("opendir(%s) errno=%d", dir_path, errno);
		return;
	}

	QfSet0(val6, sizeof(val6)); 
	sscanf(dir_path, "%*[^0-9]%04d%02d%02d", &val6[0], &val6[1], &val6[2]);
	while ((file = readdir(dir)) != NULL) {
		
		if (strcmp(file->d_name, ".") EQU 0 
			|| strcmp(file->d_name, "..") EQU 0)
			continue;
			
		sprintf(dest, "%s%s", dir_path, file->d_name);		

		// 当前录像文件不写
		if (strcmp(dest, g_rec_work.one_rec.file_name) EQU 0)
			continue;
		
		lstat(dest, &tStat);
		struct tm tmp_tm;
#if 0		

		char			strTime[128];	

		//gmtime_r(&tStat.st_mtim.tv_sec, &tmp_tm);
		localtime_r(&tStat.st_mtim.tv_sec, &tmp_tm);
		char *p = strTime;
		p += strftime(strTime, sizeof(strTime)-1, "m=%Y-%m-%d %H:%M:%S", &tmp_tm);

		localtime_r(&tStat.st_atim.tv_sec, &tmp_tm);
		p += strftime(p, strTime+sizeof(strTime)-p-1, ", a=%Y-%m-%d %H:%M:%S", &tmp_tm);

		localtime_r(&tStat.st_ctim.tv_sec, &tmp_tm);
		p += strftime(p, strTime+sizeof(strTime)-p-1, ", c=%Y-%m-%d %H:%M:%S", &tmp_tm);
		
		printf("%s, size=%lu, %s\n", dest, tStat.st_size, strTime);
		
		QfSet0(strTime, sizeof(strTime));
		
#endif		
		if (one_rec EQU NULL)
			one_rec = (T_ONE_REC *)calloc(1, sizeof(T_ONE_REC));
		else
			QfSet0(one_rec, sizeof(T_ONE_REC));		
		
		// 解析文件名,得到开始时间与类型
		fnRet = sscanf(file->d_name, "%02d%02d%02d_%hhd", &val6[3], &val6[4], &val6[5], &one_rec->rec_type);
		if (fnRet != 4){
			LogW("dir_path=%s, d_name=%s, sscanf=%d\n", dir_path, file->d_name, fnRet);
			continue;
		}
		
		tmp_tm.tm_year 	= val6[0] - 1900;
		tmp_tm.tm_mon  	= val6[1] - 1;
		tmp_tm.tm_mday 	= val6[2];
		tmp_tm.tm_hour 	= val6[3];
		tmp_tm.tm_min 	= val6[4];
		tmp_tm.tm_sec 	= val6[5];	
		
		s_strcpy(one_rec->file_name, dest);
		one_rec->st_time = mktime(&tmp_tm);
		if (tStat.st_atim.tv_sec EQU tStat.st_mtim.tv_sec+1)
			one_rec->end_time = tStat.st_atim.tv_sec;
		else
			one_rec->end_time = tStat.st_mtim.tv_sec;
		if (strstr(one_rec->file_name, ".del")){
			LogW("d_name=%s, st_time=%lu, end_time=%lu, span=%lu rec_type=%hhd\n", 
					file->d_name, one_rec->st_time, one_rec->end_time, 
					one_rec->end_time - one_rec->st_time, one_rec->rec_type);
		}
		else {
			one_rec->flag = OR_FLAG_OK;
			mapDbIndex.insert(make_pair(one_rec->st_time, one_rec));
			one_rec = NULL;
		}
		
	}
	FreeAndNull(one_rec);
	closedir(dir);

	LogD("mapDbIndex.size=%u, dir_path=%s", mapDbIndex.size(), dir_path);

	// 产生索引文件
	if (!mapDbIndex.empty())
	{
		CHAR	path[200];	
		FILE	*pf;
		
		sprintf(path, "%s/%s", dir_path, REC_DB_INDEX);
		LogW("rebuild = %s ", path);
		pf = fopen(path, "a+b");
		if (pf != NULL) {
			int my_fd = fileno(pf);
			
			flock(my_fd, LOCK_EX);
			for (auto &p : mapDbIndex){
				fwrite(p.second, sizeof(T_ONE_REC), 1, pf);
				free(p.second);
			}
			flock(my_fd, LOCK_UN);			
			fclose(pf);
		}
	}

}


/**
 * 插入一条记录
 * @param  one_rec 记录
 * @return         成功为1；失败为0
 */
UINT8 rec_db_add(T_ONE_REC *one_rec)
{
	CHAR	path[200];	
	FILE	*pf;
	
	if (one_rec EQU NULL) {
		LOGE("rec_db_add(NULL)");
		return FALSE;
	}
	
	extract_file_path(one_rec->file_name, path);
	strcat(path, REC_DB_INDEX);
	mfile_new_dbfile(path);

//	LogW("path = %s", path);

	pf = fopen(path, "a+b");
	if (pf != NULL) {
		int my_fd = fileno(pf);
		
		flock(my_fd, LOCK_EX);
		fwrite(one_rec, sizeof(T_ONE_REC), 1, pf);
		flock(my_fd, LOCK_UN);
//		LogW("st_time = %lu, end_time = %lu, flag = %d, name = %s", one_rec->st_time, one_rec->end_time, one_rec->flag, one_rec->file_name);
		// LOGI_NF("%s(%s) fwrite fail, errno=%d", __func__, path, errno);
		fclose(pf);

		return TRUE;
	}
	else {
		LOGE("%s(%s) fopen fail, errno=%d", __func__, path, errno);
	}

	return FALSE;
}

// 得到磁盘大小以及剩余空间(如果是TF)
UINT8 get_disk_info(LPCSTR path, T_DISK_INFO *info) 
{
	struct statfs diskInfo;

	QfSet0(info, sizeof(T_DISK_INFO));
	if (!mfile_tf_exist()){
		info->status = -1;	// -1为损坏
		return 0;
	}
		
	if (tf_mount_stat() EQU VS_MOUNT_UNKNOW) {
		info->total_msize = tf_dev_total_size();
		info->status = 0;	// 0未格式化(没有挂载上的也认为没有格式化,如果分区正常,应该是可以挂载的)
		LOGI_NF("%s() tf_mount_stat()=0", __func__);
		return 0;
	}

	int ret = statfs(path, &diskInfo);
	if (ret < 0) {
		info->total_msize = tf_dev_total_size();
		info->status = -1;	// -1为损坏
		//LogI("statfs fail status=%d", info->status);
		return 0;
	}

	if (access(path, W_OK) < 0) {
		info->status = (errno EQU EROFS ? -2 : -1);	// 只读
		info->total_msize = tf_dev_total_size();
		//LogI("access(%s) fail status=%d", path, info->status);
		return 0;
	}
	
	if (diskInfo.f_blocks EQU 0) {
		info->total_msize = tf_dev_total_size();
		info->status = 0;	// 0未格式化		
		LOGI_NF("%s() diskInfo.f_blocks=0", __func__);
		return 0;
	}

	info->status = 1;
	info->total_msize = diskInfo.f_bsize;
	info->total_msize *= diskInfo.f_blocks;	
	info->total_msize >>= 20;

	info->free_msize  = diskInfo.f_bsize;
	info->free_msize *= diskInfo.f_bavail;
	info->free_msize >>= 20;
	
//	LogI("%s  total=%lldMB, free=%lldMB\n", path, info->total_msize, info->free_msize);

	return 1;
}


/**
 * 从指定路径path中搜索大于more_than_date的目录,写入result中
 *  	但是不会搜索今天的
 * 成功返回>0的日期,失败返回0
 */
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wmaybe-uninitialized"
 
INT32 get_early_dir(LPCSTR path, INT32 *more_than_date)
{
	DIR 			*dir;
	struct dirent 	*file;
	struct stat		tStat;	
	char			dest[260];	
	char			cur_date[20];	
	INT32			early_date = *more_than_date + 1;
	INT32			tmp;
	UINT8			is_find = FALSE;

	dir = opendir(path);
	if (!dir) return 0;

	// 今天
	{
		T_DATETIME	now;
		
		get_now(&now);
		sprintf(cur_date, REC_DIR_FMT, now.usYear, now.usMonth, now.usDay);
	}
	
	while ((file = readdir(dir)) != NULL) {
		
		if (strcmp(file->d_name, ".") EQU 0 
			|| strcmp(file->d_name, "..") EQU 0)
			continue;
			
		sprintf(dest, "%s/%s", path, file->d_name);
		lstat(dest, &tStat);

		if (S_ISDIR(tStat.st_mode)) {

			// 不清当天的
//			if (strcmp(cur_date, file->d_name) EQU 0) {
//				continue;
//			}

			tmp = atoi(file->d_name);
			if (tmp > *more_than_date) { // 只处理大于开始日期的目录
				if (!is_find)
					early_date = tmp;
				else
					early_date = QF_MIN(early_date, tmp);
				is_find = TRUE;
			}
		} 

	}
	
	closedir(dir);

	// 找到了
	if (is_find) 
		return early_date;
	
	return 0;
}

#pragma GCC diagnostic pop


/**
 * 磁盘空间充足
 */
UINT8 disk_space_adequate()
{
    T_DISK_INFO 	disk_info;

	if (sdcard_exist()) {
	    get_disk_info(SDCARD_MOUNT_PATH, &disk_info);
	    if (disk_info.status < 1) {
			return TRUE;
	    }
	}
#if defined(NAS_ENABLE)	
	else if (mfile_nas_ready()){ // nas
		struct statfs diskInfo;
		
		int ret = statfs(NAS_MOUNT_PATH, &diskInfo);
		if (ret < 0) {
			return TRUE;
		}		

		disk_info.total_msize = diskInfo.f_bsize;
		disk_info.total_msize *= diskInfo.f_blocks;	
		disk_info.total_msize >>= 20;

		disk_info.free_msize  = diskInfo.f_bsize;
		disk_info.free_msize *= diskInfo.f_bavail;
		disk_info.free_msize >>= 20;
	}
#endif	
    UINT64	reserve_msize = disk_info.total_msize/10;

    // 保留空间空间
    reserve_msize = QF_MAX(reserve_msize, 2<<10);        // 2G
    reserve_msize = QF_MIN(reserve_msize, 4<<10);        // 5G
    //LogD("reserve_msize=%llu, free_msize=%llu", reserve_msize, disk_info.free_msize);
    if (disk_info.free_msize > reserve_msize){
        return TRUE;
    }

    return FALSE;
}


// 清除过期目录
void clear_expired_dir(LPCSTR path)
{	
	INT32	del_date = 19000101;
	char	dest[120];	
	UINT8	del_dir;

	// 判断是否有空间
	if (g_tf_format
		|| disk_space_adequate()) {
		return;
	}
	
	while ((del_date = get_early_dir(REC_PATH, &del_date)) > 0) {

		if (!Module_Running(g_module_media_file_run)) break;
		
		sprintf(dest, "%s/%d", REC_PATH, del_date);
		del_dir = TRUE;
		LOGI("clear dir: %s", dest);

		// 删除文件, 每删除6个文件统计一次可用空间
		{
			CHAR		path[200];	
			FILE		*pf;
			T_ONE_REC	one_rec;
			UINT32		del_num = 6;

			sprintf(path, "%s/" REC_DB_INDEX, dest);
			mfile_new_dbfile(path);
			pf = fopen(path, "r+b");
			if (pf != NULL) {
				while (fread(&one_rec, sizeof(one_rec), 1, pf) EQU 1) {

					if (OR_FLAG_DEL EQU one_rec.flag){	
						del_file(one_rec.file_name);	// 虽然已经标为删除,  还是做一次删除,避免正在播放的文件没有被删除
						continue;
					}

					// 录像中不删除
					if (OR_FLAG_REC EQU one_rec.flag)
						continue;

					if (!Module_Running(g_module_media_file_run)) 
						break;

					int my_fd = fileno(pf);
					flock(my_fd, LOCK_EX);
					fseek(pf, -sizeof(one_rec), SEEK_CUR);
					one_rec.flag = OR_FLAG_DEL;
					fwrite(&one_rec, sizeof(one_rec), 1, pf);
					flock(my_fd, LOCK_UN);  

					// 删除文件
					del_file(one_rec.file_name);
					//LOGI("delete file: %s", one_rec.file_name);

					// 每删除6个文件, 统计一下可用空间
					if (--del_num EQU 0) {
						if (disk_space_adequate()) {
							del_dir = FALSE;
							#ifdef AI_LOCAL_SAVE
							//同步删除AI本地记录
							if(ai_local_cb != NULL)
								ai_local_cb(one_rec.end_time);
							#endif
							#if defined(PLAT_FORM_ULC)
							if(expired_cb != NULL)
								expired_cb(ULC_SDCARD_ERROR_LESS_STORAGE);
							#endif
							break;
						} else {
							// 还是不够,再删除6个文件
							del_num = 6;
						}
					}
				}

				fclose(pf);
			}	
	
		}

		// 删除整个目录
		if (del_dir) {
			deltree_dir(dest);
			LOGI("deltree_dir(): %s", dest);
		}

		// 清够了.就不清其它的了
		if (g_tf_format
			|| disk_space_adequate()) {
			break;
		}		
		Sleep(500);
	}
	
}


// 创建文件并写入值
UINT8 create_time_file(LPCSTR file_name, LPCSTR value)
{
	if(!dir_exist(REC_TIME_PATH))
		mkdir(REC_TIME_PATH, 0777);
	
	INT32  fd = open(file_name, O_CREAT | O_WRONLY);
	
	if (fd != -1) {		

		write(fd, value, strlen(value));
		close(fd);

		return TRUE;
	}
	else {
		LOGE_NF("open(%s) fail, errno=%d", file_name, errno);
	}

	return FALSE;
}

// 向文件写入值
UINT8 file_write_string(LPCSTR file_name, LPCSTR value)
{
	INT32  fd = open(file_name, O_WRONLY);

	if (fd != -1) {		

		write(fd, value, strlen(value));
		close(fd);

		return TRUE;
	}
	else {
		LOGE_NF("open(%s) fail, errno=%d", file_name, errno);
	}

	return FALSE;
}



/** 
 * 通过文件恢复时间
 */
void mfile_restore_datetime()
{
    // 不处理单独运行程序的情况,因为os重启时,这个时间永远会小于60秒,4g时长稍微增大点
    UINT32 uptime=60;
    jparse_value_fun(ORIGIN_FUNC_DEF, "$.lte", [&](cJSON *item){
		if(item != NULL){
			if (item->type EQU cJSON_Number)
				if(item->valueint EQU 1)
					uptime += 40;
		}
	});

	LogI("GetSecondSinceBoot judge time:%d", uptime);
	
    if (GetSecondSinceBoot() > uptime)
		return;

    // 判断时间是否对已经有了(RTC会保存时间)
    T_DATETIME        now;        
    get_now(&now);
    if (now.tv.tv_sec > MIN_UTC_TIME)	
		return;

//	if(g_pRunSets->misc_set.disable_reboot != IPC_WORK_DISABLE_NET_SIGANLE_MODE)
//  		return;	

    LPCSTR shell_cmd = "cd " SDCARD_MOUNT_PATH ";cd $(ls -t -w1 -d */|head -n1);"
     "if [ $? -eq 0 ]; then\nsdcard_date=`stat -c \"%y\" $(ls -t -w1|head -n1)|cut -d. -f1`;\n[ $? -eq 0 -a -n \"$sdcard_date\" ] && date -s \"$sdcard_date\";\nfi";
        
	system_no_fd(shell_cmd);
	
}



/** 
 * 检测热拔插设备
 */
#define UEVENT_BUFFER_SIZE (2048*2)
static int init_hotplug_sock(void) {
	struct sockaddr_nl snl;
    const int buffersize = UEVENT_BUFFER_SIZE;//1 * 1024 * 1024;
    int retval;

    memset(&snl, 0x00, sizeof(struct sockaddr_nl));
    snl.nl_family = AF_NETLINK;
    snl.nl_pid = getpid();
    snl.nl_groups = 1;

    int hotplug_sock = socket(PF_NETLINK, SOCK_DGRAM, NETLINK_KOBJECT_UEVENT);
    if(hotplug_sock == -1){
        LOGE("error getting socket: %s", strerror(errno));
        return -1;
    }

    /* set receive buffersize */
    setsockopt(hotplug_sock, SOL_SOCKET, SO_RCVBUFFORCE, &buffersize, sizeof(buffersize));
    retval = bind(hotplug_sock, (struct sockaddr *) &snl, sizeof(struct sockaddr_nl));
    if(retval < 0){
        LOGE("bind failed: %s", strerror(errno));
        close(hotplug_sock);
        hotplug_sock = -1;
        return -1;
    }

    return hotplug_sock;
	
}


/**
 * tf卡检测线程
 */
LPVOID tf_detect_thread(LPVOID)
{
	THREAD_FUNC_BEGIN();
	pthread_detach(pthread_self());
	
	INT32 hotplug_sock = init_hotplug_sock();

	if (hotplug_sock != -1) {

		CHAR 		*buf = (CHAR *)malloc(UEVENT_BUFFER_SIZE);
		timeval 	tv;
		fd_set		read_fds;
		INT32		fnRet;
		CHAR		filter_dev[128];

		sprintf(filter_dev, "block/%s/%s", TF_DEV_FILE_SHORT, TF_DEV_FILE_P1_SHORT);
		while (Module_Running(g_module_media_file_run)) {

			FD_ZERO(&read_fds);  
			FD_SET(hotplug_sock, &read_fds);
			tv.tv_sec 	= 2; // 2s超时
			tv.tv_usec 	= 0; 		
			fnRet = ::select(hotplug_sock+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
			if (fnRet < 0) 
				break;
			else if (fnRet EQU 0 || 
				!FD_ISSET(hotplug_sock, &read_fds))
				continue;
			
	        fnRet = recv(hotplug_sock, buf, UEVENT_BUFFER_SIZE-1, 0);
			if (fnRet > 0) {
				buf[fnRet] = 0;
				if (strstr(buf, "add@") && strstr(buf, filter_dev)) {
					g_tf_is_ready = TRUE;
					
					LOGI("tf card ready.");
					
					#ifdef CN21_SD_DB
						Sleep(1000);
						LOGW("%s(), g_cn21_db_file = %s", __func__, g_cn21_db_file);
						cn21_sd_db_init();
					#endif
					
					#if defined(CN21_ENABLE)
						my_ability_func();
					#endif
					
				} else if (strstr(buf, "remove@") && strstr(buf, filter_dev)) {
					g_tf_is_ready = FALSE;
					
					#ifdef CN21_SD_DB
						//cn21_db_uninit();
						Sleep(1000);
						LOGW("%s(), g_cn21_db_file = %s", __func__, g_cn21_db_file);
						cn21_db_uninit();
					#endif
					LOGI("tf card remove.");

					#if defined(CN21_ENABLE)
						my_ability_func();
					#endif
				}
				else {
					LogI("hotplug=%s", buf);
				}
			} else if (fnRet < 0) {
				LOGE("%s() recv() error! errno=%d.", __FUNCTION__, errno);
			}
		}
		free(buf);
		
		close(hotplug_sock);
	} else {
		LOGE("%s() error! errno=%d.", __FUNCTION__, errno);
	}

	THREAD_FUNC_END();
	
	return NULL;
}



#if defined(MFILE_PRE_RECORD)
// 录像状态
typedef enum{
	REC_STATE_OPEN,	
	REC_STATE_SWITCH,
	REC_STATE_CLOSE,
}REC_STATE_E;

// 录像事件
typedef struct{
	REC_STATE_E			recState;		// 录像状态
	ALARM_EVENT_TYPE 	eventType;		// 类型
	ULONG 				abs_end_time;	// 结束绝对时间
}REC_EVENT_S;		

static list<REC_EVENT_S>	g_rec_event_list;
static TCSLock				g_rec_event_lock;

/** 
 * 放入事件
 * @param  recState  录像状态
 * @param  eventType  录像事件
 * @param  abs_end_time  结束绝对时间
 */
VOID mfile_set_event(REC_STATE_E recState, INT32 eventType, ULONG  abs_end_time)
{
	REC_EVENT_S rec_event = {
		.recState = recState,
		.eventType = static_cast<ALARM_EVENT_TYPE>(eventType),
		.abs_end_time = abs_end_time,
	};

	// if (recState EQU REC_STATE_OPEN) {	
	// 	LogI("recState=%d, eventType=%d, abs_end_time=%lu", recState,  eventType, abs_end_time);
	// }
	ScopedLocker l(g_rec_event_lock);

	g_rec_event_list.emplace_back(std::move(rec_event));
		
}

/** 
 * 获取事件
 * @param  rec_event  返回的数据
 * @return  成功返回TRUE;失败返回FALSE
 */
UINT8 mfile_get_event(REC_EVENT_S &rec_event)
{
	if (!g_rec_event_list.empty()){
		ScopedLocker l(g_rec_event_lock);
		
		rec_event = std::move(g_rec_event_list.front());
		g_rec_event_list.pop_front();

//		LogI("recState=%d, eventType=%d, abs_end_time=%ul", rec_event->recState,  
//			rec_event->eventType,  rec_event->abs_end_time);

		return TRUE;
	}

	return FALSE;
}



// 文件管理线程
LPVOID mfile_mgr_thread(LPVOID arg)
{
	SET_THREAD_NAME();

	UINT8 	val;
	UINT8	abn_try_count = 0;
	UINT8	sdcard_ok = g_tf_is_ready;
	UINT8	tf_stat;
	UINT16	last_switch_day = 99;

#if defined(NAS_ENABLE)
	// 加载samba	
	(VOID)mfile_nas_reload();
#endif	
	
	vs_logdb_set_env(VS_SOURCE_SYS, IPC_MOD_SDCARD);

	while (Module_Running(g_module_media_file_run)) {

		sdcard_ok = sdcard_exist();

		// 判断状态是否改变
		if (sdcard_ok != g_tf_is_ready) {
			g_tf_is_ready = sdcard_ok;
			abn_try_count = 0;
			if (sdcard_ok) { // 可能录在  nas上
				// 关闭录像
				if (g_rec_work.start) {
					mfile_close();
				}

				LOGDB_INFO(FORMAT_SDCARD_INSERT);
			}
			else{
				LOGDB_INFO(FORMAT_SDCARD_REMOVE);
			}

			
#ifdef LT_DENGHONG
			dh_tf_state(TRUE);
#else
#ifdef CMCC_HJQ
		;;
#else
		;;	//vsipc_set_state(VS_LED_TF_ERROR, FALSE);
#endif
#endif
		} 
		
		if (!sdcard_ok && !mfile_nas_ready()) {
			
			// 在tf卡不存时,无论如何都关闭计划表, 
			// 中途插入tf卡就可以利用计划表机制立即录像 
			g_rec_work.enable_sche = FALSE;	
			
			// 关闭录像
			if (g_rec_work.start) {
				mfile_set_event(REC_STATE_CLOSE, 1, 0);
			}
		}
		else if (!g_tf_format) {

			tf_stat = tf_mount_stat();

			// 只读状态
			if (tf_stat EQU VS_MOUNT_READ_ONLY && access(SDCARD_MOUNT_PATH, W_OK) < 0) {

				// 只读
				//(errno EQU EROFS ? -2 : -1);
				
				LOGE("%s abnormal. try remout count:%d", SDCARD_MOUNT_PATH, abn_try_count);
				LOGDB_WARN(FORMAT_SDCARD_READONLY);

				// 关闭计划表 
				g_rec_work.enable_sche = FALSE;		
			
				// 关闭录像
				if (g_rec_work.start) {
					mfile_set_event(REC_STATE_CLOSE, 0, 0);
				}

				// 尝试5次以后无果,重启设备
				if (abn_try_count > 5) 
				{					
					abn_try_count = 0;
					mfile_tf_format(); // 在线程中格式化
				} 
				else 
				{
#ifndef LT_DENGHONG	
#ifdef CMCC_HJQ
		;;												
#else

		;;			//vsipc_set_state(VS_LED_TF_ERROR, TRUE);
#endif
#endif
					
					// 重新挂载
					system_no_fd("mount -o remount,rw " SDCARD_MOUNT_PATH);
					abn_try_count++;
					Sleep(4000);
					continue;
				}
			} 
			else {
			
			#if defined(NAS_ENABLE)			
				// samba判断
				if (!sdcard_ok 
					&& tf_stat != VS_MOUNT_OK
					&& mfile_nas_ready()){
					tf_stat = mount_stat(NAS_MOUNT_PATH, VS_FS_SMB3);
				}
			#endif
					
				if (tf_stat EQU VS_MOUNT_OK) {
					T_DATETIME	now;
					ULONG		app_uptime = get_app_uptime();	

					//==============================================
					// 判断计划表
					//==============================================
					get_now(&now);
					//val = g_pRunSets->record.enable
					//		&& (g_pRunSets->record.all_time || ((now.tv.tv_sec > MIN_UTC_TIME) && now_in_period(&now, g_pRunSets->record.period)));

					val = 1;

					if (system_is_dormancy()) {	// 休眠不录像
						val = FALSE;
					}
					
					//LOGI("%s, enable_sche=%d, start=%d, val=%d", __FUNCTION__, g_rec_work.enable_sche, g_rec_work.start, val);
					if (g_rec_work.enable_sche != val) {
						g_rec_work.enable_sche = val;
						if (val) {
							if (!g_rec_work.start) {
								mfile_set_event(REC_STATE_OPEN, 0, app_uptime+ONE_AVFILE_TM_LEN);
							}
						} else {
							if (g_rec_work.start) {
								mfile_set_event(REC_STATE_CLOSE, 0, 0);
							}
						}
					}
					
					//==============================================
					// 切换录像文件			
					//==============================================
					else if (g_rec_work.start 
							&& g_rec_work.one_rec.flag EQU OR_FLAG_REC) {

						// 事件录像时间到				
						if (app_uptime >= g_rec_work.rec_info.abs_end_time) {
							mfile_set_event(REC_STATE_CLOSE, 4, 0);
							continue;
						}
										
						// 0点切换文件[确保每天只切换一次last_switch_day != now.usDay]
						if ((now.ucHour EQU 0 && now.ucMin EQU 0) 
							&& last_switch_day != now.usDay) {
							last_switch_day = now.usDay;
							mfile_set_event(REC_STATE_SWITCH, 0, 0);
						}
					}
					// 有计划表,但是没有打开,则开启录像
					else if (g_rec_work.enable_sche && !g_rec_work.start) {
						mfile_set_event(REC_STATE_OPEN, 0, app_uptime+ONE_AVFILE_TM_LEN);
					}

					#ifdef AI_LOCAL_SAVE
					//打开本地AI存储,如果SD卡存在
					vs_ai_local_init();	
					#endif
					
					//==============================================
					// 清盘
					//==============================================
					
					clear_expired_dir(REC_PATH);

					//==============================================
					// 非全天录像时,并且当前没在录像时间段的时候,在SD卡目录增加一个空文件来刷新时间			
					//==============================================
//					if(!g_pRunSets->record.all_time 
//						&& !((now.tv.tv_sec > MIN_UTC_TIME) && now_in_period(&now, g_pRunSets->record.period))
//						&& g_pRunSets->misc_set.disable_reboot EQU IPC_WORK_DISABLE_NET_SIGANLE_MODE)

					
					{
						if(!file_exist(REC_TIME_FILE))
							create_time_file(REC_TIME_FILE, "cdyctx/time");
						else
							file_write_string(REC_TIME_FILE, "cdyctx/time");
					}
				}
						
			}
		}
		
		Sleep(1000);

	}

	mfile_close();
		
	LOGI("%s() exit.", __FUNCTION__);
	
	END_THREAD;
	return NULL;
}

// 录像线程
LPVOID media_thread(LPVOID arg)
{
	SET_THREAD_NAME();

	CHAR		*buffer;
	INT32		fnRet;
	HANDLE		handle = NULL;
	HANDLE		audio_handle = NULL; 
	T_FRAME_HEADER 	*frameHead = NULL;
	UINT8			checkKeyFrame = TRUE;
	T_MEDIA_INFO	media_info;
	UINT8			switch_file = FALSE;
	REC_EVENT_S 	rec_event;
	//UINT8			pause_record = FALSE; (VOID)pause_record; // 避免编译警告


	//UINT8			strm_chn = g_pRunSets->misc_set.rec_stream_chn;

	UINT8			strm_chn = 0;

	LOGI("%s() strm_chn:%d,  running...", __func__,  strm_chn);

#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
		// hcc 去掉加密
		handle = mb_reader_open_ex(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0, 0x01);
		if (handle EQU INVALID_HANDLE_VALUE) {
			LOGE("mb_reader_open(MEDIA_BUFFER_STREAM_0) error");
			goto fn_end;
		}

		audio_handle = mb_reader_open(MEDIA_BUFFER_AAC);
		if (audio_handle EQU INVALID_HANDLE_VALUE) {
			LOGE("mb_reader_open(MEDIA_BUFFER_AAC) error");
			goto fn_end;
		}
#else
		// 魔镜等
		handle = mb_reader_open(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0);
		if (handle EQU INVALID_HANDLE_VALUE) {
			LOGE("mb_reader_open(MEDIA_BUFFER_STREAM_0) error");
			goto fn_end;
		}
#endif


	if (handle EQU INVALID_HANDLE_VALUE) {
		LOGE("mb_reader_open(MEDIA_BUFFER_STREAM_0) error");
		goto fn_end;
	}

	QfSet0(&media_info, sizeof(media_info));
	while (Module_Running(g_module_media_file_run)) {

		frameHead = NULL;

		// 切换录像文件
		// if (switch_file || strm_chn != g_pRunSets->misc_set.rec_stream_chn) {
		if (switch_file || strm_chn != 0) {
			LOGI("%s() strm_chn:%hhu, rec_stream_chn:%hhu, switch_file:%hhu", __func__,  strm_chn, 
				0, switch_file);
			strm_chn = 0;
			mb_reader_close(handle);
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
			mb_reader_reset_pos(audio_handle);
			handle = mb_reader_open_ex(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0, 0x01);
#else
			handle = mb_reader_open(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0);
#endif
			checkKeyFrame = TRUE;
			if (g_rec_work.start) {				
				mfile_switch();
			}			
			QfSet0(&media_info, sizeof(media_info));
			switch_file = FALSE;
		}

		// 获取事件
		if (mfile_get_event(rec_event))
		{
			if (rec_event.recState EQU REC_STATE_CLOSE){
				mfile_close();
				continue;
			}
			else if (rec_event.recState EQU REC_STATE_SWITCH) {				
				mfile_switch();
				checkKeyFrame = TRUE;
			}
			else if (rec_event.recState EQU REC_STATE_OPEN){
				UINT8 pre_record = FALSE;

#if 1
				if (rec_event.eventType) {
					if (!g_rec_work.start) {	// 未打开录像
						pre_record = TRUE;
						checkKeyFrame = TRUE;
						mfile_close();
						mfile_open(RT_MOTION_DETECT);
					}
					else if (g_rec_work.one_rec.rec_type != RT_MOTION_DETECT){	// 只是类型不一致
						mfile_open(RT_MOTION_DETECT);
					}
					g_rec_work.rec_info.abs_end_time = rec_event.abs_end_time;
				}
				else {
					if (!g_rec_work.start || g_rec_work.one_rec.rec_type != RT_NORMAL) {
						checkKeyFrame = TRUE;
						mfile_open(RT_NORMAL);	
						g_rec_work.rec_info.abs_end_time = rec_event.abs_end_time;
					}
				}
#else
				switch (rec_event.eventType) {
					/* 移动侦测 */
					case AET_EVENT_MOTIONDECT:{
						if (!g_rec_work.start || g_rec_work.one_rec.rec_type != RT_MOTION_DETECT) {
							pre_record = TRUE;
							checkKeyFrame = TRUE;
							mfile_close();
							mfile_open(RT_MOTION_DETECT);
						}
						g_rec_work.rec_info.abs_end_time = rec_event.abs_end_time;
					}
					break;
					
					/* 人形侦测 */
					case AET_EVENT_PERSONDECT: {
						if (!g_rec_work.start || g_rec_work.one_rec.rec_type != RT_PERSON_DETECT) {
							pre_record = TRUE;
							checkKeyFrame = TRUE;
							mfile_close();
							mfile_open(RT_PERSON_DETECT);
						}
						g_rec_work.rec_info.abs_end_time = rec_event.abs_end_time;
					}
					break;

					default: {
						if (!g_rec_work.start || g_rec_work.one_rec.rec_type != RT_NORMAL) {
							checkKeyFrame = TRUE;
							mfile_open(RT_NORMAL);	
							g_rec_work.rec_info.abs_end_time = rec_event.abs_end_time;
						}
					}					
					
				}
#endif

				if (pre_record) {
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
					mb_reader_reset_last_pos(audio_handle);
#endif
					mb_reader_reset_last_pos(handle);
				}
			}
		}

//		LogI("start=%hhu, wait_key_frame=%hhu, end_time=%ld", 
//			g_rec_work.start,
//			g_rec_work.wait_key_frame,
//			g_rec_work.one_rec.end_time);
		
		if (!g_rec_work.start) {
			mb_reader_nop(handle);
			Sleep(20);
			continue;
		}		

		// 读视频
		fnRet = mb_reader_pop(handle, &frameHead, &buffer);
		if (fnRet != OK) {

			// 有跳帧,必须等到I帧
			if (fnRet EQU FAIL-1) 
				checkKeyFrame = TRUE;
		}
		else {

//			UINT64 timestamp	= frameHead->utcTime;
			UINT64 timestamp	= frameHead->timeStamp*1000;

			// I帧的时候,录像文件超过5分钟(比如报警录像一直触发,g_rec_work.rec_info.abs_end_time这个时间就会一直叠加)
			if (frameHead->frameType EQU FT_IFRAME){				
				if (frameHead->utcTime >= g_rec_work.one_rec.st_time + ONE_AVFILE_TM_LEN
					|| get_app_uptime() >= g_rec_work.rec_info.abs_end_time) {
					mfile_switch();
					checkKeyFrame = TRUE;
				}
			} 
		
//			UINT64 timestamp	= frameHead->timeStamp - (frameHead->utcTime%864000)*1000;
//			timestamp	+= frameHead->utcTime*1000;
			// 视频编码改变
			if (media_update(frameHead->frameType EQU FT_AUDIO ? FALSE: TRUE, frameHead, &media_info)) {
				switch_file = TRUE;				
				continue;
			}			
			
			do {
				
				// 检查I帧
				if (checkKeyFrame && frameHead->frameType != FT_IFRAME)
					break;

				if (frameHead->frameType EQU FT_AUDIO){
#if !defined(ENABLE_AAC_ENC) || !defined(CMCC_HJQ)	
					mfile_write_audio((BYTE*)buffer, frameHead->size, timestamp);
#endif
				}
				else {
					checkKeyFrame = FALSE;

					// 非定时录像, 有预录像, 开始时间要调整
					if (g_rec_work.one_rec.rec_type != RT_NORMAL 
						&& g_rec_work.videoNums EQU 0){
						if (g_rec_work.one_rec.st_time != frameHead->utcTime)
							g_rec_work.one_rec.change_st = TRUE;
						g_rec_work.one_rec.st_time = frameHead->utcTime;
					}				

					mfile_write_video((BYTE*)buffer, frameHead->size, frameHead->frameType EQU FT_IFRAME ? 1 : 0, timestamp);
				
				}
			} while (FALSE);
		}
		
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
		if (!checkKeyFrame) {
			// 读音频
			fnRet = mb_reader_pop(audio_handle, &frameHead, &buffer);
			if (fnRet EQU OK) {
//				UINT64 timestamp	= frameHead->utcTime;
				UINT64 timestamp	= frameHead->timeStamp*1000;
//				UINT64 timestamp	= frameHead->timeStamp - (frameHead->utcTime%864000)*1000;
//				timestamp	+= frameHead->utcTime*1000;

				mfile_write_audio((BYTE*)buffer, frameHead->size, timestamp);
			}
			else
			{
//				LOGE("%s mb_reader_pop(error)", __FUNCTION__);
			}
		}
		else {
			mb_reader_nop(handle);
		}
#endif

		// 未读到任何数据
		if (frameHead EQU NULL) {
			Sleep(20);
		}

	}


fn_end:	
	mb_reader_close(handle);
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
	mb_reader_close(audio_handle);
#endif
	
	LOGI("%s() exit.", __FUNCTION__);
	
	END_THREAD;
	return NULL;
}


/**
 * 切换文件
 * @return	 成功返回1,失败返回0
 */
VOID mfile_switch()
{
	mfile_close();	
	if (g_rec_work.rec_info.abs_end_time > get_app_uptime()) {	

		// 定时录像下个结束时间切换后重新计算
		if (g_rec_work.one_rec.rec_type EQU RT_NORMAL) 
			g_rec_work.rec_info.abs_end_time = get_app_uptime() + ONE_AVFILE_TM_LEN;
		
		mfile_open(static_cast<VS_RECORD_TYPE>(g_rec_work.one_rec.rec_type));
	}	
}



/** 
 * 发送消息
 * @param  eventType  事件类型
 * @param  delay_sec_close  持续多少秒后关闭，=0使用内置策略
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_push_msg(ALARM_EVENT_TYPE eventType, INT32 delay_sec_close)
{		
	if (delay_sec_close < 5)
		delay_sec_close = ALARM_REC_TM_LEN;	// 小于5秒统一使用20秒
	
#if 1
	// 这里全部使用移动侦测标记
	mfile_set_event(REC_STATE_OPEN, AET_EVENT_MOTIONDECT, get_app_uptime() + delay_sec_close);
	return TRUE;
#else		
	switch (eventType) {
	 	/* 移动侦测 */
	 	case AET_EVENT_MOTIONDECT:
	 	/* 人形侦测 */
	 	case AET_EVENT_PERSONDECT:
	 	{
	 		mfile_set_event(REC_STATE_OPEN, eventType, get_app_uptime() + delay_sec_close);
	 		return TRUE;
	 	}	

	 	/* 没有细分就当作移动侦测来处理 */
	 	default:{
	 		mfile_set_event(REC_STATE_OPEN, AET_EVENT_MOTIONDECT, get_app_uptime() + delay_sec_close);
	 		return TRUE;
	 	}	
	}

	return FALSE;
#endif	
}

#else


// 文件管理线程
LPVOID mfile_mgr_thread(LPVOID arg)
{
	SET_THREAD_NAME();

	UINT8 	val;
	UINT8	abn_try_count = 0;
	UINT8	sdcard_ok = g_tf_is_ready;
	UINT8	tf_stat;
	UINT16	last_switch_day = 99;

#if defined(NAS_ENABLE)				
	// 加载samba
	(VOID)mfile_nas_reload();
#endif	

#if defined(PLAT_FORM_ULC)
	UINT8	abn_try_init = 0;
	//悠络客需要SD卡分区不对时，也需要自动格式化
	T_DISK_INFO disk_info;
#endif

	vs_logdb_set_env(VS_SOURCE_SYS, IPC_MOD_SDCARD);

	while (Module_Running(g_module_media_file_run)) {

		sdcard_ok = sdcard_exist();
		
		// 判断状态是否改变
		if (sdcard_ok != g_tf_is_ready) {
			g_tf_is_ready = sdcard_ok;
			abn_try_count = 0;
			#if defined(PLAT_FORM_ULC)
			abn_try_init = 0;
			#endif
			if (sdcard_ok) { // 可能录在  nas上
				// 关闭录像
				if (g_rec_work.start) {
					mfile_close();
				}
				
				LOGDB_INFO(FORMAT_SDCARD_INSERT);
			}
			else{
				LOGDB_INFO(FORMAT_SDCARD_REMOVE);
			}
			
#ifdef LT_DENGHONG
			dh_tf_state(TRUE);
#else
			;; //vsipc_set_state(VS_LED_TF_ERROR, FALSE);
#endif
		} 
		
		if (!sdcard_ok && !mfile_nas_ready()) {
			
			// 在tf卡不存时,无论如何都关闭计划表, 
			// 中途插入tf卡就可以利用计划表机制立即录像 
			g_rec_work.enable_sche = FALSE;	
			
			// 关闭录像
			if (g_rec_work.start) {
				mfile_close();
			}
		}
		else if (!g_tf_format) {

			#if defined(PLAT_FORM_ULC)			
			//悠络客需要SD卡分区不对时，也需要自动格式化
			QfSet0(&disk_info, sizeof(disk_info));
			UINT32 ret = mfile_get_tf_status(&disk_info);			   
			if(ret EQU TUTK_SD_STATUS_NOTFORMAT){
				if(abn_try_init > 5)
				{				
					abn_try_init = 0;
					mfile_tf_format(); // 在线程中格式化
					Sleep(4000);
					continue;
				}			
				abn_try_init++;
			}			
			#endif

			tf_stat = tf_mount_stat();

			// 只读状态
			if (tf_stat EQU VS_MOUNT_READ_ONLY && access(SDCARD_MOUNT_PATH, W_OK) < 0) {

				// 只读
				//(errno EQU EROFS ? -2 : -1);
				
				LOGE("%s abnormal. try remout count:%d", SDCARD_MOUNT_PATH, abn_try_count);

				// 关闭计划表 
				g_rec_work.enable_sche = FALSE;		
			
				// 关闭录像
				if (g_rec_work.start) {
					mfile_close();
				}

				// 尝试5次以后无果,重启设备
				if (abn_try_count > 5) 
				{					
					abn_try_count = 0;
					#if defined(PLAT_FORM_ULC)
						if(expired_cb != NULL)
							expired_cb(ULC_SDCARD_ERROR_BROKEN);
					#endif
					
					#if !defined(PLAT_FORM_WDZ)
					mfile_tf_format(); // 在线程中格式化
					#endif				
				} 
				else 
				{
					#ifndef LT_DENGHONG				
					;; // vsipc_set_state(VS_LED_TF_ERROR, TRUE);
					#endif
					
					#if !defined(PLAT_FORM_WDZ)
					// 重新挂载
					system_no_fd("mount -o remount,rw " SDCARD_MOUNT_PATH);
					#endif
					
					abn_try_count++;
					Sleep(4000);
					continue;
				}
			} 
			else {
			
			#if defined(NAS_ENABLE)

				// samba判断
				if (!sdcard_ok 
					&& tf_stat != VS_MOUNT_OK
					&& mfile_nas_ready()){
					tf_stat = mount_stat(NAS_MOUNT_PATH, VS_FS_SMB3);
				}
			#endif
							
				if (tf_stat EQU VS_MOUNT_OK) {
					T_DATETIME	now;

					//==============================================
					// 判断计划表
					//==============================================
					get_now(&now);
//					val = g_pRunSets->record.enable
//							&& (g_pRunSets->record.all_time || ((now.tv.tv_sec > MIN_UTC_TIME) && now_in_period(&now, g_pRunSets->record.period)));

					val = 0;

					//LOGI("%s, enable_sche=%d, start=%d, val=%d", __FUNCTION__, g_rec_work.enable_sche, g_rec_work.start, val);
					if (g_rec_work.enable_sche != val) {
						g_rec_work.enable_sche = val;
						if (val) {
							if (!g_rec_work.start) {
								if (!mfile_open(RT_NORMAL)) {
									g_rec_work.enable_sche = FALSE;
								}
							}
						} else {
							if (g_rec_work.start)
								mfile_close();
						}
					}
					
					//==============================================
					// 切换录像文件			
					//==============================================
					else if (g_rec_work.start 
							&& g_rec_work.one_rec.flag EQU OR_FLAG_REC) {
						#if defined(PLAT_FORM_ULC)
						if (now.tv.tv_sec - g_rec_work.one_rec.st_time >= vs_ulc_get_record_file_time()) 
						#else						
						if (now.tv.tv_sec - g_rec_work.one_rec.st_time >= ONE_AVFILE_TM_LEN) 
						#endif
						{	
							mfile_switch();
						}
						// 0点切换文件[确保每天只切换一次last_switch_day != now.usDay]
						else if ((now.ucHour EQU 0 && now.ucMin EQU 0)
								&& last_switch_day != now.usDay) {
							last_switch_day = now.usDay;
							mfile_switch();
						}
					}
					// 有计划表,但是没有打开,则开启录像
					else if (g_rec_work.enable_sche && !g_rec_work.start) {
						mfile_open(RT_NORMAL);
					}

					#ifdef AI_LOCAL_SAVE
					//打开本地AI存储,如果SD卡存在
					vs_ai_local_init();	
					#endif

					//==============================================
					// 清盘
					//==============================================
					
					clear_expired_dir(REC_PATH);

					//==============================================
					// 非全天录像时,并且当前没在录像时间段的时候,在SD卡目录增加一个空文件来刷新时间			
					//==============================================
//					if(!g_pRunSets->record.all_time 
//						&& !((now.tv.tv_sec > MIN_UTC_TIME) && now_in_period(&now, g_pRunSets->record.period))
//						&& g_pRunSets->misc_set.disable_reboot EQU IPC_WORK_DISABLE_NET_SIGANLE_MODE)
					{
						if(!file_exist(REC_TIME_FILE))
							create_time_file(REC_TIME_FILE, "cdyctx/time");
						else
							file_write_string(REC_TIME_FILE, "cdyctx/time");							
					}
				}
			}
					
		}
		
		Sleep(1000);

	}

	mfile_close();
		
	LOGI("%s() exit.", __FUNCTION__);
	
	END_THREAD;
	return NULL;
}

// 录像线程
LPVOID media_thread(LPVOID arg)
{
	SET_THREAD_NAME();

	CHAR		*buffer;
	INT32		fnRet;
	HANDLE		handle = NULL; 
	T_FRAME_HEADER 	*frameHead = NULL;
	UINT8			checkKeyFrame = TRUE;
	T_MEDIA_INFO	media_info;
	UINT8			switch_file = FALSE;
	//UINT8			pause_record = FALSE; (VOID)pause_record; // 避免编译警告
	UINT8			strm_chn = 0; // g_pRunSets->misc_set.rec_stream_chn;

	LOGI("%s() strm_chn:%d,  running...", __func__,  strm_chn);
	
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
        HANDLE          audio_handle = NULL; 

		// hcc 去掉加密
		handle = mb_reader_open_ex(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0, 0x01);
		if (handle EQU INVALID_HANDLE_VALUE) {
			LOGE("mb_reader_open(MEDIA_BUFFER_STREAM_0) error");
			goto fn_end;
		}

		audio_handle = mb_reader_open(MEDIA_BUFFER_AAC);
		if (audio_handle EQU INVALID_HANDLE_VALUE) {
			LOGE("mb_reader_open(MEDIA_BUFFER_AAC) error");
			goto fn_end;
		}
#else

		// 魔镜等
		handle = mb_reader_open(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0);
		if (handle EQU INVALID_HANDLE_VALUE) {
			LOGE("mb_reader_open(MEDIA_BUFFER_STREAM_0) error");
			goto fn_end;
		}
#endif
	
	QfSet0(&media_info, sizeof(media_info));
	while (Module_Running(g_module_media_file_run)) {

		frameHead = NULL;
		
#ifdef CN21_ENABLE
		
		if (system_is_dormancy()) {	// 休眠
			if (g_rec_work.start) {
				mb_reader_close(handle);
				handle = INVALID_HANDLE_VALUE;
				mfile_close();
				//pause_record = TRUE;
			}
			Sleep(1000);
		}
		
		//else if (pause_record) 
		else
		{				
			if (g_rec_work.enable_sche) {
				if (!g_rec_work.start && mfile_open(RT_NORMAL)) {
					strm_chn = 0;
					handle = mb_reader_open(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0);
					checkKeyFrame = TRUE;
					QfSet0(&media_info, sizeof(media_info));
					switch_file = FALSE;
				}
			} 
		}
#endif

		// 切换录像文件
		if (switch_file || strm_chn != 0) {
			LOGI("%s() strm_chn:%hhu, rec_stream_chn:%hhu, switch_file:%hhu", __func__,  strm_chn, 
				0, switch_file);
			strm_chn = 0;
			mb_reader_close(handle);
	#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)	
			handle = mb_reader_open_ex(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0, 0x01);
	#else	
			handle = mb_reader_open(strm_chn ? MEDIA_BUFFER_STREAM_1: MEDIA_BUFFER_STREAM_0);
	#endif
			checkKeyFrame = TRUE;
			if (g_rec_work.start) {
				mfile_switch();
			}			
			QfSet0(&media_info, sizeof(media_info));
			switch_file = FALSE;
		}

		if (!g_rec_work.start) {
			mb_reader_nop(handle);
			Sleep(20);
			continue;
		}
	

		// 读视频
		fnRet = mb_reader_pop(handle, &frameHead, &buffer);
		if (fnRet != OK) {

			// 有跳帧,必须等到I帧
			if (fnRet EQU FAIL-1) 
				checkKeyFrame = TRUE;
		}
		else {

			// I帧的时候,录像文件超过5分钟(比如报警录像一直触发,g_rec_work.rec_info.abs_end_time这个时间就会一直叠加)
			if (frameHead->frameType EQU FT_IFRAME){				
				if (frameHead->utcTime >= g_rec_work.one_rec.st_time + ONE_AVFILE_TM_LEN
					|| (g_rec_work.one_rec.rec_type != RT_NORMAL && get_app_uptime() >= g_rec_work.rec_info.abs_end_time)) {
					mfile_switch();
					checkKeyFrame = TRUE;
				}
			} 

			// 视频编码改变
			if (media_update(frameHead->frameType EQU FT_AUDIO ? FALSE: TRUE, frameHead, &media_info)) {
				switch_file = TRUE;				
				continue;
			}			
			
			UINT64 timestamp	= frameHead->timeStamp*1000;
			
			do {
				
				// 检查I帧
				if (checkKeyFrame && frameHead->frameType != FT_IFRAME)
					break;

				if (frameHead->frameType EQU FT_AUDIO){
#if !defined(ENABLE_AAC_ENC) || !defined(CMCC_HJQ)
					// 非软编AAC
					mfile_write_audio((BYTE*)buffer, frameHead->size, timestamp);	
#endif
				}
				else {
					checkKeyFrame = FALSE;
					
					#ifdef	 CMCC_HJQ
						// 和家亲--I帧--AES加密码流
						UINT8		*enc_buf = NULL;		// 和家亲加密SD卡数据
						UINT8		*enc_buf_tmp = NULL;
						UINT32		enc_len = 0;
						enc_buf = (UINT8 *)malloc(frameHead->size+128);
						if (enc_buf EQU NULL){
							LogE("malloc Err, return");
							break;
						}
						QfSet0(enc_buf, frameHead->size+128);
						enc_buf_tmp = enc_buf;
						memcpy(enc_buf_tmp, buffer, SD_I_NOT_ENC_LEN);
						enc_buf_tmp += SD_I_NOT_ENC_LEN;			
						aes_encode((UINT8*)buffer+SD_I_NOT_ENC_LEN, frameHead->size-SD_I_NOT_ENC_LEN, enc_buf_tmp, &enc_len);
						enc_len += SD_I_NOT_ENC_LEN;
						mfile_write_video(enc_buf, enc_len, frameHead->frameType EQU FT_IFRAME ? 1 : 0);
						FreeAndNull(enc_buf);

					#else
						mfile_write_video((BYTE*)buffer, frameHead->size, frameHead->frameType EQU FT_IFRAME ? 1 : 0, timestamp);	
					#endif
				}
			} while (FALSE);
		}
		
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
		// 软编AAC
		if (!checkKeyFrame) {
			// 读音频
			frameHead = NULL;
			fnRet = mb_reader_pop(audio_handle, &frameHead, &buffer);
			if(fnRet EQU OK){
				UINT64 timestamp	= frameHead->timeStamp*1000;
				fnRet = mfile_write_audio((BYTE*)buffer, frameHead->size, timestamp);
			}
		}else {
			mb_reader_nop(handle);
		}
#endif

		// 未读到任何数据
		if (frameHead EQU NULL) {
			Sleep(20);
		}

	}

fn_end:	
	mb_reader_close(handle);
#if defined(ENABLE_AAC_ENC) && defined(CMCC_HJQ)
	mb_reader_close(audio_handle);
#endif

	LOGI("%s() exit.", __FUNCTION__);
	
	END_THREAD;
	return NULL;
}


/**
 * 切换文件
 * @return	 成功返回1,失败返回0
 */
VOID mfile_switch()
{
	mfile_close();	
	if (g_rec_work.rec_info.abs_end_time > get_app_uptime()) {	

		// 定时录像下个结束时间切换后重新计算
		if (g_rec_work.one_rec.rec_type EQU RT_NORMAL) 
			g_rec_work.rec_info.abs_end_time = get_app_uptime() + ONE_AVFILE_TM_LEN;
		
		mfile_open(static_cast<VS_RECORD_TYPE>(g_rec_work.one_rec.rec_type));
	}
	else {
		mfile_open(RT_NORMAL);
	}	

}
/** 
 * 发送消息
 * @param  eventType  事件类型
 * @param  delay_sec_close  持续多少秒后关闭，=0使用内置策略
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_push_msg(ALARM_EVENT_TYPE eventType, INT32 delay_sec_close)
{
#if 1		
	if (delay_sec_close < 5)
		delay_sec_close = ALARM_REC_TM_LEN;	// 小于5秒统一使用20秒
	
	mfile_open(RT_MOTION_DETECT);
	g_rec_work.rec_info.abs_end_time = get_app_uptime() + delay_sec_close;
	return TRUE;
#else		
	switch (eventType) {
		/* 移动侦测 */
		case AET_EVENT_MOTIONDECT:{			
			mfile_open(RT_MOTION_DETECT);
			return TRUE;
		}		
		
		/* 人形侦测 */
		case AET_EVENT_PERSONDECT:{
			//mfile_open(RT_PERSON_DETECT);
			mfile_open(RT_MOTION_DETECT);
			return TRUE;
		}	
		
		/* 没有细分就当作移动侦测来处理 */
		default:
			mfile_open(RT_MOTION_DETECT);
			return TRUE;
	}

	return FALSE;
#endif
}

#endif


/**
 * mf初始化
 * @return   成功=OK；失败=FAIL
 */
INT32 mfile_init()
{
	INT32	dev_id = 0;
	INT32	dev_p_id = 1;

	g_module_media_file_run = TRUE;
	g_rec_work.event_cb = NULL;	
	do {

#if 0	
		if (g_pSystemSets->stat.emmc_as_sd){	// emmc做sd卡
			if (file_exist("/dev/mmcblk1")){	// 并且外部的SDIO没有插上
				LogE("emmc_as_sd(defconfig/system.json): but TF-Card exist.");
			}
			else if (file_exist("/dev/mmcblk0boot0")) {		// 的确是emmc做引导
				LPCSTR sdfile_cmdline[] = {
					"awk -F '[ =]' '{for(i=1;i<=NF;i++){if($i==\"blkdevparts\") print $(i+1)}}' /proc/cmdline|awk -F '[:,]' '{for(i=2;i<=NF;i++) if ($i~/storage/) print (i-1);}'",
					"awk -F '[ =]' '{for(i=1;i<=NF;i++){if($i==\"blkdevparts\") print $(i+1)}}' /proc/cmdline|awk -F '[:,]' '{for(i=2;i<=NF;i++) if ($i~/tfcard/) print (i-1);}'",
				};
				dev_id = 0;			
				
				for (int i = 0; i < 2; i++){	// emmc切换一部分做存储时,要看有没有
					FILE *pfile = popen(sdfile_cmdline[i], "r");

					if (NULL != pfile) {
						fread(sg_tf_dev_file.dev_file_p1_short, 1, sizeof(sg_tf_dev_file.dev_file_p1_short) - 1, pfile);
						pclose(pfile);
						if (strlen(sg_tf_dev_file.dev_file_p1_short) > 0) {
							dev_p_id = atoi(sg_tf_dev_file.dev_file_p1_short);
							sg_tf_dev_file.emmc_vfat = i > 0 ? TRUE : FALSE;
							break;
						}
					}
				}
				LogW("emmc_as_sd dev_id=%d, p_id=%d", dev_id, dev_p_id);
				break;
			}
			else {
				LogE("emmc_as_sd(defconfig/system.json): but emmc not exist.");
			}
			g_pSystemSets->stat.emmc_as_sd = FALSE;
		}


		// 常规方式
		{
			LogW("chipid=%#x dev_id=%d", g_pSystemSets->chip.chipid, dev_id);
			if (file_exist("/proc/mtd")) {	// nand or nor			
				dev_id = 0;	// emmc与sdio都用mmc0
			}
			else {
				dev_id = file_exist("/dev/mmcblk0boot0") ? 1 : 0;
			}
		} 
#endif		
	}while (FALSE);

	sprintf(sg_tf_dev_file.dev_file_short, "mmcblk%d", dev_id);
	sprintf(sg_tf_dev_file.dev_file, "/dev/%s", sg_tf_dev_file.dev_file_short);
	sprintf(sg_tf_dev_file.dev_file_p1_short, "mmcblk%dp%d", dev_id, dev_p_id);
	sprintf(sg_tf_dev_file.dev_file_p1, "/dev/%s", sg_tf_dev_file.dev_file_p1_short);

	LogW("sdio_file=%s", sg_tf_dev_file.dev_file_p1_short);

	// 检测tf是否就绪
	if (sdcard_exist()) {
		g_tf_is_ready = TRUE;
		if (!tf_mount_stat()){	// 挂载sd卡
			CHAR	cmdline[256];

			sprintf(cmdline, "%sboot0", TF_DEV_FILE);
			UINT8	is_emmc = file_exist(cmdline) ? TRUE : FALSE;
			
			QfSet0(cmdline, sizeof(cmdline));	 
			if (is_emmc) {
//				sprintf(cmdline, 
//						"mount %s %s;mount %s /mnt/storage;"
//						"mkdir /mnt/storage/update; "
//						"mount /mnt/storage/update /opt/update", 
//						TF_DEV_FILE_P1, SDCARD_MOUNT_PATH, SDCARD_MOUNT_PATH);
				sprintf(cmdline, 
						"mount %s %s", 
						 TF_DEV_FILE_P1, SDCARD_MOUNT_PATH);
			}
			else {
				sprintf(cmdline, "mount -o iocharset=utf8 %s %s&", TF_DEV_FILE_P1, SDCARD_MOUNT_PATH);	
			}
			system_no_fd(cmdline);
		}
	}
	else{
		if(mfile_tf_exist()){
			mfile_tf_format();
		}
	}		

#if 0	
	// tf卡检测线程
	{
		pthread_t	thrd;
		PTHREAD_CREATE(&thrd, NULL, tf_detect_thread, NULL);
	}
#endif

	// 非工厂模式
	if (!get_factory_mode()) {
		// tf卡管理[清盘,或是状态切换]
		PTHREAD_CREATE(&g_workThread, NULL, mfile_mgr_thread, NULL);
		//通过文件恢复时间
		mfile_restore_datetime();
	} else {
		LOGW("factory_mode:%d, disable tf manager and record.", get_factory_mode());
	}

#if defined(AVF_FFMPEG)
	 pthread_begin(&g_mediaThread, media_thread, NULL, 0x100000);
#else
	 PTHREAD_CREATE(&g_mediaThread, NULL, media_thread, NULL); 
#endif	
	
	LOGI("mfile module init.");

	return OK;
}

/**
 * mfile反初始化
 */
VOID mfile_uninit()
{
	if (!g_module_media_file_run)
		return;
	
	g_module_media_file_run = FALSE;

	WAIT_THREAD(g_workThread, NULL);
	WAIT_THREAD(g_mediaThread, NULL);
	mfile_pcm_stop();

	LOGI("mfile module uninit.");
	
	return;
}




/**
 * 切换录像流
 * @param  stream_chn 	流通道号
 */
VOID mfile_switch_stream(UINT8	stream_chn)
{
	// g_pRunSets->misc_set.rec_stream_chn = stream_chn;
}

/**
 * 设置录像勾子函数
 * @param  record_cb 	录像勾子函数, 不使用时可以置NULL
 */
VOID mfile_set_record_hook(fn_record_callback record_cb)
{
	g_rec_work.event_cb = record_cb;
}


/**
 * 判断TF卡是否存在 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_tf_exist()
{
	return file_exist(TF_DEV_FILE) ? TRUE:FALSE;
}

/**
 * 判断TF卡是否就绪(必须分区正常,mount成功) 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_tf_ready()
{
	if (g_tf_is_ready 
		&& g_tf_format EQU 0 
		&& sdcard_exist()
		&& tf_mount_stat() EQU VS_MOUNT_OK) {
		return TRUE;
	}

#if defined(NAS_ENABLE)
	if (mfile_nas_ready()
		&& mount_stat(NAS_MOUNT_PATH, VS_FS_SMB3) EQU VS_MOUNT_OK) {
		return TRUE;
	}
#endif	

	return FALSE;
}


/**
 * 得到TF卡磁盘信息 
 * @return	 成功返回1,失败返回0
 */
UINT32 mfile_get_tf_status(T_DISK_INFO *diskinfo)
{
	bzero(diskinfo, sizeof(T_DISK_INFO));
	if (!mfile_tf_exist())
		return TUTK_SD_STATUS_NOTINIT;

	if (g_tf_format) {
		diskinfo->format_prog = tf_format_progress();
		return TUTK_SD_STATUS_FORMATING;
	}
	
	get_disk_info(REC_PATH, diskinfo);
	if (diskinfo->status EQU 0)
		return TUTK_SD_STATUS_NOTFORMAT;
	else if (diskinfo->status < 0)
		return TUTK_SD_STATUS_READONLY;

	return TUTK_SD_STATUS_OK;
}



/**
 * 格式化tf卡线程
 */
LPVOID tf_format_thread(LPVOID)
{
	THREAD_FUNC_BEGIN();	 
	CHAR	cmdline[1024];

	QfSet0(cmdline, sizeof(cmdline));
	sprintf(cmdline, "%sboot0", TF_DEV_FILE);
	UINT8	is_emmc = file_exist(cmdline) ? TRUE : FALSE;

	vs_logdb_set_env(VS_SOURCE_SYS, IPC_MOD_SDCARD);
	g_tf_format = 2;
	
	// 需要移动数据库
	vs_logdb_bakup(); // 先备份数据
	LOGDB_INFO(FORMAT_SDCARD_START);

#ifdef LT_DENGHONG
	dh_tf_state(FALSE);
#endif

	// 关录像,停计划表
	mfile_close();
	g_rec_work.enable_sche = FALSE;
	Sleep(500);

#ifdef AI_LOCAL_SAVE
	//关闭本地AI存储
	vs_ai_local_uninit();
	Sleep(500);
#endif

//	//人流统计关闭数据库
//	if(counter_format_cb != NULL)
//	{		
//		counter_format_cb();
//		Sleep(500);
//	}

	// umount	
	QfSet0(cmdline, sizeof(cmdline));
	sprintf(cmdline, "mount /tmp /etc/udev; umount %s;"
		"num=`cat /proc/mounts|grep %s|wc -l`; for i in {1..$num} ;"
		" do umount %s; done;", TF_DEV_FILE_P1, TF_DEV_FILE_P1, TF_DEV_FILE_P1);
#if defined(LH_LWJ)
	strcat(cmdline, "sleep 1;/opt/defconfig/Disable_Storage.sh;sync;sleep 1");
#endif	
	system_no_fd(cmdline);

#if 0
	if ((file_exist(TF_DEV_FILE) && !file_exist(TF_DEV_FILE_P1)) 	// 如果emmc纯做存储又没有分区
		|| !g_pSystemSets->stat.emmc_as_sd){	// emmc做存储时,由于是切的一部分分区,所以不能重新分区了
		LOGW("fdisk %s", TF_DEV_FILE_P1);
		sdcard_fdisk(!sg_tf_dev_file.emmc_vfat && is_emmc);
	}


	g_tf_format = 1;

	// 格式化并mount
	QfSet0(cmdline, sizeof(cmdline));	 
	if (!sg_tf_dev_file.emmc_vfat && is_emmc) {
		char *p = cmdline;
		p += sprintf(p, "for i in {1..15} ; do [ -b %s ] && break; sleep 0.2; done"
				   ";which mkfs.ext4; sleep 0.5;if [ $? -eq 0 ]; then mkfs.ext4 -qF %s;else mkfs.ext2 -F %s;fi"
				   ";sleep 0.5;mount %s %s;umount /etc/udev",
				   TF_DEV_FILE_P1, TF_DEV_FILE_P1, TF_DEV_FILE_P1, TF_DEV_FILE_P1, SDCARD_MOUNT_PATH);
		if (g_pSystemSets->stat.emmc_as_sd){ // emmc做存储时
			p += sprintf(p, ";mount %s /mnt/storage;"
							"mkdir /mnt/storage/update &&"
							"mount /mnt/storage/update /opt/update && "
							"mount /opt/update " UPDATE_FW_DIR,
						TF_DEV_FILE_P1);
		}
	}
	else {
		sprintf(cmdline, "for i in {1..15} ; do [ -b %s ] && break; sleep 0.2; done"
				   ";sleep 0.5;mkfs.vfat %s"  
#if defined(LH_LWJ)
					";sync;sleep 2;/opt/defconfig/Config_Storage.sh;sleep 5"
#endif	
				   ";mount -o iocharset=utf8 %s %s;umount /etc/udev",
				   TF_DEV_FILE_P1, TF_DEV_FILE_P1, TF_DEV_FILE_P1, SDCARD_MOUNT_PATH);	
	}
#endif

	system_no_fd(cmdline);

	// 检查卡是否存在
	for (INT32 i = 0; i < 10; i++){
		if (tf_mount_stat()) 
			break;		
		Sleep(500);
	}
	//Sleep(1000);

	vs_logdb_restore();
	g_tf_format = 0;
	LOGDB_INFO(FORMAT_SDCARD_END);

#ifdef CN21_ENABLE
	// SD卡状态改变的能力集上报回调
	my_ability_func();

	T_LOG_REPOR log_report;
	strcpy(log_report.log_url, FORMATSDCARD);
	strcpy(log_report.log_msg, "FormatSDcard OK");
	log_report.log_num = 0;
	log_report.log_et_enable = 0;
	
	my_log_report(&log_report);

#ifdef CN21_SD_DB
	// SD卡缓存，需要重启，不然缓存文件在下次启动后丢失
	system_reboot();
#endif
	
#endif
	

#ifdef LT_DENGHONG
	dh_tf_state(TRUE);
#endif


	THREAD_FUNC_END();

	return NULL;
}


/**
 * 格式化tf卡
 * @return	 成功返回1,失败返回0 
 */
UINT8 mfile_tf_format()
{
	if (!mfile_tf_exist())
		return 0;

	if (!g_tf_format) {

		g_tf_format = 1;
		tf_format_progress(tf_dev_total_size(256<<10));
		if (PTHREAD_CREATE(NULL, NULL, tf_format_thread, NULL)) {
			g_tf_format = 0;
			return 0;
		}		

		return 1;
	} 

	return 1;
}


/**
 * 回放查询
 * @param  avSocket tutk套接字
 * @param  buf      请求缓冲
 * @return          无
 */
 #define TUTK_MAX_LIST_COUNT ((MAX_SIZE_IOCTRL_BUF - sizeof(SMsgAVIoctrlListEventResp)) / sizeof(SAvEvent) + 1)
VOID mfile_tutk_query(LPVOID	avSocket, CHAR *buf, fn_send_msg fn_send)
{
	SMsgAVIoctrlListEventReq 	*p = (SMsgAVIoctrlListEventReq *)buf;
	CHAR	path[200];
	FILE	*pf;

#ifdef USE_HTS_PROTOCOL
	extern void time_convert(STimeDay *td, int zone);

	time_convert(&p->stStartTime, 8 * 60);
#endif

	sprintf(path, "%s/" REC_DIR_FMT "/" REC_DB_INDEX, 
		REC_PATH, p->stStartTime.year, p->stStartTime.month, p->stStartTime.day);
	mfile_new_dbfile(path);
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{
		UINT32		i;
		LONG		remain_total;
		T_ONE_REC	one_rec;
		SMsgAVIoctrlListEventResp *resp;

		// 得到记录条数
		fseek(pf, 0, SEEK_END);
		remain_total = ftell(pf)/sizeof(T_ONE_REC);
		rewind(pf);

		// 分配内存
		resp = (SMsgAVIoctrlListEventResp *)malloc(MAX_SIZE_IOCTRL_BUF);
		bzero(resp, MAX_SIZE_IOCTRL_BUF);
		resp->channel = p->channel;
		
		LOGI("mfile_tutk_query() total=%ld", remain_total);

		// 读出所有数据
		for (; !resp->endflag; resp->index++) {
			resp->count = 0;
			for (i = 0; i < TUTK_MAX_LIST_COUNT;)
			{
				if (fread(&one_rec, sizeof(one_rec), 1, pf) != 1) {
					remain_total = 0;	// 读失败了, 没有剩余的记录了
					break;
				}

				if (one_rec.flag != OR_FLAG_OK) 
					continue;

				QfInit(resp->stEvent[i]);
				resp->stEvent[i].event = (one_rec.rec_type EQU RT_NORMAL ?  
										AVIOCTRL_EVENT_ALL : AVIOCTRL_EVENT_MOTIONDECT);	// 录像类型								
				
#ifdef USE_HTS_PROTOCOL
				LTIME_2_PARSE(one_rec.st_time, resp->stEvent[i].stBeginTime);
				time_convert(&resp->stEvent[i].stBeginTime, -1*8 * 60);
#else
				LTIME_2_PARSE(one_rec.st_time, resp->stEvent[i].stBeginTime);
				LTIME_2_PARSE(one_rec.end_time, resp->stEvent[i].stEndTime);
#endif

				
//				if (resp->stEvent[i].stEndTime.day > resp->stEvent[i].stBeginTime.day) {
//					resp->stEvent[i].stEndTime.day		= resp->stEvent[i].stBeginTime.day;
//					resp->stEvent[i].stEndTime.hour 	= 23;
//					resp->stEvent[i].stEndTime.minute 	= 59;
//					resp->stEvent[i].stEndTime.second 	= 59;
//				}
//				
//				printf("page: %2d, index:%2d, beg: %04d-%02d-%02d %02d:%02d:%02d ~ end: %04d-%02d-%02d %02d:%02d:%02d\n",
//					resp->index, resp->count,
//                   	resp->stEvent[i].stBeginTime.year,
//                    resp->stEvent[i].stBeginTime.month,
//                    resp->stEvent[i].stBeginTime.day,
//                    resp->stEvent[i].stBeginTime.hour,
//                    resp->stEvent[i].stBeginTime.minute,
//                    resp->stEvent[i].stBeginTime.second,
//                    resp->stEvent[i].stEndTime.year,
//                    resp->stEvent[i].stEndTime.month,
//                    resp->stEvent[i].stEndTime.day,
//                    resp->stEvent[i].stEndTime.hour,
//                    resp->stEvent[i].stEndTime.minute,
//                    resp->stEvent[i].stEndTime.second);
				
				remain_total--;
				i = ++resp->count;
			}
			
			resp->endflag = (remain_total EQU 0 ? 1 : 0);
			printf("IOTYPE_USER_IPCAM_LISTEVENT_RESP count=%d, isover=%d\n", resp->count, resp->endflag);

			// 发送数据
			(*fn_send)(avSocket, IOTYPE_USER_IPCAM_LISTEVENT_RESP, (char *)resp,
					sizeof(SMsgAVIoctrlListEventResp) - sizeof(resp->stEvent[0]) + resp->count*sizeof(resp->stEvent[0]));
		}
		free(resp);	
		fclose(pf);
		
		return;
	}
	
	LOGI("mfile_tutk_query() no file, from: %s", path);

	// 回复
	{
		SMsgAVIoctrlListEventResp 	res;

		QfInit(res);
		res.channel = p->channel;
		res.endflag = 1;
		(*fn_send)(avSocket, IOTYPE_USER_IPCAM_LISTEVENT_RESP, (char *)&res,
					sizeof(SMsgAVIoctrlListEventResp) - sizeof(res.stEvent[0]));
	}
}

#define TUTK_MAX_FILE_LIST_COUNT ((MAX_SIZE_IOCTRL_BUF - sizeof(SMsgAVIoctrlListFileEventResp)) / sizeof(SfileNameEvent) + 1)
VOID mfile_tutk_query_file(LPVOID	avSocket,       CHAR *buf, fn_send_msg fn_send)
{
	SMsgAVIoctrlListEventReq	*p = (SMsgAVIoctrlListEventReq *)buf;
	CHAR	path[200];	
	FILE	*pf;	

	sprintf(path, "%s/" REC_DIR_FMT "/" REC_DB_INDEX, 
		REC_PATH, p->stStartTime.year, p->stStartTime.month, p->stStartTime.day);
	mfile_new_dbfile(path);
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{
		UINT32		i;
		INT32		pos = strlen(REC_PATH)+1;
		LONG		remain_total;
		T_ONE_REC	one_rec;
		time_t		query_beg = to_timet(&p->stStartTime);
		time_t		query_end = to_timet(&p->stEndTime);
		SMsgAVIoctrlListFileEventResp *resp;

		// 得到记录条数
		fseek(pf, 0, SEEK_END);
		remain_total = ftell(pf)/sizeof(T_ONE_REC);
		rewind(pf);

		// 分配内存
		resp = (SMsgAVIoctrlListFileEventResp *)malloc(MAX_SIZE_IOCTRL_BUF);
		bzero(resp, MAX_SIZE_IOCTRL_BUF);
		resp->channel = p->channel;
		
		LogI("total=%ld", remain_total);

		// 读出所有数据
		for (; !resp->endflag; resp->index++) {
			resp->count = 0;
			for (i = 0; i < TUTK_MAX_FILE_LIST_COUNT;)
			{
				if (fread(&one_rec, sizeof(one_rec), 1, pf) != 1) {
					remain_total = 0;	// 读失败了, 没有剩余的记录了
					break;
				}

				if (one_rec.flag != OR_FLAG_OK) 
					continue;

				QfSet0(&resp->stEvent[i], sizeof(resp->stEvent[i]));
				resp->stEvent[i].event = (one_rec.rec_type EQU RT_NORMAL ?	
										AVIOCTRL_EVENT_ALL : AVIOCTRL_EVENT_MOTIONDECT);	// 录像类型
				
#ifdef USE_HTS_PROTOCOL
				LTIME_2_PARSE(one_rec.st_time, resp->stEvent[i].stBeginTime);
#else
				LTIME_2_PARSE(one_rec.st_time, resp->stEvent[i].stBeginTime);
				LTIME_2_PARSE(one_rec.end_time, resp->stEvent[i].stEndTime);
#endif
				strncpy(resp->stEvent[i].fileName, &one_rec.file_name[pos], sizeof(resp->stEvent[i].fileName)-1);

				if (query_beg > to_timet(&resp->stEvent[i].stEndTime) 
					|| to_timet(&resp->stEvent[i].stBeginTime) > query_end) {
					continue;
				}

//				if (resp->stEvent[i].stEndTime.day > resp->stEvent[i].stBeginTime.day) {
//					resp->stEvent[i].stEndTime.day		= resp->stEvent[i].stBeginTime.day;
//					resp->stEvent[i].stEndTime.hour 	= 23;
//					resp->stEvent[i].stEndTime.minute 	= 59;
//					resp->stEvent[i].stEndTime.second 	= 59;
//				}
//				
//				printf("page: %2d, index:%2d, beg: %04d-%02d-%02d %02d:%02d:%02d ~ end: %04d-%02d-%02d %02d:%02d:%02d\n",
//					resp->index, resp->count,
//                   	resp->stEvent[i].stBeginTime.year,
//                    resp->stEvent[i].stBeginTime.month,
//                    resp->stEvent[i].stBeginTime.day,
//                    resp->stEvent[i].stBeginTime.hour,
//                    resp->stEvent[i].stBeginTime.minute,
//                    resp->stEvent[i].stBeginTime.second,
//                    resp->stEvent[i].stEndTime.year,
//                    resp->stEvent[i].stEndTime.month,
//                    resp->stEvent[i].stEndTime.day,
//                    resp->stEvent[i].stEndTime.hour,
//                    resp->stEvent[i].stEndTime.minute,
//                    resp->stEvent[i].stEndTime.second);				
				
				remain_total--;
				i = ++resp->count;
			}
			
			resp->endflag = (remain_total EQU 0 ? 1 : 0);
			printf("IOTYPE_USER_IPCAM_LISTEVENT_FILE_RESP count=%d, isover=%d\n", resp->count, resp->endflag);

			// 发送数据
			(*fn_send)(avSocket, IOTYPE_USER_IPCAM_LISTEVENT_FILE_RESP, (char *)resp,
					sizeof(SMsgAVIoctrlListFileEventResp) - sizeof(resp->stEvent[0]) + resp->count*sizeof(resp->stEvent[0]));
		}
		free(resp); 
		fclose(pf);
		
		return;
	}
	
	LOGI("%s() no file, from: %s", __FUNCTION__, path);

	// 回复
	{
		SMsgAVIoctrlListFileEventResp	res;

		QfSet0(&res, sizeof(res));
		res.channel = p->channel;
		res.endflag = 1;
		(*fn_send)(avSocket, IOTYPE_USER_IPCAM_LISTEVENT_FILE_RESP, (char *)&res,
					sizeof(SMsgAVIoctrlListFileEventResp) - sizeof(res.stEvent[0]));
	}

}


/**
 * 回放查询
 * @param  rec_type 	录像类型
 * @param  query_beg    开始时间
 * @param  query_end    结束时间
 * @param  f_list    	录像文件列表
 * @param  desc    		倒序
 * @return          记录条数 
 */
INT32 mfile_query_file_ex(int snridx, INT32 rec_type, time_t		query_beg, time_t query_end, t_vs_list *f_list, UINT8 desc)

{	
	CHAR	path[200];	
	FILE	*pf;		
	INT32	fnum = 0;

	// 解析日期
	{
		STimeDay stStartTime;

		LTIME_2_PARSE(query_beg, stStartTime);

		sprintf(path, "%s/" REC_DIR_FMT "/" REC_DB_INDEX, 
			REC_PATH, stStartTime.year, stStartTime.month, stStartTime.day);
	}
	mfile_new_dbfile(path);

	// 读记录
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{		
		LONG		remain_total;
		T_ONE_REC	one_rec;		

		// 得到记录条数
		fseek(pf, 0, SEEK_END);
		remain_total = ftell(pf)/sizeof(T_ONE_REC);
		rewind(pf);
		
		LOGI_NF("%s() total=%ld, query type:%d, beg:%lu, end:%lu", __FUNCTION__, 
			remain_total, rec_type, (ULONG)query_beg, (ULONG)query_end);

		// 读出所有数据
		while (TRUE) {
			if (fread(&one_rec, sizeof(one_rec), 1, pf) != 1) {
				remain_total = 0;	// 读失败了, 没有剩余的记录了
				break;
			}

			if (one_rec.flag != OR_FLAG_OK) 
				continue;

			if (query_beg > one_rec.end_time
				|| one_rec.st_time > query_end) {
				continue;
			}

			if (rec_type != RT_ALL_TYPE) {
				if (rec_type != one_rec.rec_type)
					continue;
			}

			T_ONE_REC_ITEM	*item = (T_ONE_REC_ITEM *)malloc(sizeof(T_ONE_REC_ITEM));

			QfSet0(item, sizeof(T_ONE_REC_ITEM));
			memcpy(&item->one_rec, &one_rec, sizeof(one_rec));

			if (desc)
				list_add(&item->flist, f_list);
			else
				list_add_tail(&item->flist, f_list);
			fnum++;

		}
		fclose(pf);
		
	}
	
	LOGI("%s() from: %s, list count: %d", __FUNCTION__, path, fnum);

	return fnum;	
}

INT32 mfile_query_file(INT32 rec_type, time_t		query_beg, time_t query_end, t_vs_list *f_list, UINT8 desc)
{
	return mfile_query_file_ex(0, rec_type, query_beg, query_end, f_list, desc);
}


/**
 * 回放日期区间列表查询
 * @param  avSocket tutk套接字
 * @param  buf      请求缓冲
 * @return          无
 */
VOID mfile_tutk_query_list(LPVOID	avSocket,       CHAR *buf, fn_send_msg fn_send)
{
	FILE		*pf;
	UINT32		total = 0;
	INT32		date, t_b, t_e;	
	CHAR		path[64];	
	char		dest[260];	
	DIR 		*dir;
	struct dirent 	*file;
	struct	stat	tStat;	
	SMsgAVIoctrlListEventReq 	*p = (SMsgAVIoctrlListEventReq *)buf;
	SMsgAVIoctrlListEventResp 	*resp = (SMsgAVIoctrlListEventResp *)malloc(MAX_SIZE_IOCTRL_BUF);

	bzero(resp, MAX_SIZE_IOCTRL_BUF);
	resp->channel = p->channel;

	// 得到起始时间
	sprintf(path, REC_DIR_FMT, p->stStartTime.year, p->stStartTime.month, p->stStartTime.day);
	t_b = atoi(path);
	sprintf(path, REC_DIR_FMT, p->stEndTime.year, p->stEndTime.month, p->stEndTime.day);
	t_e = atoi(path);

	// 查找目录 	
	strcpy(path, REC_PATH);
	dir = opendir(path);
	if (dir) {
		
		while ((file = readdir(dir)) != NULL) {
			
			if (strcmp(file->d_name, ".") EQU 0 
				|| strcmp(file->d_name, "..") EQU 0)
				continue;
				
			sprintf(dest, "%s/%s", path, file->d_name);
			lstat(dest, &tStat);

			if (S_ISDIR(tStat.st_mode)) {
				date = atoi(file->d_name);
				if (QfInRange(date, t_b, t_e)) {

					CHAR fname[200];

					sprintf(fname, "%s/" REC_DB_INDEX, dest);
					mfile_new_dbfile(fname);
					pf = fopen(fname, "rb");
					if (pf != NULL) 
					{
						UINT32		pos;
						T_ONE_REC	one_rec;

						// 读出一条有效数据
						while (TRUE)
						{
							if (fread(&one_rec, sizeof(one_rec), 1, pf) != 1) {
								break;
							}

							if (one_rec.flag != OR_FLAG_OK) 
								continue;

							pos = total%TUTK_MAX_LIST_COUNT;
							resp->index = total/TUTK_MAX_LIST_COUNT;
							
							QfInit(resp->stEvent[pos]);
							resp->stEvent[pos].event = (one_rec.rec_type EQU RT_NORMAL ?  
													AVIOCTRL_EVENT_ALL : AVIOCTRL_EVENT_MOTIONDECT);	// 录像类型
							
#ifdef USE_HTS_PROTOCOL		
							LTIME_2_PARSE(one_rec.st_time, resp->stEvent[pos].stBeginTime);
#else
							LTIME_2_PARSE(one_rec.st_time, resp->stEvent[pos].stBeginTime);
							LTIME_2_PARSE(one_rec.end_time, resp->stEvent[pos].stEndTime);
#endif

							++resp->count;
							++total;
							break;
						}
							
						// 发送数据
						if (resp->count EQU TUTK_MAX_LIST_COUNT) {
							(*fn_send)(avSocket, IOTYPE_USER_IPCAM_LISTEVENT_DATE_RESP, (char *)resp,
									sizeof(SMsgAVIoctrlListEventResp) - sizeof(resp->stEvent[0]) + resp->count*sizeof(resp->stEvent[0]));
							bzero(resp, MAX_SIZE_IOCTRL_BUF);
							resp->channel = p->channel;
						}		
						
						fclose(pf);
						
					}
					
				}
			} 
		}
		
		closedir(dir);

	}

	// 回复
	{
		resp->endflag = 1;
		(*fn_send)(avSocket, IOTYPE_USER_IPCAM_LISTEVENT_DATE_RESP, (char *)resp,
					sizeof(SMsgAVIoctrlListEventResp) - sizeof(resp->stEvent[0]) + resp->count*sizeof(resp->stEvent[0]));
	}
	
	free(resp); 
}


/**
 * 录像纯音频文件
 * @param  filename  文件名 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_pcm_record(LPCSTR filename)
{
	INT32 fd;
	INT32 mask;
	
	mfile_pcm_stop();

	mask = umask(0);
	umask (mask);

	fd = open(filename, O_RDWR|O_CREAT, (S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH) &~ mask);
	if (fd < 0)
		return FALSE;

	g_pcm_fd = fd;

	return TRUE;
}

/** 
 * 写入纯音频数据
 * @param  buff  缓冲
 * @param  size  大小
 */
VOID mfile_pcm_audio(UINT8 *buff, UINT32 size)
{
	if (g_pcm_fd != INVALID_VALUE) {
		write(g_pcm_fd, buff, size);
	}
}


/**
 * 停止纯音频记录
 */
VOID mfile_pcm_stop()
{
	INT32 fd = g_pcm_fd;

	g_pcm_fd = INVALID_VALUE;
	if (fd != INVALID_VALUE) {
		close(fd);
	}
}



/** 
 * 将数据库文件改为新的名称(以前与国科是REC_DB_INDEX_OLD)
 * @param  filename  新文件名
 */
VOID mfile_new_dbfile(LPCSTR filename)
{
	CHAR	old_file[120] = {'\0'};

	if (file_exist(filename))
		return;
	
	extract_file_path(filename, old_file);
	strcat(old_file, REC_DB_INDEX_OLD);
	if (file_exist(old_file)) {
		rename(old_file, filename);
		LOGW_NF("%s(), src:%s, dst:%s", __func__, old_file, filename);
	}
}

/** 
* 是否支持某种文件系统
* @param  fs_type  系统系统
* @return	成功返回1,失败返回0
*/
UINT8 mfile_is_support_fs(INT32 fs_type)
{
#if defined(NAS_ENABLE)
	FILE	*pfile; 
	CHAR	fs_name[32] = {'\0'};
	INT32 	total = 0, mmz = 0;

	// 判断内存大小,太小内存不适配nas
	get_mmz_memory(NULL, &mmz);
	if (mmz < 42)
		return FALSE;		

	switch (fs_type){
	BREAK_CASE(MFILE_FS_NFS,  strcpy(fs_name, VS_FS_NFS));
	BREAK_CASE(MFILE_FS_SMB3, strcpy(fs_name, VS_FS_SMB3));
	default: return FALSE;
	}
	
	pfile = fopen("/proc/filesystems", "r");
	if (NULL != pfile) {
		char	*line = NULL;
		size_t	nums = 0;	
		
		while (getline(&line, &nums, pfile) != -1){
			if (strstr(line+5, fs_name)) {
				free(line);
				fclose(pfile);
			
				return TRUE;
			}

			free(line);
			line = NULL;
			nums = 0;
		}	
		fclose(pfile);		
	}
#endif

	return FALSE;
}

/** 
 * 得到录像目录
 * @return  根据当前录像状态返回路径
 */
LPCSTR get_record_path()
{
#if defined(NAS_ENABLE)
	static LPCSTR s_rec_path[] = {SDCARD_MOUNT_PATH, NAS_MOUNT_PATH};
	
	if (!sdcard_exist() && mfile_nas_ready())
		return s_rec_path[1];

	return s_rec_path[0];	
#else	
	return SDCARD_MOUNT_PATH;
#endif	
}


/** 
 * nas载入配置
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_nas_reload()
{
	return FALSE;

#if 0 // defined(NAS_ENABLE)	

	if (settings_load_nas()) {
		
		g_nas_point.enable = mfile_is_support_fs(MFILE_FS_SMB3) && 0;	// g_pRunSets->nas.enable

		// 挂载
		do {			
			CHAR	mnt_opt[200] = {'\0'};
			LPSTR	p = mnt_opt;

			umount_fs(VS_FS_NFS, NAS_MOUNT_PATH);
			umount_fs(VS_FS_SMB3, NAS_MOUNT_PATH);
			if (!g_nas_point.enable){
				break;
			}

			for (INT32 i = 0; i < 20; i++) {
				if (net_if_ready(net_get_work_ifname(), NULL))
					break;
				
				LogI("wait net[%s] ready.", net_get_work_ifname());
				Sleep(1000);
			}
			
			if (stricmp(g_pRunSets->nas.protocol, VS_FS_NFS) EQU 0){
				if (!mfile_is_support_fs(MFILE_FS_NFS)){
					LogE("内核不支持nfs挂载");
					break;
				}
				
				s_strcpy(mnt_opt, "nolock,tcp,rsize=32768,wsize=32768"); 
			}
			else {
				if (!mfile_is_support_fs(MFILE_FS_SMB3)){
					LogE("内核不支持smb3挂载");
					break;
				}
				
				strcpy(g_pRunSets->nas.protocol, VS_FS_SMB3);
				if (strlen(g_pRunSets->nas.user))
					p += snprintf(p, sizeof(mnt_opt) - (p - mnt_opt), "username=%s,password=%s,", g_pRunSets->nas.user, g_pRunSets->nas.pass);
				p += snprintf(p, sizeof(mnt_opt) - (p - mnt_opt), "iocharset=%s,dir_mode=0777,file_mode=0666", "utf8");
			}
			
		    // 挂载 smb/nfs 文件系统
		    if (mount(g_pRunSets->nas.addr, NAS_MOUNT_PATH, g_pRunSets->nas.protocol, 0, mnt_opt) != 0) {
		        LogE("mount smb3/nfs errnor: %s", strerror(errno));
		        break;
		    }
			else {
				LogI("mount %s=>%s", g_pRunSets->nas.addr, NAS_MOUNT_PATH);
				g_nas_point.mount_ok = TRUE;
			}			
			
		} while (FALSE);

		if (!g_nas_point.enable || g_nas_point.mount_ok)
			return TRUE;		
		
		return FALSE;
	} 
	g_nas_point.status = FALSE;
	
#endif

	return FALSE;
}


/** 
 * nas是否就绪
 * @return	 成功返回TRUE; 失败返回FALSE
 */
UINT8 mfile_nas_ready()
{
#if defined(NAS_ENABLE)
	return g_nas_point.enable && g_nas_point.mount_ok;
#else 
	return FALSE;	
#endif	
}













