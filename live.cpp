#include "live.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <chrono>
#include <new>  // for std::bad_alloc

// 全局实时流管理器映射表
static std::map<INT32, LiveStreamManager*> g_liveStreamManagers;
static TRWLock g_managerLock;

// 注意：移除了全局数据包释放函数，因为智能指针会自动管理内存


// ========== StreamSenderThread 实现 ==========

StreamSenderThread::StreamSenderThread(INT32 channelId, LiveStreamManager* manager)
    : m_channelId(channelId),
      m_manager(manager),
      m_thread(0),
      m_shouldStop(false),
      m_state(THREAD_STATE_STOPPED),
      m_voChn(-1)
{}

StreamSenderThread::~StreamSenderThread()
{
    stop();
}

INT32 StreamSenderThread::start()
{
    // 优化：快速检查，减少锁竞争
    if (m_thread != 0 && m_state.load() == THREAD_STATE_RUNNING) {
        return OK;  // 已经运行，快速返回
    }

    ScopedLocker lock(m_configLock);

    // 双重检查
    if (m_thread != 0) {
        return OK;
    }

    if (m_voChn < 0) {
        LogE("VO通道未设置: chn=%d", m_channelId);
        return FAIL;
    }

    // 优化：预设状态，减少状态切换延迟
    m_shouldStop = false;
    m_state = THREAD_STATE_RUNNING;

    // 优化：使用默认线程属性，加快创建速度
    int ret = pthread_create(&m_thread, NULL, threadEntry, this);
    if (ret != 0) {
        LogE("创建发送线程失败: chn=%d, error=%d", m_channelId, ret);
        m_state = THREAD_STATE_STOPPED;
        return FAIL;
    }

    return OK;
}

INT32 StreamSenderThread::stop()
{
    {
        ScopedLocker lock(m_configLock);
        if (m_state == THREAD_STATE_STOPPED) {
            return OK;
        }

        m_shouldStop = true;
        m_state = THREAD_STATE_STOPPING;
    }

    // 唤醒可能在暂停等待中的线程
    {
        ScopedLocker pauseLock(m_pauseLock);
        m_pauseCondition.NotifyAll();
    }

    // 等待线程结束
    if (m_thread != 0) {
        pthread_join(m_thread, NULL);
        m_thread = 0;
    }

    m_state = THREAD_STATE_STOPPED;

    return OK;
}

void StreamSenderThread::requestStop()
{
    // 优化：快速停止请求，不等待线程结束，用于快速状态切换
    {
        ScopedLocker lock(m_configLock);
        if (m_state == THREAD_STATE_STOPPED) {
            return;
        }

        m_shouldStop = true;
        m_state = THREAD_STATE_STOPPING;
    }

    // 唤醒可能在等待中的线程，但不等待线程结束
    {
        ScopedLocker pauseLock(m_pauseLock);
        m_pauseCondition.NotifyAll();
    }

}

THREAD_STATE StreamSenderThread::getState() const
{
    return m_state.load();
}

bool StreamSenderThread::isRunning() const
{
    return m_state.load() == THREAD_STATE_RUNNING;
}

bool StreamSenderThread::isPaused() const
{
    return m_state.load() == THREAD_STATE_PAUSED;
}

void StreamSenderThread::setVoChannel(INT32 vo_chn)
{
    m_voChn = vo_chn;
}

INT32 StreamSenderThread::getVoChannel() const
{
    return m_voChn.load();
}



void* StreamSenderThread::threadEntry(void* arg)
{
    StreamSenderThread* thread = static_cast<StreamSenderThread*>(arg);
    if (thread) {
        thread->threadLoop();
    }
    return NULL;
}

void StreamSenderThread::threadLoop()
{
    while (!m_shouldStop.load()) {
        // 从队列取出数据包
        T_LIVE_STREAM_PACKET packet;
        INT32 ret = m_manager->popStreamData(packet);

        if (ret == OK) {
            // 发送数据包
            sendFrame(packet);
            // 智能指针会自动释放内存，无需手动释放
        } else {
            // 没有数据时，根据内部状态自动处理
            if (m_shouldStop.load()) {
                // 如果收到停止信号，排空缓冲区
                drainBufferOnStop();
                break;
            } else {
                // 没有数据，短暂休眠后继续检查（不改变线程状态）
                // 优化：使用更短的休眠时间，提高响应速度
                Sleep(LIVE_SENDER_THREAD_SLEEP_MS);
            }
        }
    }
}

INT32 StreamSenderThread::sendFrame(const T_LIVE_STREAM_PACKET& packet)
{
    if (!packet.data || packet.dataSize == 0) {
        LogE("数据包无效: chn=%d", m_channelId);
        return FAIL;
    }

    INT32 voChn = m_voChn.load();
    if (voChn < 0) {
        LogE("VO通道未设置: chn=%d", m_channelId);
        return FAIL;
    }

    // 调用海思解码接口
    INT32 ret = vs_hisi_vdec_send_frame(
        m_channelId,    // 使用通道ID作为解码通道
        voChn,          // VO通道号
        packet.data.get(),
        packet.dataSize,
        packet.timestamp,
        0,  // is_end
        1   // is_live
    );

    if (ret != 0) {
        LogE("发送帧到解码器失败: chn=%d, vo_chn=%d, ret=%d",
             m_channelId, voChn, ret);
        return FAIL;
    }

    return OK;
}

void StreamSenderThread::drainBufferOnStop()
{
    UINT64 startTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    UINT32 drainTimeout = LIVE_BUFFER_DRAIN_TIMEOUT_MS;

    while (true) {
        // 检查超时
        UINT64 currentTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        if (currentTime - startTime > drainTimeout) {
            break;
        }

        // 尝试获取数据包
        T_LIVE_STREAM_PACKET packet;
        INT32 ret = m_manager->popStreamData(packet);

        if (ret == OK) {
            // 发送数据包
            sendFrame(packet);
            // 智能指针会自动释放内存
        } else {
            // 队列为空，排空完成
            break;
        }
    }
}

bool StreamSenderThread::waitForData(UINT32 timeoutMs)
{
    // 简单的休眠等待
    Sleep(timeoutMs);
    return true;
}

void StreamSenderThread::handleAutoPause()
{
    // 已移除自动暂停机制，此方法保留为空实现
    // 线程现在通过简单的数据轮询来工作，不进行复杂的状态切换
}

void StreamSenderThread::resumeFromPause()
{
    // 简化实现：由于移除了自动暂停机制，此方法主要用于从管理器层面的显式恢复
    THREAD_STATE currentState = m_state.load();

    if (currentState == THREAD_STATE_PAUSED) {
        m_state = THREAD_STATE_RUNNING;

        // 唤醒可能在等待的线程
        {
            ScopedLocker pauseLock(m_pauseLock);
            m_pauseCondition.NotifyOne();
        }

    }
    // 其他状态不需要详细日志，避免日志噪音
}

// ========== LiveChannelStream 实现 ==========

LiveChannelStream::LiveChannelStream(INT32 channelId)
    : m_channelId(channelId),
      m_state(LIVE_STREAM_STOPPED),
      m_running(false),
      m_maxQueueSize(LIVE_DEFAULT_QUEUE_SIZE),
      m_bufferSize(LIVE_DEFAULT_BUFFER_SIZE),
      m_sequence(0),
      m_senderThread(nullptr)
{}

LiveChannelStream::~LiveChannelStream()
{
    cleanup();
}

INT32 LiveChannelStream::initialize()
{
    ScopedWriteLocker lock(m_rwLock);

    if (m_running) {
        LogW("LiveChannelStream 已初始化: chn=%d", m_channelId);
        return OK;
    }

    // 清空队列 - 智能指针会自动释放内存
    {
        ScopedLocker queueLock(m_queueLock);
        while (!m_dataQueue.empty()) {
            m_dataQueue.pop();
        }
    }

    // 重置序列号
    m_sequence = 0;

    m_running = true;
    m_state = LIVE_STREAM_RUNNING;

    return OK;
}

void LiveChannelStream::cleanup()
{
    ScopedWriteLocker lock(m_rwLock);

    // 清空队列并通知等待的线程 - 智能指针会自动释放内存
    {
        ScopedLocker queueLock(m_queueLock);
        while (!m_dataQueue.empty()) {
            m_dataQueue.pop();
        }

        // 通知所有等待的线程
        m_dataAvailable.NotifyAll();
    }

    m_running = false;
    m_state = LIVE_STREAM_STOPPED;
}

INT32 LiveChannelStream::pushStreamData(FRAMEINFO_t* hdr, void* data, uint32_t size)
{
    if (!hdr || !data || size == 0) {
        LogE("推送数据参数错误: chn=%d", m_channelId);
        return FAIL;
    }

    ScopedWriteLocker lock(m_rwLock);

    if (!m_running) {
        LogE("流未运行，无法推送数据: chn=%d", m_channelId);
        return FAIL;
    }

    bool wasEmpty = false;
    {
        ScopedLocker queueLock(m_queueLock);

        wasEmpty = m_dataQueue.empty();

        // 检查队列是否已满
        if (isQueueFull()) {
            // 丢弃最旧的数据包
            dropOldestPacket();
        }

        // 创建新数据包
        T_LIVE_STREAM_PACKET packet;
        INT32 ret = createPacket(packet, hdr, data, size);
        if (ret != OK) {
            LogE("创建数据包失败: chn=%d", m_channelId);
            return FAIL;
        }

        // 添加到队列
        m_dataQueue.push(std::move(packet));
    }

    // 如果队列之前为空，通知等待的线程
    if (wasEmpty) {
        notifyDataAvailable();
        // 移除频繁的线程状态切换，让线程自然处理数据可用性
    }

    return OK;
}

INT32 LiveChannelStream::popStreamData(T_LIVE_STREAM_PACKET& packet)
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_running) {
        return FAIL; // 未运行
    }

    ScopedLocker queueLock(m_queueLock);

    if (m_dataQueue.empty()) {
        return FAIL; // 无数据可取
    }

    // 取出数据包
    packet = std::move(m_dataQueue.front());
    m_dataQueue.pop();

    return OK;
}

INT32 LiveChannelStream::popStreamDataBlocking(T_LIVE_STREAM_PACKET& packet, UINT32 timeoutMs)
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_running) {
        return FAIL; // 未运行
    }

    ScopedLocker queueLock(m_queueLock);

    // 等待数据可用或超时
    while (m_dataQueue.empty() && m_running) {
        if (!m_dataAvailable.TimeWait(&m_queueLock, timeoutMs)) {
            // 超时
            return FAIL;
        }
    }

    if (!m_running || m_dataQueue.empty()) {
        return FAIL;
    }

    // 取出数据包
    packet = std::move(m_dataQueue.front());
    m_dataQueue.pop();

    return OK;
}

LIVE_STREAM_STATE LiveChannelStream::getState() const
{
    return m_state.load();
}

UINT32 LiveChannelStream::getQueueSize()
{
    ScopedLocker queueLock(m_queueLock);
    return m_dataQueue.size();
}

bool LiveChannelStream::isEmpty()
{
    ScopedLocker queueLock(m_queueLock);
    return m_dataQueue.empty();
}

bool LiveChannelStream::isFull() 
{
    ScopedLocker queueLock(m_queueLock);
    return isQueueFull();
}

void LiveChannelStream::setMaxQueueSize(UINT32 maxSize)
{
    ScopedWriteLocker lock(m_rwLock);
    m_maxQueueSize = maxSize;
}

void LiveChannelStream::setBufferSize(UINT32 bufferSize)
{
    ScopedWriteLocker lock(m_rwLock);
    m_bufferSize = QF_MIN(bufferSize, LIVE_MAX_BUFFER_SIZE);
}

void LiveChannelStream::notifyDataAvailable()
{
    ScopedLocker queueLock(m_queueLock);
    m_dataAvailable.NotifyAll();
}

void LiveChannelStream::setSenderThread(StreamSenderThread* senderThread)
{
    m_senderThread = senderThread;
}

void LiveChannelStream::enterSleepState()
{
    // 优化：使用原子操作，减少锁开销
    LIVE_STREAM_STATE oldState = m_state.exchange(LIVE_STREAM_SLEEPING);
    (void)oldState; // 避免未使用变量警告
}

void LiveChannelStream::exitSleepState()
{
    // 优化：使用原子比较交换，确保只有在休眠状态时才切换
    LIVE_STREAM_STATE expected = LIVE_STREAM_SLEEPING;
    if (m_state.compare_exchange_strong(expected, LIVE_STREAM_RUNNING)) {
        ; //LogI("流快速退出休眠状态: chn=%d", m_channelId);
    }
}

bool LiveChannelStream::isSleeping() const
{
    return m_state.load() == LIVE_STREAM_SLEEPING;
}

LIVE_STREAM_STATE LiveChannelStream::getStateFast() const
{
    // 优化：无锁快速状态获取，用于性能敏感的场景
    return m_state.load();
}

void LiveChannelStream::notifyThreadResume()
{
    // 简化：移除频繁的线程恢复调用
    // 线程会自然地通过数据可用性来处理状态，无需频繁干预
    // 只保留数据可用的通知，不进行状态切换
}

INT32 LiveChannelStream::createPacket(T_LIVE_STREAM_PACKET& packet, FRAMEINFO_t* hdr, void* data, uint32_t size)
{
    // 安全检查：防止过大的内存分配
    if (size == 0 || !data) {
        return FAIL;
    }

    try {
        // 使用智能指针分配数据内存
        packet.data = std::make_unique<CHAR[]>(size);

        // 复制数据
        memcpy(packet.data.get(), data, size);

        // 填充包信息
        packet.dataSize = size;
        packet.timestamp = hdr->utctime;
        packet.frameType = hdr->flags;
        packet.keyFrame = (hdr->flags == IPC_FRAME_FLAG_IFRAME) ? 1 : 0;
        packet.sequence = ++m_sequence;

        return OK;

    } catch (const std::bad_alloc& e) {
        LogE("内存分配异常: chn=%d, size=%u, error=%s", m_channelId, size, e.what());
        return FAIL;
    }
}

// 移除了 freePacket 方法，因为智能指针会自动管理内存

bool LiveChannelStream::isQueueFull() const
{
    return m_dataQueue.size() >= m_maxQueueSize;
}

void LiveChannelStream::dropOldestPacket()
{
    if (!m_dataQueue.empty()) {
        m_dataQueue.pop(); // 智能指针会自动释放内存
    }
}

// ========== LiveStreamManager 实现 ==========

LiveStreamManager::LiveStreamManager(INT32 channelId)
    : m_channelId(channelId),
      m_voChn(-1),
      m_initialized(false),
      m_stream(nullptr),
      m_senderThread(nullptr)
{
    ;
}

LiveStreamManager::~LiveStreamManager()
{
    cleanup();
    ;
}

INT32 LiveStreamManager::initialize()
{
    ScopedWriteLocker lock(m_rwLock);

    if (m_initialized) {
        LogW("LiveStreamManager 已初始化: chn=%d", m_channelId);
        return OK;
    }

    m_initialized = true;

    return OK;
}

void LiveStreamManager::cleanup()
{
    ScopedWriteLocker lock(m_rwLock);

    if (!m_initialized) {
        return;
    }

    // 停止发送线程
    if (m_senderThread) {
        m_senderThread->stop();
		
		DeleteAndNull(m_senderThread);
    }

    // 停止流
    if (m_stream) {
        m_stream->cleanup();

		DeleteAndNull(m_stream );
    }

    m_initialized = false;
}

INT32 LiveStreamManager::startStream()
{
    ScopedWriteLocker lock(m_rwLock);

    if (!m_initialized) {
        LogE("LiveStreamManager 未初始化: chn=%d", m_channelId);
        return FAIL;
    }

    if (!m_stream) {
        m_stream = new LiveChannelStream(m_channelId);
        if (!m_stream) {
            LogE("创建流失败: chn=%d", m_channelId);
            return FAIL;
        }
    }

    INT32 ret = m_stream->initialize();
    if (ret != OK) {
        LogE("启动流失败: chn=%d", m_channelId);
        return ret;
    }

    // LogI("实时流已启动: chn=%d", m_channelId);
	
    return OK;
}

INT32 LiveStreamManager::stopStream()
{
    ScopedWriteLocker lock(m_rwLock);

    if (m_stream) {
        m_stream->cleanup();
    }

    LogI("停止流成功: chn=%d", m_channelId);
	
    return OK;
}

INT32 LiveStreamManager::pushStreamData(FRAMEINFO_t* hdr, void* data, uint32_t size)
{
    if (!hdr || !data || size == 0) {
        LogE("推送数据参数错误: chn=%d", m_channelId);
        return FAIL;
    }

    // 优化：使用无锁快速状态检查，最大化性能
    bool needWakeup = false;

    {
        ScopedReadLocker lock(m_rwLock);

        if (!m_initialized || !m_stream) {
            LogE("LiveStreamManager 未初始化或流不存在: chn=%d", m_channelId);
            return FAIL;
        }

        // 使用快速状态检查，避免额外的锁开销
        needWakeup = (m_stream->getStateFast() == LIVE_STREAM_SLEEPING);
    }

    // 优化：只有在休眠状态时才需要唤醒，并且异步处理
    if (needWakeup) {
        INT32 ret = wakeupStream();
        if (ret != OK) {
            LogE("快速唤醒流失败: chn=%d", m_channelId);
            return FAIL;
        }
    }

    // 优化：减少重复的状态检查
    ScopedReadLocker lock(m_rwLock);

    if (!m_stream) {
        LogE("流对象不存在: chn=%d", m_channelId);
        return FAIL;
    }

    return m_stream->pushStreamData(hdr, data, size);
}

INT32 LiveStreamManager::popStreamData(T_LIVE_STREAM_PACKET& packet)
{

    ScopedReadLocker lock(m_rwLock);

    if (!m_initialized || !m_stream) {
        LogE("LiveStreamManager 未初始化或流不存在: chn=%d", m_channelId);
        return FAIL;
    }

    return m_stream->popStreamData(packet);
}

THREAD_STATE LiveStreamManager::getSenderThreadState() const
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_senderThread) {
        return THREAD_STATE_STOPPED;
    }

    return m_senderThread->getState();
}



LIVE_STREAM_STATE LiveStreamManager::getStreamState() const
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_stream) {
        return LIVE_STREAM_STOPPED;
    }

    return m_stream->getState();
}

UINT32 LiveStreamManager::getStreamQueueSize() const
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_stream) {
        return 0;
    }

    return m_stream->getQueueSize();
}

INT32 LiveStreamManager::sleepStream()
{
    // 优化：使用更细粒度的锁，减少锁持有时间
    {
        ScopedReadLocker lock(m_rwLock);
        if (!m_stream) {
            LogE("流不存在，无法进入休眠: chn=%d", m_channelId);
            return FAIL;
        }
    }

    // 优化：先设置流状态，再处理线程，减少总体切换时间
    {
        ScopedWriteLocker lock(m_rwLock);
        if (m_stream) {
            m_stream->enterSleepState();
        }
    }

    // 优化：异步停止线程，不等待线程完全结束
    if (m_senderThread) {
        // 设置停止标志，但不等待线程结束
        m_senderThread->requestStop();
    }

    // LogI("实时流已休眠: chn=%d", m_channelId);
	
    return OK;
}

INT32 LiveStreamManager::wakeupStream()
{
    // 优化：快速状态检查，减少锁持有时间
    bool needWakeup = false;
    bool needRestartThread = false;
    THREAD_STATE threadState = THREAD_STATE_STOPPED;

    {
        ScopedReadLocker lock(m_rwLock);

        if (!m_stream) {
            LogE("流不存在，无法唤醒: chn=%d", m_channelId);
            return FAIL;
        }

        // 快速检查是否需要唤醒
        needWakeup = m_stream->isSleeping();
        if (needWakeup && m_senderThread && m_voChn >= 0) {
            threadState = m_senderThread->getState();
            needRestartThread = (threadState == THREAD_STATE_STOPPED || threadState == THREAD_STATE_STOPPING);
        }
    }

    if (!needWakeup) {
        return OK;  // 无需唤醒，直接返回
    }

    // 优化：先启动线程，再更新流状态，并行处理
    if (needRestartThread) {
        ScopedReadLocker lock(m_rwLock);
        if (m_senderThread && m_voChn >= 0) {
            INT32 ret = m_senderThread->start();
            if (ret != OK) {
                LogE("唤醒时重启发送线程失败: chn=%d", m_channelId);
                return FAIL;
            }
        }
    }

    // 优化：最后更新流状态
    {
        ScopedWriteLocker lock(m_rwLock);
        if (m_stream && m_stream->isSleeping()) {
            m_stream->exitSleepState();
        }
    }

    // LogI("实时流已唤醒: chn=%d", m_channelId);
	
    return OK;
}

bool LiveStreamManager::isStreamSleeping() const
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_stream) {
        return false;
    }

    return m_stream->isSleeping();
}



INT32 LiveStreamManager::setStreamConfig(UINT32 maxQueueSize, UINT32 bufferSize)
{
    ScopedReadLocker lock(m_rwLock);

    if (!m_stream) {
        LogE("流不存在: chn=%d", m_channelId);
        return FAIL;
    }

    m_stream->setMaxQueueSize(maxQueueSize);
    m_stream->setBufferSize(bufferSize);

    LogI("设置流配置成功: chn=%d, maxQueue=%d, bufferSize=%d",
         m_channelId, maxQueueSize, bufferSize);
    return OK;
}

// === 自动流发送功能实现 ===

INT32 LiveStreamManager::enableAutoSend(INT32 vo_chn)
{
    ScopedWriteLocker lock(m_rwLock);

    if (!m_initialized) {
        LogE("LiveStreamManager 未初始化: chn=%d", m_channelId);
        return FAIL;
    }

    // 如果线程已存在，先停止
    if (m_senderThread) {
        m_senderThread->stop();

		DeleteAndNull(m_senderThread);
    }

    // 创建新的发送线程
    m_senderThread = new StreamSenderThread(m_channelId, this);
    if (!m_senderThread) {
        LogE("创建发送线程失败: chn=%d", m_channelId);
        return FAIL;
    }

    // 设置VO通道
    m_senderThread->setVoChannel(vo_chn);
    m_voChn = vo_chn;

    // 设置流和线程之间的关联
    if (m_stream) {
        m_stream->setSenderThread(m_senderThread);
    }

    // 启动线程
    INT32 ret = m_senderThread->start();
    if (ret != OK) {
        LogE("启动发送线程失败: chn=%d", m_channelId);
		DeleteAndNull(m_senderThread);
        return FAIL;
    }

    return OK;
}

INT32 LiveStreamManager::disableAutoSend()
{
    ScopedWriteLocker lock(m_rwLock);

    if (m_senderThread) {
        m_senderThread->stop();
		DeleteAndNull(m_senderThread);
    }

    return OK;
}

// 移除了isAutoSendEnabled方法，简化接口

INT32 LiveStreamManager::switchVoChannel(INT32 vo_chn)
{
    ScopedWriteLocker lock(m_rwLock);

    if (!m_senderThread) {
        LogE("发送线程不存在: chn=%d", m_channelId);
        return FAIL;
    }

    m_senderThread->setVoChannel(vo_chn);
    m_voChn = vo_chn;

    LogI("切换VO通道成功: chn=%d, vo_chn=%d", m_channelId, vo_chn);
    return OK;
}

INT32 LiveStreamManager::getVoChannel() const
{
    ScopedReadLocker lock(m_rwLock);
    return m_voChn;
}

// 移除了统计信息和线程状态查询方法，简化接口

bool LiveStreamManager::isValidChannelId(INT32 channelId) const
{
    return channelId >= 0 && channelId < LIVE_MAX_CHANNEL_NUM;
}



// ========== 全局接口函数实现 ==========

static LiveStreamManager* getLiveStreamManager(INT32 channelId)
{
    ScopedReadLocker lock(g_managerLock);

    auto it = g_liveStreamManagers.find(channelId);
    return (it != g_liveStreamManagers.end()) ? it->second : nullptr;
}

static LiveStreamManager* createLiveStreamManager(INT32 channelId)
{
    ScopedWriteLocker lock(g_managerLock);

    // 检查是否已存在
    auto it = g_liveStreamManagers.find(channelId);
    if (it != g_liveStreamManagers.end()) {
        return it->second;
    }

    // 创建新的管理器
    LiveStreamManager* manager = new LiveStreamManager(channelId);
    if (!manager) {
        LogE("创建LiveStreamManager失败: chn=%d", channelId);
        return nullptr;
    }

    if (manager->initialize() != OK) {
        LogE("初始化LiveStreamManager失败: chn=%d", channelId);
		DeleteAndNull(manager);
	
        return nullptr;
    }

    g_liveStreamManagers[channelId] = manager;

    return manager;
}

static void destroyLiveStreamManager(INT32 channelId)
{
    ScopedWriteLocker lock(g_managerLock);

    auto it = g_liveStreamManagers.find(channelId);
    if (it != g_liveStreamManagers.end()) {
        // 检查是否处于休眠状态，如果是则不删除
        if (it->second && it->second->isStreamSleeping()) {
            // LogI("通道处于休眠状态，跳过销毁: chn=%d", channelId);
            return;
        }

        it->second->cleanup();
        DeleteAndNull(it->second);
        g_liveStreamManagers.erase(it);
    }
}

INT32 live_stream_init(INT32 channelId)
{
    if (channelId < 0 || channelId >= LIVE_MAX_CHANNEL_NUM) {
        LogE("无效的通道ID: chn=%d", channelId);
        return FAIL;
    }

    LiveStreamManager* manager = createLiveStreamManager(channelId);
    if (!manager) {
        LogE("初始化实时流管理器失败: chn=%d", channelId);
        return FAIL;
    }
	
    return OK;
}

VOID live_stream_cleanup(INT32 channelId)
{
    if (channelId < 0 || channelId >= LIVE_MAX_CHANNEL_NUM) {
        LogE("无效的通道ID: chn=%d", channelId);
        return;
    }

    destroyLiveStreamManager(channelId);
	
}

INT32 live_stream_start(INT32 channelId)
{
    // 首先检查通道是否已经存在
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (manager) {
        // 通道已存在，检查是否处于休眠状态
        if (manager->isStreamSleeping()) {
            return manager->wakeupStream();
        } 
		else {
            // 通道已存在且未休眠，直接启动
            return manager->startStream();
        }
    }
	else {
		LogE("无实时流播放对象!!");
		return FAIL;
	}

	return OK;
}

INT32 live_stream_stop(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    // 使用休眠模式而不是完全停止
    return manager->sleepStream();
}

INT32 live_stream_push_data(INT32 channelId, FRAMEINFO_t* hdr, LPVOID data, UINT32 size)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    return manager->pushStreamData(hdr, data, size);
}

INT32 live_stream_pop_data(INT32 channelId, T_LIVE_STREAM_PACKET& packet)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    return manager->popStreamData(packet);
}

// ========== 自动流发送全局接口实现 ==========

INT32 live_stream_enable_auto_send(INT32 channelId, INT32 vo_chn)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    return manager->enableAutoSend(vo_chn);
}

INT32 live_stream_disable_auto_send(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    return manager->disableAutoSend();
}

INT32 live_stream_switch_vo_channel(INT32 channelId, INT32 vo_chn)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    return manager->switchVoChannel(vo_chn);
}

INT32 live_stream_get_vo_channel(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return -1;
    }

    return manager->getVoChannel();
}

// ========== 线程状态查询接口实现 ==========

THREAD_STATE live_stream_get_sender_thread_state(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return THREAD_STATE_STOPPED;
    }

    return manager->getSenderThreadState();
}



// ========== 状态查询接口实现 ==========

LIVE_STREAM_STATE live_stream_get_state(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return LIVE_STREAM_STOPPED;
    }

    return manager->getStreamState();
}

UINT32 live_stream_get_queue_size(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return 0;
    }

    return manager->getStreamQueueSize();
}

// ========== 流休眠管理接口实现 ==========

INT32 live_stream_force_stop(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        LogE("获取LiveStreamManager失败: chn=%d", channelId);
        return FAIL;
    }

    // 强制停止：先停止流，然后完全清理资源
    INT32 ret = manager->stopStream();
    if (ret == OK) {
        // 强制销毁管理器
        ScopedWriteLocker lock(g_managerLock);
        auto it = g_liveStreamManagers.find(channelId);
        if (it != g_liveStreamManagers.end()) {
            it->second->cleanup();
            DeleteAndNull(it->second);
            g_liveStreamManagers.erase(it);
            LogI("强制停止并清理LiveStreamManager成功: chn=%d", channelId);
        }
    }

    return ret;
}

bool live_stream_is_sleeping(INT32 channelId)
{
    LiveStreamManager* manager = getLiveStreamManager(channelId);
    if (!manager) {
        return false;
    }

    return manager->isStreamSleeping();
}


