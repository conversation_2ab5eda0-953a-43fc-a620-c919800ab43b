/*******************************************************************************
*
* 功能：  原子操作函数
*
* 说明：
*		
*
* 作者：  徐辉  
* 
*******************************************************************************/

#ifndef ATOMIC_HPP
#define ATOMIC_HPP

//#include <atomic.h>

#ifndef atomic_add
	#define atomic_add(ptr,val) __sync_add_and_fetch((ptr),(val))   
#endif

#ifndef atomic_sub
	#define atomic_sub(ptr,val) __sync_sub_and_fetch((ptr),(val))  
#endif	

#define atomic_get(ptr) 	__sync_fetch_and_add((ptr),(0)) 

// 设置值为newval，并返回之前的值，与windows上的InterlockedExchange一样
#define atomic_set(ptr, newval) 	__sync_lock_test_and_set((ptr), newval)  

// 比较*ptr与oldval的值，如果两者相等，则将newval更新到*ptr并返回true
#define atomic_compare_and_set(ptr, oldval, newval) 	__sync_bool_compare_and_swap((ptr), oldval, newval)  

// 比较是否=x
#define atomic_compare(ptr, val)	atomic_compare_and_set(ptr, val, *(ptr))

#define atomic_lock(ptr) 	while (atomic_set(ptr, 1)) Sleep(0)
#define atomic_trylock(ptr) (atomic_set(ptr, 1) EQU 0 ? 1 : 0)
#define atomic_unlock(ptr) 	atomic_set(ptr, 0)

//#define atomic_lock(ptr) 	while (!atomic_trylock(ptr)) Sleep(0)
//#define atomic_trylock(ptr) (*(ptr) == 0 && atomic_compare_and_set(ptr, 0, 1))
//#define atomic_unlock(ptr) 	atomic_set(ptr, 0)

//#define atomic_lock(ptr) 	while (!atomic_trylock(ptr)) Sleep(0)
//#define atomic_trylock(ptr) (*(ptr) == 0 && (*(ptr) = 1))
//#define atomic_unlock(ptr) 	(*ptr = 0)

#ifdef __cplusplus

template<typename _T>
class ScopedAtomic
{
public:
	explicit ScopedAtomic(_T *tALock, UINT8 try_lock): m_tLocker(tALock)
	{
		if (try_lock) {
			m_locked = atomic_set(m_tLocker, 1) EQU 0 ? 1 : 0;
		}
		else {
			while (atomic_set(m_tLocker, 1)){				
				Sleep(0);
			}
			
			m_locked = 1;
		}
	}	

	~ScopedAtomic()
	{
		if (m_locked) {
			m_locked = 0;
			atomic_set(m_tLocker,  0);
		}
	}

	UINT8 IsLock() {
		return m_locked;
	}

private:

	_T		*m_tLocker;
	UINT8	m_locked;
};
#endif


#endif
