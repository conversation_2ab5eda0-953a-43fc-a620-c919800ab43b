#!/bin/bash

# 磁盘诊断脚本
# 用于分析磁盘使用情况和清盘功能状态

echo "=== 磁盘诊断报告 ==="
echo "诊断时间: $(date)"
echo ""

# 磁盘路径
DISK1="/mnt/custom/disk/disk1"
DISK2="/mnt/custom/disk/disk2"

# 函数：检查磁盘基本信息
check_disk_basic() {
    echo "=== 磁盘基本信息 ==="
    echo "磁盘挂载情况:"
    mount | grep -E "(disk1|disk2)"
    echo ""
    
    echo "磁盘使用情况:"
    df -h $DISK1 $DISK2 2>/dev/null
    echo ""
    
    echo "磁盘inode使用情况:"
    df -i $DISK1 $DISK2 2>/dev/null
    echo ""
}

# 函数：检查磁盘读写性能
check_disk_performance() {
    echo "=== 磁盘性能检查 ==="
    
    for disk in $DISK1 $DISK2; do
        if [ -d "$disk" ]; then
            echo "检查磁盘: $disk"
            
            # 检查磁盘是否可写
            test_file="$disk/.write_test_$$"
            if echo "test" > "$test_file" 2>/dev/null; then
                echo "  ✅ 磁盘可写"
                rm -f "$test_file"
            else
                echo "  ❌ 磁盘不可写 (错误: $?)"
            fi
            
            # 检查磁盘读取速度（简单测试）
            if [ -f "$disk"/*.h264 ]; then
                sample_file=$(find "$disk" -name "*.h264" | head -1)
                if [ -n "$sample_file" ]; then
                    read_time=$(time (dd if="$sample_file" of=/dev/null bs=1M count=10 2>/dev/null) 2>&1 | grep real | awk '{print $2}')
                    echo "  读取速度测试: $read_time"
                fi
            fi
            echo ""
        else
            echo "磁盘路径不存在: $disk"
        fi
    done
}

# 函数：分析文件分布
analyze_file_distribution() {
    echo "=== 文件分布分析 ==="
    
    for disk in $DISK1 $DISK2; do
        if [ -d "$disk" ]; then
            echo "磁盘: $disk"
            
            # 统计文件数量
            total_files=$(find "$disk" -type f 2>/dev/null | wc -l)
            h264_files=$(find "$disk" -name "*.h264" 2>/dev/null | wc -l)
            idx_files=$(find "$disk" -name "*.idx" 2>/dev/null | wc -l)
            
            echo "  总文件数: $total_files"
            echo "  录像文件(.h264): $h264_files"
            echo "  索引文件(.idx): $idx_files"
            
            # 按日期统计文件
            echo "  最近7天的文件分布:"
            for i in {0..6}; do
                date_str=$(date -d "$i days ago" +%Y%m%d)
                count=$(find "$disk" -name "*$date_str*" 2>/dev/null | wc -l)
                echo "    $(date -d "$i days ago" +%Y-%m-%d): $count 个文件"
            done
            
            # 统计文件大小分布
            echo "  文件大小分布:"
            echo "    总大小: $(du -sh "$disk" 2>/dev/null | cut -f1)"
            echo "    平均文件大小: $(find "$disk" -type f -exec du -b {} + 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f MB\n", sum/count/1024/1024; else print "0 MB"}')"
            
            echo ""
        fi
    done
}

# 函数：检查清盘配置
check_cleanup_config() {
    echo "=== 清盘配置检查 ==="
    
    # 检查配置文件（根据实际路径调整）
    config_files=(
        "/etc/disk_manager.conf"
        "/usr/local/etc/storage.conf"
        "disk_manager.h"
    )
    
    for config in "${config_files[@]}"; do
        if [ -f "$config" ]; then
            echo "配置文件: $config"
            grep -E "(THRESHOLD|CLEANUP|CRITICAL)" "$config" 2>/dev/null | head -10
            echo ""
        fi
    done
    
    # 从头文件中提取配置
    if [ -f "disk_manager.h" ]; then
        echo "从 disk_manager.h 提取的配置:"
        grep -E "#define.*DISK.*THRESHOLD" disk_manager.h 2>/dev/null
        grep -E "#define.*CLEANUP" disk_manager.h 2>/dev/null
        echo ""
    fi
}

# 函数：检查进程状态
check_process_status() {
    echo "=== 进程状态检查 ==="
    
    # 检查可能相关的进程
    echo "相关进程:"
    ps aux | grep -E "(disk|storage|record|cleanup)" | grep -v grep
    echo ""
    
    # 检查系统负载
    echo "系统负载:"
    uptime
    echo ""
    
    # 检查内存使用
    echo "内存使用:"
    free -h
    echo ""
}

# 函数：检查日志中的错误
check_logs() {
    echo "=== 日志错误检查 ==="
    
    # 检查系统日志中的磁盘相关错误
    echo "系统日志中的磁盘错误 (最近100行):"
    dmesg | grep -i -E "(error|fail|disk|storage)" | tail -20
    echo ""
    
    # 检查应用日志（根据实际路径调整）
    log_files=(
        "/var/log/storage.log"
        "/var/log/disk_manager.log"
        "/tmp/app.log"
    )
    
    for log in "${log_files[@]}"; do
        if [ -f "$log" ]; then
            echo "应用日志: $log (最近的错误):"
            tail -100 "$log" | grep -i -E "(error|fail|异常|错误)" | tail -10
            echo ""
        fi
    done
}

# 函数：生成建议
generate_recommendations() {
    echo "=== 诊断建议 ==="
    
    # 检查磁盘使用率
    disk1_usage=$(df "$DISK1" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//')
    disk2_usage=$(df "$DISK2" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$disk1_usage" -gt 95 ] || [ "$disk2_usage" -gt 95 ]; then
        echo "🚨 紧急建议:"
        echo "  1. 立即运行 emergency_cleanup.sh 脚本清理磁盘"
        echo "  2. 检查是否有大文件异常占用空间"
        echo "  3. 考虑临时停止录像功能"
    elif [ "$disk1_usage" -gt 90 ] || [ "$disk2_usage" -gt 90 ]; then
        echo "⚠️  警告建议:"
        echo "  1. 运行清理脚本释放空间"
        echo "  2. 检查清盘配置是否正确"
        echo "  3. 考虑调整录像保存策略"
    else
        echo "✅ 磁盘使用率正常"
    fi
    
    echo ""
    echo "常规建议:"
    echo "  1. 定期检查磁盘使用情况"
    echo "  2. 确保清盘功能配置正确"
    echo "  3. 监控应用日志中的错误信息"
    echo "  4. 考虑设置自动清理任务"
    
    echo ""
    echo "如果清盘功能仍然不工作，请检查:"
    echo "  1. 清盘线程是否正常运行"
    echo "  2. 清盘阈值配置是否合理"
    echo "  3. 磁盘权限是否正确"
    echo "  4. 是否有文件被进程占用无法删除"
}

# 主函数
main() {
    check_disk_basic
    check_disk_performance
    analyze_file_distribution
    check_cleanup_config
    check_process_status
    check_logs
    generate_recommendations
    
    echo ""
    echo "=== 诊断完成 ==="
    echo "如需立即清理磁盘，请运行: bash emergency_cleanup.sh"
}

# 执行主函数
main
