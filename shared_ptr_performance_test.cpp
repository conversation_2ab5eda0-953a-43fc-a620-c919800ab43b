#include <iostream>
#include <memory>
#include <chrono>
#include <vector>
#include <queue>
#include <thread>
#include <atomic>
#include <cstring>

// 模拟必要的类型定义
typedef char CHAR;
typedef unsigned int UINT32;
typedef unsigned long long UINT64;
typedef unsigned char UINT8;

// 模拟 FRAMEINFO_t 结构
typedef struct {
    UINT64 utctime;
    UINT8 flags;
} FRAMEINFO_t;

// shared_ptr 版本的数据包结构
typedef struct _T_LIVE_STREAM_PACKET_SHARED {
    UINT32 dataSize;
    UINT64 timestamp;
    UINT8  frameType;
    UINT8  keyFrame;
    UINT32 sequence;
    std::shared_ptr<CHAR[]> data;

    _T_LIVE_STREAM_PACKET_SHARED() 
        : dataSize(0), timestamp(0), frameType(0), keyFrame(0), sequence(0), data(nullptr) {}
} T_LIVE_STREAM_PACKET_SHARED;

// unique_ptr 版本的数据包结构（用于对比）
typedef struct _T_LIVE_STREAM_PACKET_UNIQUE {
    UINT32 dataSize;
    UINT64 timestamp;
    UINT8  frameType;
    UINT8  keyFrame;
    UINT32 sequence;
    std::unique_ptr<CHAR[]> data;

    _T_LIVE_STREAM_PACKET_UNIQUE() 
        : dataSize(0), timestamp(0), frameType(0), keyFrame(0), sequence(0), data(nullptr) {}
} T_LIVE_STREAM_PACKET_UNIQUE;

// 性能测试类
class PerformanceTest {
public:
    static const int TEST_ITERATIONS = 10000;
    static const int PACKET_SIZE = 1024;
    static const int THREAD_COUNT = 4;

    // 测试 shared_ptr 内存分配性能
    static void testSharedPtrAllocation() {
        std::cout << "=== shared_ptr 内存分配性能测试 ===" << std::endl;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::shared_ptr<CHAR[]>> ptrs;
        ptrs.reserve(TEST_ITERATIONS);
        
        for (int i = 0; i < TEST_ITERATIONS; ++i) {
            auto ptr = std::shared_ptr<CHAR[]>(new CHAR[PACKET_SIZE]);
            // 模拟数据写入
            memset(ptr.get(), i % 256, PACKET_SIZE);
            ptrs.push_back(ptr);
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "shared_ptr 分配 " << TEST_ITERATIONS << " 个包耗时: " 
                  << duration.count() << " 微秒" << std::endl;
        std::cout << "平均每个包: " << (double)duration.count() / TEST_ITERATIONS 
                  << " 微秒" << std::endl;
        
        // 清理
        ptrs.clear();
    }

    // 测试 unique_ptr 内存分配性能（对比）
    static void testUniquePtrAllocation() {
        std::cout << "=== unique_ptr 内存分配性能测试 ===" << std::endl;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::unique_ptr<CHAR[]>> ptrs;
        ptrs.reserve(TEST_ITERATIONS);
        
        for (int i = 0; i < TEST_ITERATIONS; ++i) {
            auto ptr = std::make_unique<CHAR[]>(PACKET_SIZE);
            // 模拟数据写入
            memset(ptr.get(), i % 256, PACKET_SIZE);
            ptrs.push_back(std::move(ptr));
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "unique_ptr 分配 " << TEST_ITERATIONS << " 个包耗时: " 
                  << duration.count() << " 微秒" << std::endl;
        std::cout << "平均每个包: " << (double)duration.count() / TEST_ITERATIONS 
                  << " 微秒" << std::endl;
        
        // 清理
        ptrs.clear();
    }

    // 测试 shared_ptr 拷贝性能
    static void testSharedPtrCopy() {
        std::cout << "=== shared_ptr 拷贝性能测试 ===" << std::endl;
        
        // 创建一个源数据包
        T_LIVE_STREAM_PACKET_SHARED source;
        source.data = std::shared_ptr<CHAR[]>(new CHAR[PACKET_SIZE]);
        source.dataSize = PACKET_SIZE;
        memset(source.data.get(), 0xAA, PACKET_SIZE);
        
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<T_LIVE_STREAM_PACKET_SHARED> copies;
        copies.reserve(TEST_ITERATIONS);
        
        for (int i = 0; i < TEST_ITERATIONS; ++i) {
            copies.push_back(source);  // 拷贝，只增加引用计数
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "shared_ptr 拷贝 " << TEST_ITERATIONS << " 次耗时: " 
                  << duration.count() << " 微秒" << std::endl;
        std::cout << "平均每次拷贝: " << (double)duration.count() / TEST_ITERATIONS 
                  << " 微秒" << std::endl;
        std::cout << "最终引用计数: " << source.data.use_count() << std::endl;
        
        // 清理
        copies.clear();
        std::cout << "清理后引用计数: " << source.data.use_count() << std::endl;
    }

    // 测试队列操作性能
    static void testQueueOperations() {
        std::cout << "=== 队列操作性能测试 ===" << std::endl;
        
        std::queue<T_LIVE_STREAM_PACKET_SHARED> queue;
        
        // 测试 push 操作
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < TEST_ITERATIONS; ++i) {
            T_LIVE_STREAM_PACKET_SHARED packet;
            packet.data = std::shared_ptr<CHAR[]>(new CHAR[PACKET_SIZE]);
            packet.dataSize = PACKET_SIZE;
            packet.sequence = i;
            
            queue.push(std::move(packet));
        }
        
        auto mid = std::chrono::high_resolution_clock::now();
        
        // 测试 pop 操作
        std::vector<T_LIVE_STREAM_PACKET_SHARED> results;
        results.reserve(TEST_ITERATIONS);
        
        while (!queue.empty()) {
            results.push_back(std::move(queue.front()));
            queue.pop();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        
        auto push_duration = std::chrono::duration_cast<std::chrono::microseconds>(mid - start);
        auto pop_duration = std::chrono::duration_cast<std::chrono::microseconds>(end - mid);
        
        std::cout << "Push " << TEST_ITERATIONS << " 个包耗时: " 
                  << push_duration.count() << " 微秒" << std::endl;
        std::cout << "Pop " << TEST_ITERATIONS << " 个包耗时: " 
                  << pop_duration.count() << " 微秒" << std::endl;
        std::cout << "总耗时: " << (push_duration + pop_duration).count() << " 微秒" << std::endl;
    }

    // 多线程性能测试
    static void testMultiThreadPerformance() {
        std::cout << "=== 多线程性能测试 ===" << std::endl;
        
        std::atomic<int> produced(0);
        std::atomic<int> consumed(0);
        std::queue<T_LIVE_STREAM_PACKET_SHARED> queue;
        std::mutex queue_mutex;
        std::condition_variable cv;
        std::atomic<bool> stop_flag(false);
        
        auto start = std::chrono::high_resolution_clock::now();
        
        // 生产者线程
        std::vector<std::thread> producers;
        for (int t = 0; t < THREAD_COUNT / 2; ++t) {
            producers.emplace_back([&]() {
                for (int i = 0; i < TEST_ITERATIONS / (THREAD_COUNT / 2); ++i) {
                    T_LIVE_STREAM_PACKET_SHARED packet;
                    packet.data = std::shared_ptr<CHAR[]>(new CHAR[PACKET_SIZE]);
                    packet.dataSize = PACKET_SIZE;
                    packet.sequence = produced.fetch_add(1);
                    
                    {
                        std::lock_guard<std::mutex> lock(queue_mutex);
                        queue.push(std::move(packet));
                    }
                    cv.notify_one();
                }
            });
        }
        
        // 消费者线程
        std::vector<std::thread> consumers;
        for (int t = 0; t < THREAD_COUNT / 2; ++t) {
            consumers.emplace_back([&]() {
                while (!stop_flag.load()) {
                    std::unique_lock<std::mutex> lock(queue_mutex);
                    cv.wait(lock, [&] { return !queue.empty() || stop_flag.load(); });
                    
                    if (!queue.empty()) {
                        auto packet = std::move(queue.front());
                        queue.pop();
                        lock.unlock();
                        
                        // 模拟处理
                        consumed.fetch_add(1);
                    }
                }
            });
        }
        
        // 等待生产者完成
        for (auto& t : producers) {
            t.join();
        }
        
        // 等待队列清空
        while (true) {
            std::lock_guard<std::mutex> lock(queue_mutex);
            if (queue.empty()) break;
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        stop_flag.store(true);
        cv.notify_all();
        
        // 等待消费者完成
        for (auto& t : consumers) {
            t.join();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "多线程测试完成:" << std::endl;
        std::cout << "生产: " << produced.load() << " 个包" << std::endl;
        std::cout << "消费: " << consumed.load() << " 个包" << std::endl;
        std::cout << "总耗时: " << duration.count() << " 毫秒" << std::endl;
        std::cout << "吞吐量: " << (double)consumed.load() / duration.count() * 1000 
                  << " 包/秒" << std::endl;
    }

    // 内存使用测试
    static void testMemoryUsage() {
        std::cout << "=== 内存使用测试 ===" << std::endl;
        
        std::cout << "sizeof(T_LIVE_STREAM_PACKET_SHARED): " 
                  << sizeof(T_LIVE_STREAM_PACKET_SHARED) << " 字节" << std::endl;
        std::cout << "sizeof(T_LIVE_STREAM_PACKET_UNIQUE): " 
                  << sizeof(T_LIVE_STREAM_PACKET_UNIQUE) << " 字节" << std::endl;
        std::cout << "sizeof(std::shared_ptr<CHAR[]>): " 
                  << sizeof(std::shared_ptr<CHAR[]>) << " 字节" << std::endl;
        std::cout << "sizeof(std::unique_ptr<CHAR[]>): " 
                  << sizeof(std::unique_ptr<CHAR[]>) << " 字节" << std::endl;
        
        // 测试引用计数开销
        auto ptr = std::shared_ptr<CHAR[]>(new CHAR[PACKET_SIZE]);
        std::cout << "初始引用计数: " << ptr.use_count() << std::endl;
        
        {
            auto ptr2 = ptr;
            std::cout << "拷贝后引用计数: " << ptr.use_count() << std::endl;
            
            auto ptr3 = ptr;
            std::cout << "再次拷贝后引用计数: " << ptr.use_count() << std::endl;
        }
        
        std::cout << "作用域结束后引用计数: " << ptr.use_count() << std::endl;
    }
};

int main() {
    std::cout << "shared_ptr vs unique_ptr 性能对比测试" << std::endl;
    std::cout << "测试迭代次数: " << PerformanceTest::TEST_ITERATIONS << std::endl;
    std::cout << "数据包大小: " << PerformanceTest::PACKET_SIZE << " 字节" << std::endl;
    std::cout << "线程数量: " << PerformanceTest::THREAD_COUNT << std::endl;
    std::cout << std::endl;

    // 运行所有测试
    PerformanceTest::testMemoryUsage();
    std::cout << std::endl;
    
    PerformanceTest::testSharedPtrAllocation();
    std::cout << std::endl;
    
    PerformanceTest::testUniquePtrAllocation();
    std::cout << std::endl;
    
    PerformanceTest::testSharedPtrCopy();
    std::cout << std::endl;
    
    PerformanceTest::testQueueOperations();
    std::cout << std::endl;
    
    PerformanceTest::testMultiThreadPerformance();
    std::cout << std::endl;

    std::cout << "所有测试完成！" << std::endl;
    return 0;
}
