/*******************************************************************************
*
* 功能：  条件变量
*
* 说明：  对于函数的调用：in表示传入,out表示数据带出
*
*
* 作者：  徐辉
*
*******************************************************************************/

#ifndef COND_HPP
#define COND_HPP

#include <string.h>
#include "MyTypes.h"
#include "MacroFunc.h"
#ifdef LINUX
	#include <pthread.h>
	#include <time.h>
	#include <sys/time.h>
#endif
#include "./Lock.hpp"


//==============================================================================
// 条件变量
//==============================================================================
class TCondition
{
public:

	TCondition()
	{
#ifdef WIN32
		m_lWaitNums  = 0;
		m_hNotifyEvnt= ::CreateEvent(NULL, FALSE, FALSE, NULL);
		QF_ASSERT(m_hNotifyEvnt != NULL);
#elif defined(LINUX)
		pthread_condattr_t cond_attr;

		pthread_condattr_init(&cond_attr);
		pthread_condattr_setclock(&cond_attr, CLOCK_MONOTONIC);	// 使用绝对时间
		pthread_cond_init(&m_tCondition, &cond_attr);
		pthread_condattr_destroy(&cond_attr);	
#endif
	}
	
	~TCondition()
	{
#ifdef WIN32
		BOOL bRet = ::CloseHandle(m_hNotifyEvnt);
		QF_ASSERT(bRet == TRUE);
#elif defined(LINUX)
		 pthread_cond_destroy(&m_tCondition);
#endif
	}

	void Wait(TLock *ptLock)
	{
#ifdef WIN32
		(void)TimeWait(ptLock, INFINITE);
#elif defined(LINUX)
		this->TimedWait(ptLock, 0);
#endif
	}
	
	UINT8 TimeWait(TLock *ptLock, long lWaitTime)	
	{
#ifdef WIN32
		DWORD	dwRet;
	
		ptLock->Leave();
		m_lWaitNums++;
		dwRet = ::WaitForSingleObject(m_hNotifyEvnt, lWaitTime);
		m_lWaitNums--;
		ptLock->Enter();
	
		return (dwRet == WAIT_OBJECT_0);
#elif defined(LINUX)
		return this->TimedWait(ptLock, lWaitTime);
#endif
	}

	void NotifyOne()
	{
#ifdef WIN32
		BOOL	bRet = ::SetEvent(m_hNotifyEvnt);

		QF_ASSERT(bRet == TRUE);
#elif defined(LINUX)
		pthread_cond_signal(&m_tCondition);
#endif
	}
	
	void NotifyAll()
	{
#ifdef WIN32
		BOOL	bRet;

		for (int i = m_lWaitNums; i > 0; --i)
		{
			bRet = ::SetEvent(m_hNotifyEvnt);
			QF_ASSERT(bRet == TRUE);
		}
#elif defined(LINUX)
		 pthread_cond_broadcast(&m_tCondition);
#endif
	}

private:

#ifdef WIN32

	HANDLE			m_hNotifyEvnt;	// 通知事件
	volatile long	m_lWaitNums;	// 锁定次数

#elif defined(LINUX)

	pthread_cond_t      m_tCondition;
	UINT8 TimedWait(TLock *ptLock, long to_ms)
	{	
		TCSLock	*ptCSLock = ((TCSLock*)ptLock);

		if (to_ms <= 0)
			(void)pthread_cond_wait(&m_tCondition, &ptCSLock->m_tMutex);
		else
		{
			struct timespec ts;
			long long tm_to_ns;		
			
			clock_gettime(CLOCK_MONOTONIC, &ts);
			tm_to_ns = ts.tv_sec * 1e9 + ts.tv_nsec;
			tm_to_ns += to_ms * 1e6;
			
			ts.tv_sec = tm_to_ns * 1e-9;
			ts.tv_nsec = tm_to_ns - (ts.tv_sec * 1e9);	


			if (pthread_cond_timedwait(&m_tCondition, &ptCSLock->m_tMutex, &ts) EQU ETIMEDOUT)
				return FALSE;			
		}
		
		return TRUE;
	}

#endif

};

#endif
