/*
 *  avilib.h
 *
 *  Copyright (C) <PERSON> s<PERSON> - June 2001
 *  multiple audio track support Copyright (C) 2002 <PERSON>
 *
 *  Original code:
 *  Copyright (C) 1999 <PERSON><PERSON> <<PERSON><PERSON>@Johanni.de> 
 *
 *  This file is part of transcode, a linux video stream processing tool
 *      
 *  transcode is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2, or (at your option)
 *  any later version.
 *   
 *  transcode is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *   
 *  You should have received a copy of the GNU General Public License
 *  along with GNU Make; see the file COPYING.  If not, write to
 *  the Free Software Foundation, 675 Mass Ave, Cambridge, MA 02139, USA. 
 *
 */

#include <sys/types.h>
#include <sys/stat.h>
#include <stdio.h>
#include <fcntl.h>

//SLM
#ifdef __CYGWIN__
#include <sys/types.h>
#elif defined WIN32
#if defined __GNUWIN32__
#include <stdint.h>
#else
#define uint32_t unsigned __int32
#define uint8_t unsigned __int8
#define uint16_t unsigned __int16
#define uint64_t unsigned __int64
#endif
#else
#include <unistd.h>
#include <inttypes.h>
#endif


#include <limits.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

#ifndef AVILIB_H
#define AVILIB_H

#define AVI_MAX_TRACKS 8

typedef struct
{
  off_t key;
  off_t pos;
  off_t len;
} video_index_entry;

typedef struct
{
   off_t pos;
   off_t len;
   off_t tot;
} audio_index_entry;

typedef struct track_s
{

    long   a_fmt;             /* Audio format, see #defines below */
    long   a_chans;           /* Audio channels, 0 for no audio */
    long   a_rate;            /* Rate in Hz */
    long   a_bits;            /* bits per audio sample */
    long   mp3rate;           /* mp3 bitrate kbs*/

    long   audio_strn;        /* Audio stream number */
    off_t  audio_bytes;       /* Total number of bytes of audio data */
    long   audio_chunks;      /* Chunks of audio data in the file */

    char   audio_tag[4];      /* Tag of audio data */
    long   audio_posc;        /* Audio position: chunk */
    long   audio_posb;        /* Audio position: byte within chunk */
 
    off_t a_codech_off;       /* absolut offset of audio codec information */ 
    off_t a_codecf_off;       /* absolut offset of audio codec information */ 

    audio_index_entry *audio_index;

} track_t;

typedef struct
{
  uint32_t  bi_size;
  uint32_t  bi_width;
  uint32_t  bi_height;
  uint16_t  bi_planes;
  uint16_t  bi_bit_count;
  uint32_t  bi_compression;
  uint32_t  bi_size_image;
  uint32_t  bi_x_pels_per_meter;
  uint32_t  bi_y_pels_per_meter;
  uint32_t  bi_clr_used;
  uint32_t  bi_clr_important;
} BITMAPINFOHEADER_avilib;

typedef struct
{
  uint16_t  w_format_tag;
  uint16_t  n_channels;
  uint32_t  n_samples_per_sec;
  uint32_t  n_avg_bytes_per_sec;
  uint16_t  n_block_align;
  uint16_t  w_bits_per_sample;
  uint16_t  cb_size;
} WAVEFORMATEX_avilib;

typedef struct
{
  uint32_t fcc_type; 
  uint32_t fcc_handler; 
  uint32_t dw_flags; 
  uint32_t dw_caps; 
  uint16_t w_priority;
  uint16_t w_language;
  uint32_t dw_scale;
  uint32_t dw_rate;
  uint32_t dw_start;
  uint32_t dw_length;
  uint32_t dw_initial_frames;
  uint32_t dw_suggested_buffer_size;
  uint32_t dw_quality;
  uint32_t dw_sample_size;
  uint32_t dw_left;
  uint32_t dw_top;
  uint32_t dw_right;
  uint32_t dw_bottom;
  uint32_t dw_edit_count;
  uint32_t dw_format_change_count;
  char     sz_name[64];
} AVISTREAMINFO;

typedef struct
{
  
  long   fdes;              /* File descriptor of AVI file */
  long   mode;              /* 0 for reading, 1 for writing */
  
  long   width;             /* Width  of a video frame */
  long   height;            /* Height of a video frame */
  double fps;               /* Frames per second */
  char   compressor[8];     /* Type of compressor, 4 bytes + padding for 0 byte */
  char   compressor2[8];     /* Type of compressor, 4 bytes + padding for 0 byte */
  long   video_strn;        /* Video stream number */
  long   video_frames;      /* Number of video frames */
  char   video_tag[4];      /* Tag of video data */
  long   video_pos;         /* Number of next frame to be read
			       (if index present) */
  
  unsigned long max_len;    /* maximum video chunk present */
  
  track_t track[AVI_MAX_TRACKS];  // up to AVI_MAX_TRACKS audio tracks supported
  
  off_t pos;        /* position in file */
  long   n_idx;             /* number of index entries actually filled */
  long   max_idx;           /* number of index entries actually allocated */
  
  off_t v_codech_off;       /* absolut offset of video codec (strh) info */ 
  off_t v_codecf_off;       /* absolut offset of video codec (strf) info */ 
  
  unsigned char (*idx)[16]; /* index entries (AVI idx1 tag) */
  video_index_entry *video_index;
  
  off_t last_pos;          /* Position of last frame written */
  unsigned long last_len;          /* Length of last frame written */
  int must_use_index;              /* Flag if frames are duplicated */
  off_t movi_start;
  
  int anum;            // total number of audio tracks 
  int aptr;            // current audio working track 
  
  BITMAPINFOHEADER_avilib *bitmap_info_header;
  WAVEFORMATEX_avilib *wave_format_ex[AVI_MAX_TRACKS];
} avi_t;

#define AVI_MODE_WRITE  0
#define AVI_MODE_READ   1

/* The error codes delivered by avi_open_input_file */

#define AVI_ERR_SIZELIM      1     /* The write of the data would exceed
                                      the maximum size of the AVI file.
                                      This is more a warning than an error
                                      since the file may be closed safely */

#define AVI_ERR_OPEN         2     /* Error opening the AVI file - wrong path
                                      name or file nor readable/writable */

#define AVI_ERR_READ         3     /* Error reading from AVI File */

#define AVI_ERR_WRITE        4     /* Error writing to AVI File,
                                      disk full ??? */

#define AVI_ERR_WRITE_INDEX  5     /* Could not write index to AVI file
                                      during close, file may still be
                                      usable */

#define AVI_ERR_CLOSE        6     /* Could not write header to AVI file
                                      or not truncate the file during close,
                                      file is most probably corrupted */

#define AVI_ERR_NOT_PERM     7     /* Operation not permitted:
                                      trying to read from a file open
                                      for writing or vice versa */

#define AVI_ERR_NO_MEM       8     /* malloc failed */

#define AVI_ERR_NO_AVI       9     /* Not an AVI file */

#define AVI_ERR_NO_HDRL     10     /* AVI file has no has no header list,
                                      corrupted ??? */

#define AVI_ERR_NO_MOVI     11     /* AVI file has no has no MOVI list,
                                      corrupted ??? */

#define AVI_ERR_NO_VIDS     12     /* AVI file contains no video data */

#define AVI_ERR_NO_IDX      13     /* The file has been opened with
                                      getIndex==0, but an operation has been
                                      performed that needs an index */
// xuhui add 
#define AVI_ERR_SEEK_IDX    14		/*定位索引失败*/  

/* Possible Audio formats */

#ifndef WAVE_FORMAT_PCM
#define WAVE_FORMAT_UNKNOWN             (0x0000)
#define WAVE_FORMAT_PCM                 (0x0001)
#define WAVE_FORMAT_ADPCM               (0x0002)
#define WAVE_FORMAT_IBM_CVSD            (0x0005)
#define WAVE_FORMAT_ALAW                (0x0006)
#define WAVE_FORMAT_MULAW               (0x0007)
#define WAVE_FORMAT_OKI_ADPCM           (0x0010)
#define WAVE_FORMAT_DVI_ADPCM           (0x0011)
#define WAVE_FORMAT_DIGISTD             (0x0015)
#define WAVE_FORMAT_DIGIFIX             (0x0016)
#define WAVE_FORMAT_YAMAHA_ADPCM        (0x0020)
#define WAVE_FORMAT_DSP_TRUESPEECH      (0x0022)
#define WAVE_FORMAT_GSM610              (0x0031)
#define WAVE_FORMAT_AAC              	(0xa106)	// 原来的0x00ff在微软官方定义不匹配
#define IBM_FORMAT_MULAW                (0x0101)
#define IBM_FORMAT_ALAW                 (0x0102)
#define IBM_FORMAT_ADPCM                (0x0103)
#endif

/**
 * 打开输出文件
 * @param  filename 文件名
 * @return          成功返回avi指针；失败返回NULL
 */
avi_t* AVI_open_output_file(const char * filename);

/**
 * 设置图像格式
 * @param AVI        avi指针
 * @param width      图像宽
 * @param height     图像高
 * @param fps        fps
 * @param compressor 编码方式:如"H264"
 */
void AVI_set_video(avi_t *AVI, int width, int height, double fps, const char *compressor);
/**
 * 设置音频格式	
 * @param AVI      avi指针
 * @param channels 通道数
 * @param rate     频率
 * @param bits     位率
 * @param format   格式
 * @param mp3rate  mp3rate
 */
void AVI_set_audio(avi_t *AVI, int channels, long rate, int bits, int format, long mp3rate);
/**
 * 写入一帧图像
 * @param  AVI      avi指针
 * @param  data     数据
 * @param  bytes    数据长度
 * @param  keyframe 是否关键帧
 * @return          成功返回0；失败返回-1
 */
int  AVI_write_frame(avi_t *AVI, char *data, long bytes, int keyframe);
/**
 * 复制上一帧
 * @param  AVI avi指针
 * @return     成功返回0；失败返回-1
 */
int  AVI_dup_frame(avi_t *AVI);
/**
 * 写入一帧音频
 * @param  AVI   avi指针
 * @param  data  数据
 * @param  bytes 数据长度
 * @return       成功返回0；失败返回-1
 */
int  AVI_write_audio(avi_t *AVI, char *data, long bytes);
/**
 * 在上一个音频帧后追加音频数据
 * @param  AVI   avi指针
 * @param  data  数据
 * @param  bytes 数据长度
 * @return       成功返回0；失败返回-1
 */
int  AVI_append_audio(avi_t *AVI, char *data, long bytes);
/**
 * 剩下大小,由于单文件是2*10的9次方,即2G单文件大小,至于为何不清楚
 * @param  AVI avi指针
 * @return     [description]
 */
long AVI_bytes_remain(avi_t *AVI);
/**
 * 关闭文件
 * @param  AVI avi指针
 * @return     成功返回0；失败返回-1
 */
int  AVI_close(avi_t *AVI);
/**
 * 返回写入的数据所占空间大小
 * @param  AVI avi指针
 * @return     文件大小 
 */
long AVI_bytes_written(avi_t *AVI);

/**
 * 打开输入文件
 * @param  filename 文件名
 * @param  getIndex 暂时未知,从1开始
 * @return          成功返回avi指针；失败返回NULL
 */
avi_t *AVI_open_input_file(const char *filename, int getIndex);
/**
 * 打开输入文件
 * @param  fd 		文件句柄
 * @param  getIndex 暂时未知,从1开始
 * @return          成功返回avi指针；失败返回NULL
 */
avi_t *AVI_open_fd(int fd, int getIndex);
/**
 * 解析avi文件,AVI_open_*函数自动调用此函数, 解析失败会释放AVI,请注意使用
 * @param  AVI      avi指针
 * @param  getIndex 暂时未知,从1开始
 * @return          成功返回0；失败返回-1
 */
int avi_parse_input_file(avi_t *AVI, int getIndex);
/**
 * 得到mp3rate    
 */
long AVI_audio_mp3rate(avi_t *AVI);
/**
 * 得到视频总帧数    
 */
long AVI_video_frames(avi_t *AVI);
/**
 * 得到视频宽    
 */
int  AVI_video_width(avi_t *AVI);
/**
 * 得到视频高   
 */
int  AVI_video_height(avi_t *AVI);
/**
 * 得到帧率:fps   
 */
double AVI_frame_rate(avi_t *AVI);
/**
 * 得到视频编码格式   
 */
char* AVI_video_compressor(avi_t *AVI);
/**
 * 得到音频通道数   
 */
int  AVI_audio_channels(avi_t *AVI);
/**
 * 得到音频位率 
 */
int  AVI_audio_bits(avi_t *AVI);
/**
 * 得到音频编码格式 
 */
int  AVI_audio_format(avi_t *AVI);
/**
 * 得到音频采样 
 */
long AVI_audio_rate(avi_t *AVI);
/**
 * 得到音频字节数 
 */
long AVI_audio_bytes(avi_t *AVI);
/**
 * 得到音频总帧数
 */
long AVI_audio_chunks(avi_t *AVI);
/**
 * 得到视频总帧数
 */
long AVI_max_video_chunk(avi_t *AVI);

/**
 * 得到指定视频帧大小
 * @param  frame 帧序号
 * @return       帧大小
 */
long AVI_frame_size(avi_t *AVI, long frame);
/**
 * 得到指定音频帧大小
 * @param  frame 帧序号
 * @return       帧大小
 */
long AVI_audio_size(avi_t *AVI, long frame);
/**
 * 定位到第一帧
 */
int  AVI_seek_start(avi_t *AVI);
/**
 * 定位到第frame视频
 * @param  frame 帧序号
 * @return       成功返回0；失败返回-1
 */
int  AVI_set_video_position(avi_t *AVI, long frame);
/**
 * 得到第frame视频所在文件位置
 * @param  frame 帧序号
 * @return       返回对应文件位置
 */
long AVI_get_video_position(avi_t *AVI, long frame);
/**
 * 读取当前视频帧,请确保vidbuf尺寸>=AVI_frame_size
 * @param  vidbuf   缓冲存
 * @param  keyframe 是否为关键帧
 * @return          返回实时读取大小
 */
long AVI_read_frame(avi_t *AVI, char *vidbuf, int *keyframe);

/**
 * 定位到byte对应的音频帧,从头开始
 * @param  byte	 byte大小
 * @return       成功返回0；失败返回-1
 */
int  AVI_set_audio_position(avi_t *AVI, long byte);
int  AVI_set_audio_bitrate(avi_t *AVI, long bitrate);
/**
 * 读取当前音频帧,请确保audbuf尺寸>=AVI_audio_size
 * @param  audbuf   缓冲区
 * @param  bytes 	AVI_audio_size的返回值
 * @return          返回实时读取大小
 */
long AVI_read_audio(avi_t *AVI, char *audbuf, long bytes);
/**
 * 读取一个chunk的数据
 * @param  audbuf 缓冲区
 * @return        返回实时读取大小
 */
long AVI_read_audio_chunk(avi_t *AVI, char *audbuf);

/**
 * 以下四个函数关于音频或是视频的codec偏移地址,感觉没有啥大用
 */
long AVI_audio_codech_offset(avi_t *AVI);
long AVI_audio_codecf_offset(avi_t *AVI);
long AVI_video_codech_offset(avi_t *AVI);
long AVI_video_codecf_offset(avi_t *AVI);

/**
 * 用于读取下一个音频或视频块的特殊程序，无需指定文件索引
 * @param  vidbuf     视频缓冲
 * @param  max_vidbuf 视频缓冲大小
 * @param  audbuf     音频缓冲 
 * @param  max_audbuf 音频缓冲大小
 * @param  len        读到的音频或是视频大小
 * @param  keyframe   是否为关键帧(xuhui add) 
 * @return           
 *    1 = video data read
 *    2 = audio data read
 *    0 = reached EOF
 *   -1 = video buffer too small
 *   -2 = audio buffer too small
 */
int  AVI_read_data(avi_t *AVI, char *vidbuf, long max_vidbuf,
                               char *audbuf, long max_audbuf,
                               long *len, int *keyframe);

/** xuhui add
 * 定位到第frame视频, 主要配合AVI_read_data使用
 * @param  frame 帧序号
 * @return       成功返回0；失败返回-1
 */
int AVI_seek_frame(avi_t *AVI, long frame);

/** xuhui add
 * 得到当前读到第多少帧
 * @return       成功第多少帧；失败返回-1
 */
long AVI_get_frame_pos(avi_t *AVI);

/**
 * 打印错误信息
 * @param str 在错误信息前加前缀
 */
void AVI_print_error(char *str);
/**
 * 得到错误信息
 */
char *AVI_strerror();
/**
 * 以下未定义
 */
char *AVI_syserror();
int AVI_scan(char *name);
int AVI_dump(char *name, int mode);
char *AVI_codec2str(short cc);
int AVI_file_check(char *import_file);
void AVI_info(avi_t *avifile);

/**
 * 得到文件允许最大尺寸
 */
uint64_t AVI_max_size();
/**
 * 更新avi头
 * @return     成功返回0；失败返回-1
 */
int avi_update_header(avi_t *AVI);
/**
 * 设置当前track
 */
int AVI_set_audio_track(avi_t *AVI, int track);
/**
 * 得到当前track
 */
int AVI_get_audio_track(avi_t *AVI);
/**
 * 得到音频总track数
 */
int AVI_audio_tracks(avi_t *AVI);


struct riff_struct 
{
  unsigned char id[4];   /* RIFF */
  uint32_t len;
  unsigned char wave_id[4]; /* WAVE */
};


struct chunk_struct 
{
	unsigned char id[4];
	uint32_t len;
};

struct common_struct 
{
	uint16_t wFormatTag;
	uint16_t wChannels;
	uint32_t dwSamplesPerSec;
	uint32_t dwAvgBytesPerSec;
	uint16_t wBlockAlign;
	uint16_t wBitsPerSample;  /* Only for PCM */
};

struct wave_header 
{
	struct riff_struct   riff;
	struct chunk_struct  format;
	struct common_struct common;
	struct chunk_struct  data;
};



struct AVIStreamHeader {
  long  fccType;
  long  fccHandler;
  long  dwFlags;
  long  dwPriority;
  long  dwInitialFrames;
  long  dwScale;
  long  dwRate;
  long  dwStart;
  long  dwLength;
  long  dwSuggestedBufferSize;
  long  dwQuality;
  long  dwSampleSize;
};

#endif

