# PlaybackManager 重构方案

## 1. 职责分离重构

### 原始问题
- PlaybackManager承担会话管理、文件操作、导出功能等多重职责
- 复杂的状态机难以维护
- 资源管理分散，容易内存泄漏

### 重构方案：拆分为3个专职类

```cpp
// 1. 回放会话管理（简化）
class PlaybackSessionManager {
public:
    int createSession();
    bool destroySession(int sessionId);
    PlaybackSession* getSession(int sessionId);
    
private:
    std::map<int, std::unique_ptr<PlaybackSession>> m_sessions;
    std::atomic<int> m_nextSessionId{1};
    TCSLock m_sessionLock;
};

// 2. 媒体文件读取器（简化）
class MediaFileReader {
public:
    bool openFile(const std::string& filePath, uint32_t startTime);
    int readFrame(char* buffer, uint32_t bufferSize);
    bool seekToTime(uint32_t timestamp);
    void closeFile();
    
private:
    FILE* m_videoFile;
    FILE* m_indexFile;
    T_INDEX_ENTRY m_currentIndex;
};

// 3. 回放控制器（简化）
class PlaybackController {
public:
    bool startPlayback(int channelId, uint32_t startTime, uint32_t endTime);
    void stopPlayback();
    void pausePlayback(bool pause);
    void setSpeed(int speed);
    
private:
    PlaybackSessionManager m_sessionManager;
    MediaFileReader m_fileReader;
    std::atomic<bool> m_isPlaying{false};
};
```

## 2. 状态机简化

### 原始问题
- 回放状态转换复杂
- 状态检查分散在多个函数中
- 状态不一致导致的bug

### 重构方案：简化状态模式

```cpp
// 简化的回放状态
enum class PlaybackState {
    STOPPED,
    PLAYING,
    PAUSED,
    SEEKING,
    ERROR
};

// 状态转换表
class PlaybackStateMachine {
public:
    bool canTransition(PlaybackState from, PlaybackState to);
    PlaybackState transition(PlaybackState current, PlaybackEvent event);
    
private:
    // 简化的状态转换映射
    std::map<std::pair<PlaybackState, PlaybackEvent>, PlaybackState> m_transitions;
};

// 简化的回放会话
class SimplePlaybackSession {
public:
    bool start(const PlaybackParams& params);
    void stop();
    void pause(bool pause);
    PlaybackState getState() const { return m_state; }
    
private:
    PlaybackState m_state{PlaybackState::STOPPED};
    PlaybackStateMachine m_stateMachine;
    MediaFileReader m_reader;
};
```

## 3. 资源管理简化

### 原始问题
- 内存分配和释放分散
- 文件句柄管理混乱
- 资源泄漏风险高

### 重构方案：RAII资源管理

```cpp
// 自动资源管理类
class PlaybackResource {
public:
    PlaybackResource(size_t bufferSize) 
        : m_videoBuffer(new char[bufferSize])
        , m_audioBuffer(new char[64*1024])
        , m_bufferSize(bufferSize) {}
    
    ~PlaybackResource() {
        delete[] m_videoBuffer;
        delete[] m_audioBuffer;
    }
    
    char* getVideoBuffer() { return m_videoBuffer; }
    char* getAudioBuffer() { return m_audioBuffer; }
    size_t getBufferSize() const { return m_bufferSize; }
    
private:
    char* m_videoBuffer;
    char* m_audioBuffer;
    size_t m_bufferSize;
};

// 文件句柄自动管理
class FileHandle {
public:
    FileHandle(const std::string& path, const std::string& mode)
        : m_file(fopen(path.c_str(), mode.c_str())) {}
    
    ~FileHandle() {
        if (m_file) {
            fclose(m_file);
        }
    }
    
    FILE* get() { return m_file; }
    bool isValid() const { return m_file != nullptr; }
    
private:
    FILE* m_file;
};
```

## 4. 接口简化

### 原始问题
- 接口参数过多
- 返回值类型不一致
- 错误处理复杂

### 重构方案：统一简化接口

```cpp
// 统一的回放参数
struct PlaybackParams {
    int channelId;
    uint32_t startTime;
    uint32_t endTime;
    int speed = 1;
    std::string diskPath;
    
    // 验证参数有效性
    bool isValid() const {
        return channelId >= 0 && startTime < endTime;
    }
};

// 统一的回放结果
enum class PlaybackResult {
    SUCCESS,
    INVALID_PARAMS,
    FILE_NOT_FOUND,
    DECODE_ERROR,
    RESOURCE_ERROR
};

// 简化的回放接口
class SimplePlaybackManager {
public:
    PlaybackResult startPlayback(const PlaybackParams& params);
    PlaybackResult stopPlayback();
    PlaybackResult pausePlayback(bool pause);
    PlaybackResult seekToTime(uint32_t timestamp);
    
    // 状态查询
    PlaybackState getState() const;
    uint32_t getCurrentTime() const;
    
private:
    std::unique_ptr<SimplePlaybackSession> m_session;
};
```

## 5. 重构实施步骤

### 第一阶段：接口提取（1天）
1. 定义PlaybackParams和PlaybackResult
2. 创建SimplePlaybackManager接口
3. 保持原有实现不变

### 第二阶段：资源管理重构（2天）
1. 实现PlaybackResource和FileHandle
2. 替换原有的手动内存管理
3. 确保无内存泄漏

### 第三阶段：状态机简化（2天）
1. 实现简化的状态机
2. 重构状态转换逻辑
3. 统一状态检查

### 第四阶段：职责分离（2天）
1. 创建专职类
2. 迁移功能到对应类
3. 保持接口兼容

## 6. 预期效果

### 代码量减少
- 原始：2045行 → 重构后：约800行（减少60%）
- 函数平均长度：60行 → 20行（减少67%）

### 复杂度降低
- 状态转换：复杂状态机 → 简单状态表
- 资源管理：手动管理 → RAII自动管理
- 接口参数：平均5个 → 1个结构体

### 可靠性提升
- 自动资源管理，避免内存泄漏
- 简化状态机，减少状态不一致
- 统一错误处理，提高健壮性
