#pragma once

#include <string>
#include <map>
#include "low_delay_common.h"
#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "vs_media_buffer.h"
#include <memory>
#include "disk_manager.h"
#include "buffer_manager.h"
#include "record.h"
#include "playback.h"

class RecordChannel;			// 录像通道类
class RecordManager;			// 录像管理类
class PlaybackManager;			// 回放管理类
class BufferManager;			// 缓冲区管理类
// 移除 DiskManager 前向声明，使用全局实例
class RecordEventManager;		// 录像事件管理类

// 每个通道对应一个完整的实例
class Channel 
{
public:
    Channel(INT32 channelId);
    ~Channel();

    // 录像相关
    INT32 startRecord(UINT32 streamMask);
    INT32 stopRecord();
    INT32 pushFrame(UINT32 streamChn, FRAMEINFO_t* hdr, void* data, uint32_t size);
    void setEventType(UINT8 event_type);
    
    // 预录像相关
    INT32 startRecordWithPreRecord(UINT32 streamMask, INT32 preRecordDuration);
    void setPreRecordParams(bool enabled, INT32 duration, INT32 bufferSize = -1);
    bool isPreRecordEnabled() const;
    INT32 getPreRecordDuration() const;
    
    // 磁盘管理相关
    bool cleanupChannelSpace(uint64_t bytesToFree);
    bool requestAsyncCleanup(uint64_t bytesToFree);


    // 缓冲区相关接口
    void closeAllBuffers(); // 新增：关闭所有缓冲区
    
    // 获取通道ID
    INT32 getChannelId() const { return m_channelId; }
    
    // 获取内部对象
    RecordChannel	*getRecordChannel() 			const { return m_recordChannel; }
    RecordManager	*getRecordManager() 			const { return m_recordManager; }
    SimplePlaybackManager	*getPlaybackManager() 			const { return m_playbackManager; }
    BufferManager	*getBufferManager() 			const { return m_bufferManager; }
    // 移除 getDiskManager()，使用全局 g_disk_manager

    // 设置录像管理器
    void setRecordManager(RecordManager* manager) { m_recordManager = manager; }
    
    // 开始事件录像
    INT32 startEventRecord(UINT32 streamMask, ALARM_EVENT_TYPE eventType, INT32 duration);
    
    // 停止事件录像
    INT32 stopEventRecord();

	INT32 m_channelId;

private:
    
    RecordChannel	*m_recordChannel;
    RecordManager	*m_recordManager;
    SimplePlaybackManager	*m_playbackManager;
    BufferManager	*m_bufferManager;
    // 移除 m_diskManager 成员，使用全局 g_disk_manager
    
    // 预录像相关参数
    bool m_preRecordEnabled;
    INT32 m_preRecordDuration;
    INT32 m_preRecordBufferSize;
};

// 通道管理器 - 按需创建通道的唯一入口（单例模式）
class ChannelManager {
public:
    // 获取单例实例
    static ChannelManager* getInstance();

    // 销毁单例实例
    static void destroyInstance();

    // === 通道管理器初始化 ===

    // 初始化通道管理器（仅在主程序启动时调用一次）
    bool initialize();

    // 检查是否已初始化
    bool isInitialized() const { return m_initialized; }

    // 检查模块是否已初始化
    bool isModuleInitialized() const { return m_moduleInitialized; }

    // === 通道生命周期管理（按需创建） ===

    // 获取通道（按需创建，唯一入口）
    Channel* getChannel(INT32 chn);

    // 检查通道是否存在
    bool hasChannel(INT32 chn) const;

    // 销毁指定通道（统一销毁入口）
    bool destroyChannel(INT32 chn);

    // 销毁所有通道（程序退出时调用）
    void destroyAllChannels();

    // === 通道信息查询 ===

    // 获取所有已存在的通道ID列表
    std::vector<INT32> getExistingChannels() const;

    // 🚀 架构重构：获取所有活跃通道ID列表（与RecordEventManager保持一致）
    std::vector<INT32> getActiveChannels() const;

    // 获取通道总数
    size_t getChannelCount() const;

    // === 资源管理控制 ===

    // 禁止外部资源申请标志
    bool isExternalResourceAllowed() const { return false; }

private:
    ChannelManager();  // 私有构造函数
    ~ChannelManager(); // 私有析构函数

    // 禁用拷贝构造和赋值操作
    ChannelManager(const ChannelManager&) = delete;
    ChannelManager& operator=(const ChannelManager&) = delete;

    // 内部方法
    Channel* createChannelInternal(INT32 chn);
    bool isChannelIdValid(INT32 chn) const;

    // 成员变量
    std::map<INT32, Channel*> channels;
    bool m_initialized;                    // 初始化标志
    bool m_moduleInitialized;              // 模块初始化标志

    // 单例相关
    static ChannelManager* m_instance;
    static TCSLock m_instanceMutex;
};

// ========== 顶层初始化接口 ==========

/**
 * 通道模块初始化函数
 * 供主程序启动时调用，其他模块不得调用
 *
 * 支持任意正整数通道ID，无数量限制
 *
 * @return true 初始化成功, false 初始化失败
 */
bool channel_module_initialize();


/**
 * 通道模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void channel_module_cleanup();

/**
 * 检查通道模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool channel_module_is_initialized();


