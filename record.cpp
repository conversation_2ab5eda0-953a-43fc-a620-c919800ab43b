#include "record.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/file.h>
#include <sys/time.h>
#include <sys/statvfs.h>
#include <time.h>
#include <errno.h>
#include <iostream>
#include <dirent.h>     // 目录操作：DIR, opendir, readdir, closedir
#include <unordered_map> // std::unordered_map
#include <memory>       // std::unique_ptr, std::make_unique

#include <atomic>       // C++11 原子操作
#include <string>       // 用于路径处理
#include <chrono>       // 高精度时间

#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "vs_media_buffer.h"
#include "channel.h"
#include "disk_manager.h"  // 引入g_disk_formatting变量
#include "record.h"

// 从文件头部添加DiskManager相关定义
#include "disk_manager.h"


// 创建嵌套目录的辅助函数
static void mkdir_recursive(const char* path)
{
    char tmp[256];
    char* p = NULL;
    size_t len;
    
    snprintf(tmp, sizeof(tmp), "%s", path);
    len = strlen(tmp);
    
    // 删除末尾的文件名部分
    p = strrchr(tmp, '/');
    if (p) {
        *p = '\0';
    } else {
        return; // 没有目录部分
    }
    
    // 递归创建目录
    for (p = tmp + 1; *p; p++) {
        if (*p == '/') {
            *p = '\0';
            mkdir(tmp, 0755);
            *p = '/';
        }
    }
    mkdir(tmp, 0755);
}


#define DEFAULT_EVENT_DURATION 10

// ========== RecordEventManager 类实现 ==========

// 初始化静态成员
RecordEventManager* RecordEventManager::m_instance = nullptr;

// 获取单例实例
RecordEventManager* RecordEventManager::getInstance() {
    if (m_instance == nullptr) {
        m_instance = new RecordEventManager();
    }
    return m_instance;
}

// 构造函数
RecordEventManager::RecordEventManager() : m_initialized(false), m_check_thread_running(false) {
    // 构造函数不做实际初始化，等待init调用
    // TCSLock在构造函数中已经自动初始化
}

// 析构函数
RecordEventManager::~RecordEventManager() {
    uninit();
    // TCSLock在析构函数中会自动销毁
    m_instance = nullptr;
}

// 初始化事件管理器
bool RecordEventManager::init() {
    if (m_initialized) {
        return true;
    }

    // 架构重构：动态事件队列初始化（支持任意通道ID）
    // 使用map的动态初始化，不再需要预分配固定数量的通道
    // 通道事件队列将在首次使用时通过ensureChannelExists()按需创建
    LogI("RecordEventManager初始化完成，支持任意通道ID");

    // 启动检查线程
    m_check_thread_running = true;
    int ret = pthread_create(&m_check_thread, NULL, checkThreadFunc, this);
    if (ret != 0) {
        LogE("创建事件检查线程失败: ret = %d", ret);
        m_check_thread_running = false;
        return false;
    }

    m_initialized = true;
    LogI("录像事件管理模块初始化成功");
    return true;
}

// 反初始化事件管理器
void RecordEventManager::uninit() {
    if (!m_initialized) {
        return;
    }

    // 停止检查线程
    m_check_thread_running = false;
    pthread_join(m_check_thread, NULL);

    // 架构重构：动态清理所有通道的事件队列（支持任意通道ID）
    {
        ScopedLocker lock(m_eventQueuesLock);

        for (auto& pair : m_eventQueues) {
            int channelId = pair.first;
            auto& queue = pair.second;

            if (queue.initialized) {
                {
                    ScopedLocker queueLock(queue.mutex);
                    queue.event_list.clear();
                    queue.event_status.clear();
                    queue.initialized = false;
                }
                LogI("清理通道%d的事件队列", channelId);
            }
        }

        m_eventQueues.clear();
        LogI("所有通道事件队列已清理");
    }

    m_initialized = false;
    LogI("录像事件管理模块反初始化成功");
}

// 架构重构：检查通道是否有效（支持随机通道ID）
bool RecordEventManager::isValidChannel(int channel) const {
    // 移除固定范围限制，支持任意正整数通道ID
    return (channel >= 0);
}

// 架构重构：确保通道事件队列存在（按需创建）
void RecordEventManager::ensureChannelExists(int channel) {
    ScopedLocker lock(m_eventQueuesLock);

    auto it = m_eventQueues.find(channel);
    if (it == m_eventQueues.end()) {
        // 创建新的通道事件队列
        CHANNEL_EVENT_QUEUE newQueue;
        newQueue.initialized = true;
        m_eventQueues[channel] = newQueue;

        LogI("创建新的通道事件队列: chn=%d", channel);
    }
}

// 架构重构：获取所有活跃的通道ID列表
std::vector<int> RecordEventManager::getActiveChannels() const {
    ScopedLocker lock(m_eventQueuesLock);

    std::vector<int> activeChannels;
    for (const auto& pair : m_eventQueues) {
        if (pair.second.initialized) {
            activeChannels.push_back(pair.first);
        }
    }

    return activeChannels;
}

// 架构重构：清理指定通道的所有事件（用于录像文件清理）
void RecordEventManager::clearChannelEvents(int channel) {
    if (!isValidChannel(channel)) {
        LogE("无效的通道ID: %d", channel);
        return;
    }

    ScopedLocker lock(m_eventQueuesLock);

    auto it = m_eventQueues.find(channel);
    if (it != m_eventQueues.end()) {
        // 清理事件队列
        {
            ScopedLocker queueLock(it->second.mutex);
            it->second.event_list.clear();
            it->second.event_status.clear();
        }

        // 移除通道事件队列
        m_eventQueues.erase(it);

        LogI("已清理通道%d的所有事件", channel);
    }
}

// 设置录像事件
void RecordEventManager::setEvent(int channel, REC_STATE_E recState, uint8_t eventType, uint64_t abs_end_time) {
    if (!m_initialized) {
        LogE("录像事件管理模块未初始化");
        return;
    }

    if (!isValidChannel(channel)) {
        LogE("设置录像事件失败: 无效的通道号 %d", channel);
        return;
    }

    // 创建新的事件
    REC_EVENT_S event;
    event.recState = recState;
    event.eventType = eventType;
    
    // 使用系统提供的get_now获取当前时间
    if (!abs_end_time) {
        T_DATETIME dt;
        get_now(&dt);
        time_t local_ts = get_now();
        abs_end_time = local_ts;
    }
    event.abs_end_time = abs_end_time;
    event.channel = channel;

    // 架构重构：确保通道存在，然后添加事件
    ensureChannelExists(channel);

    // 使用ScopedLocker加锁并添加事件
    {
        ScopedLocker lock(m_eventQueues[channel].mutex);
        m_eventQueues[channel].event_list.push_back(event);
    }

    LogI("设置录像事件: chn=%d, state=%d, type=%d, end_time=%llu", 
         channel, recState, eventType, abs_end_time);
}

// 获取录像事件
bool RecordEventManager::getEvent(int channel, REC_EVENT_S& event) {
    if (!m_initialized) {
        LogE("录像事件管理模块未初始化");
        return false;
    }

    if (!isValidChannel(channel)) {
        LogE("获取录像事件失败: 无效的通道号 %d", channel);
        return false;
    }

    // 架构重构：确保通道存在，然后获取事件
    ensureChannelExists(channel);

    // 使用ScopedLocker加锁并获取事件
    bool hasEvent = false;
    {
        ScopedLocker lock(m_eventQueues[channel].mutex);

        if (m_eventQueues[channel].event_list.empty()) {
            return false;
        }

        // 获取并移除第一个事件
        event = m_eventQueues[channel].event_list.front();
        m_eventQueues[channel].event_list.pop_front();
        hasEvent = true;
    }

    if (hasEvent) {
        LogI("获取录像事件: chn=%d, state=%d, type=%d, end_time=%llu", 
             event.channel, event.recState, event.eventType, event.abs_end_time);
    }
    
    return hasEvent;
}

// 发送录像事件消息
bool RecordEventManager::pushEventMsg(int channel, uint8_t eventType, int delay_sec_close) {
    if (!m_initialized) {
        LogE("录像事件管理模块未初始化");
        return false;
    }

    if (!isValidChannel(channel)) {
        LogE("发送录像事件消息失败: 无效的通道号 %d", channel);
        return false;
    }

    // 检查是否有活动磁盘，没有则立即返回
    if (!DiskManager::getInstance()->hasAvailableDisk()) {
        LogW("没有活动磁盘，拒绝录像请求: 通道=%d, 事件类型=%d", channel, eventType);
        return false;
    }
    
    // 根据事件类型设置不同的录像参数
    uint64_t abs_end_time;
    REC_STATE_E recState = REC_STATE_OPEN;

    if (eventType != 0) { // 非0表示有事件
        // 事件录像
        if (delay_sec_close == 0) {
            // 使用默认策略：ALARM_REC_TM_LEN秒后结束
            abs_end_time = getSystemUptimeMs() / 1000 + EVENT_REC_FILE_TIME;
        } else {
            // 使用指定的延迟时间
            abs_end_time = getSystemUptimeMs() / 1000 + delay_sec_close;
        }
        
        LogI("发送事件录像消息: chn=%d, type=%d, delay=%d秒", 
             channel, eventType, delay_sec_close ? delay_sec_close : EVENT_REC_FILE_TIME);
    } else {
        // 普通录像，使用默认的文件切换策略
        abs_end_time = getSystemUptimeMs() / 1000 + ALL_REC_FILE_TIME;
        
        LogI("发送普通录像消息: chn=%d, 时长=%d秒", channel, ALL_REC_FILE_TIME);
    }

    // 设置录像事件
    setEvent(channel, recState, eventType, abs_end_time);
    
    return true;
}

// 获取系统时间（毫秒）
uint64_t RecordEventManager::getSystemUptimeMs() {
    T_DATETIME dt;
    get_now(&dt);
    time_t local_ts = get_now();
    return (uint64_t)local_ts * 1000 + dt.tv.tv_usec / 1000;
}

// 启动事件录像
void RecordEventManager::startEventRecord(int channel, uint8_t eventType, int duration) {
    
    if (!m_initialized) {
        LogE("录像事件管理模块未初始化");
        return;
    }

    if (!isValidChannel(channel)) {
        LogE("启动事件录像失败: 无效的通道号 %d", channel);
        return;
    }

    // 架构重构：确保通道存在
    ensureChannelExists(channel);

    {
        ScopedLocker lock(m_eventQueues[channel].mutex);
        LogI("获取事件队列锁成功: chn=%d", channel);

        auto& event_status = m_eventQueues[channel].event_status;
        auto it = event_status.find(eventType);
        
        uint64_t current_time = getSystemUptimeMs() / 1000; // 转换为秒
        int actual_duration = (duration > 0) ? duration : DEFAULT_EVENT_DURATION;
        
        LogI("当前时间=%llu, 实际持续时间=%d秒", current_time, actual_duration);
        
        if (it != event_status.end() && it->second.is_active) {
            LogI("发现已存在的事件录像: chn=%d, type=%d", channel, eventType);
            // 相同通道相同事件类型正在录像，检查是否需要续期
            if (current_time < it->second.end_time) {
                // 上一次录像没有结束，延长结束时间
                uint64_t old_end_time = it->second.end_time;
                it->second.end_time += actual_duration;
                it->second.duration += actual_duration;
                LogI("事件录像续期: chn=%d, type=%d, 原结束时间=%llu, 新结束时间=%llu, 总时长=%d秒", 
                     channel, eventType, old_end_time, it->second.end_time, it->second.duration);
            } else {
                // 上一次录像已结束，重新开始
                it->second.start_time = current_time;
                it->second.end_time = current_time + actual_duration;
                it->second.duration = actual_duration;
                it->second.is_active = true;
                LogI("事件录像重新开始: chn=%d, type=%d, 开始时间=%llu, 结束时间=%llu", 
                     channel, eventType, it->second.start_time, it->second.end_time);
            }
        } else {
            LogI("创建新的事件录像: chn=%d, type=%d", channel, eventType);
            // 新的事件录像
            EVENT_RECORD_STATUS new_status;
            new_status.event_type = eventType;
            new_status.start_time = current_time;
            new_status.end_time = current_time + actual_duration;
            new_status.is_active = true;
            new_status.duration = actual_duration;
            
            event_status[eventType] = new_status;
            LogI("事件录像开始: chn=%d, type=%d, 开始时间=%llu, 结束时间=%llu, 时长=%d秒", 
                 channel, eventType, new_status.start_time, new_status.end_time, actual_duration);
        }
    }
    
    LogI("释放事件队列锁: chn=%d", channel);
    LogI("=== RecordEventManager::startEventRecord 结束 ===");
}

// 停止事件录像
void RecordEventManager::stopEventRecord(int channel, uint8_t eventType) 
{
    if (!m_initialized) {
        LogE("录像事件管理模块未初始化");
        return;
    }

    if (!isValidChannel(channel)) {
        LogE("停止事件录像失败: 无效的通道号 %d", channel);
        return;
    }

    // 架构重构：确保通道存在
    ensureChannelExists(channel);

    {
        ScopedLocker lock(m_eventQueues[channel].mutex);

        auto& event_status = m_eventQueues[channel].event_status;
        auto it = event_status.find(eventType);

        if (it != event_status.end() && it->second.is_active) {
            it->second.is_active = false;
            LogI("事件录像停止: chn=%d, type=%d", channel, eventType);
        }
    }
}

// 检查事件录像状态
bool RecordEventManager::isEventRecording(int channel, uint8_t eventType) {
    if (!m_initialized || !isValidChannel(channel)) {
        return false;
    }

    // 🚀 架构重构：确保通道存在
    ensureChannelExists(channel);

    bool isRecording = false;
    {
        ScopedLocker lock(m_eventQueues[channel].mutex);

        auto& event_status = m_eventQueues[channel].event_status;
        auto it = event_status.find(eventType);

        isRecording = (it != event_status.end() && it->second.is_active);
    }
    
    // 添加日志，但不要太频繁
    static int log_counter = 0;
    if (++log_counter % 1000 == 0) { // 每1000次检查才打印一次日志
        LogI("事件录像状态检查: chn=%d, type=%d, is_recording=%s", channel, eventType, isRecording ? "是" : "否");
    }
    
    return isRecording;
}

// 延长事件录像时间
void RecordEventManager::extendEventRecord(int channel, uint8_t eventType, int additional_duration) {
    if (!m_initialized) {
        LogE("录像事件管理模块未初始化");
        return;
    }

    if (!isValidChannel(channel)) {
        LogE("延长事件录像失败: 无效的通道号 %d", channel);
        return;
    }

    // 🚀 架构重构：确保通道存在
    ensureChannelExists(channel);

    {
        ScopedLocker lock(m_eventQueues[channel].mutex);

        auto& event_status = m_eventQueues[channel].event_status;
        auto it = event_status.find(eventType);

        if (it != event_status.end() && it->second.is_active) {
            it->second.end_time += additional_duration;
            it->second.duration += additional_duration;
            LogI("事件录像延长: chn=%d, type=%d, 延长%d秒, 新结束时间=%llu",
                 channel, eventType, additional_duration, it->second.end_time);
        }
    }
}

// 检查并结束过期事件
void RecordEventManager::checkAndEndExpiredEvents() {
    uint64_t current_time = getSystemUptimeMs() / 1000; // 转换为秒
    
    // 🚀 架构重构：获取所有活跃通道列表，支持随机通道ID
    std::vector<int> activeChannels = getActiveChannels();

    for (int chn : activeChannels) {
        {
            ScopedLocker lock(m_eventQueues[chn].mutex);
            
            auto& event_status = m_eventQueues[chn].event_status;
            std::vector<uint8_t> expired_events;
            
            // 检查每个事件的状态
            for (auto& pair : event_status) {
                if (pair.second.is_active) {
                    LogI("检查事件状态: chn=%d, type=%d, 当前时间=%llu, 结束时间=%llu, 剩余时间=%lld秒", 
                         chn, pair.first, current_time, pair.second.end_time, 
                         (long long)(pair.second.end_time - current_time));
                    
                    if (current_time >= pair.second.end_time) {
                        expired_events.push_back(pair.first);
                        LogI("发现过期事件: chn=%d, type=%d", chn, pair.first);
                    }
                }
            }
            
            // 结束过期事件
            for (uint8_t event_type : expired_events) {
                event_status[event_type].is_active = false;
                LogI("事件录像自动结束: chn=%d, type=%d, 持续时间=%d秒", 
                     chn, event_type, event_status[event_type].duration);
            }
        }
    }
}

// 检查线程函数
void* RecordEventManager::checkThreadFunc(void* arg) {
    RecordEventManager* pManager = static_cast<RecordEventManager*>(arg);
    
    while (pManager->m_check_thread_running) {
        pManager->checkAndEndExpiredEvents();
        
        // 每秒检查一次
        sleep(1);
    }
    
    return NULL;
}

// ========== RecordChannel 类实现 ==========

RecordChannel::RecordChannel(int channel_id) : 
    chn(channel_id),
    thread_id(0),
    running(false),
    video_fd(-1),
    idx_fd(-1),
    cur_event_type(0),
    previous_event_type(0),
    file_start_time(0),
    file_end_time(0),
    check_next_frame(false),
    day_changed(false),
    need_switch_file(false),
    iframe_wait_start_time(0),
    real_end_time(0),
    no_stream_count(0), // 初始化计数器
    wait_for_iframe(false), // 初始化等待I帧标志
    m_recordingState(false)
{
    // TCSLock 和 TCondition 在构造函数中已经自动初始化
    is_full_time_recording = false;
	pre_record_active = FALSE;
	start_time_modified = false;

	// 初始化磁盘切换保护标志
	m_diskSwitchPending = false;
	m_fileWriteInProgress = false;

	// 初始化平滑过渡机制标志
	m_diskSwitchDeferred = false;

}

RecordChannel::~RecordChannel()
{
    // 确保线程已停止（析构时使用阻塞方式确保完全停止）
    if (running) {
        stop();  // 析构时保持阻塞调用，确保资源完全释放
    }

    // 清理事件映射
    {
        ScopedLocker lock(m_eventLock);
        m_activeEvents.clear();
    }

    // 关闭文件
    if (video_fd >= 0) {
        close(video_fd);
        video_fd = -1;
    }

    if (idx_fd >= 0) {
        close(idx_fd);
        idx_fd = -1;
    }

    LogW("录像通道释放: chn = %d", chn);
    // TCSLock 和 TCondition 在析构函数中会自动清理
}

void RecordChannel::start() 
{
    if (running) {
        LogW("RecordChannel 已经在运行: chn = %d", chn);
        return;
    }
    
    // 确保线程ID被重置
    if (thread_id != 0) {
        thread_id = 0;
    }
    
    // 重置事件等待计数器
    event_wait_count = 0;
    
    // 创建录像线程
    running = true;
    LogI("设置运行标志: chn = %d, running = %d", chn, running);
    
    int ret = pthread_create(&thread_id, NULL, recordThreadFunc, this);
    if (ret != 0) {
        LogE("创建录像线程失败: chn = %d, ret = %d, errno = %d", chn, ret, errno);
        running = false;
        thread_id = 0;
        return;
    }
    
}

void RecordChannel::stop() 
{
    if (!running) {
        LogW("RecordChannel 已经停止: chn = %d", chn);
        return;
    }
    
    running = false;
    
    // 等待线程结束
    if (thread_id != 0) {
        LogI("等待录像线程结束: chn = %d, thread_id = %lu", chn, (unsigned long)thread_id);
        pthread_join(thread_id, NULL);
        LogI("录像线程已结束: chn = %d", chn);
        thread_id = 0; // 重置线程ID
    }
}

void* RecordChannel::recordThreadFunc(void* arg) 
{
    RecordChannel* channel = static_cast<RecordChannel*>(arg);
    if (!channel) {
        LogE("录像线程函数异常: channel 指针为空");
        return NULL;
    }
    
    channel->recordThread();
    return NULL;
}

void RecordChannel::recordThread() 
{
    time_t last_check_time = 0;       // 上次检查文件切换的时间
    time_t last_event_check_time = 0; // 上次检查事件状态的时间
    time_t last_disk_check_time = 0;  // 上次检查磁盘可用性的时间

	T_DATETIME last_local;
	T_DATETIME local_now;
	time_t local_ts;
	QfSet0(&last_local, sizeof(T_DATETIME));
	QfSet0(&local_now, sizeof(T_DATETIME));
	

    T_FRAME_HEADER *frame_hdr = NULL;
    CHAR *buffer = nullptr;

    // 获取通道的缓冲区管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("无法获取 ChannelManager 单例实例");
        return;
    }

    Channel* pChannel = channelManager->getChannel(chn);
    if (!pChannel) {
        LogE("无法获取通道对象: chn=%d", chn);
        return;
    }

    BufferManager* pBufferMgr = pChannel->getBufferManager();
    if (!pBufferMgr) {
        LogE("无法获取缓冲区管理器: chn=%d", chn);
        return;
    }
    
    // 打开读取流 - 使用通道专用的码流
    HANDLE reader = pBufferMgr->openReaderStream(0); // 使用默认码流通道0
    if (reader == INVALID_HANDLE_VALUE) {
        LogE("无法打开媒体读取流: chn=%d", chn);
        return;
    }

    pBufferMgr->resetReaderPosition();

    // 初始化最后检查的日期，直接使用本地时间
	get_now(&last_local);
	
    time_t now = time(NULL);
    last_check_time = now;
    last_event_check_time = now;
    last_disk_check_time = now;  // 初始化磁盘检查时间

    // 主循环
    while (running) {
        // 获取当前时间
        now = time(NULL);
        
        // 获取当前本地时间
        local_ts = get_now(&local_now);

        // 周期性检查磁盘可用性（每10秒检查一次）
        if (now - last_disk_check_time >= 10) {
            last_disk_check_time = now;

            // 检查是否有可用磁盘
            if (!DiskManager::getInstance()->hasAvailableDisk()) {
                LogE("磁盘已不可用或已卸载，停止录像: chn=%d", chn);
                // 关闭当前文件
                if (video_fd >= 0 || idx_fd >= 0) {
                    closeCurrentFile(now);
                }

                // 等待一段时间后再次检查，避免频繁日志
                sleep(3);
                continue;
            }

            // 🔥 关键修复：在录像线程主循环中检查磁盘切换，而不是在文件创建时
            // 这样避免在文件创建时引入延迟
            checkAndExecutePendingDiskSwitch();
        }
        
        // 检查日期是否变化
        bool is_day_changed = checkDateChange(now, &last_local);
        
        // 检查文件是否需要切换
        if (checkFileSwitch(now, last_check_time, is_day_changed)) {
            last_check_time = now;
        }
        
        // 检查事件状态
        if (now - last_event_check_time >= 1) {
            last_event_check_time = now;
            
            // 如果是全天录像模式，检查事件是否已结束，结束后恢复为正常录像类型
            if (is_full_time_recording && video_fd >= 0) {
                uint8_t active_event_type = getActiveEventType(now);
                
                // 如果没有活跃事件，但当前事件类型不是正常录像，则恢复为正常录像
                if (active_event_type == RT_NORMAL && cur_event_type != RT_NORMAL) {
                    previous_event_type = cur_event_type;
                    cur_event_type = RT_NORMAL;
                }
                // 如果有活跃事件，更新当前事件类型
                else if (active_event_type != RT_NORMAL && cur_event_type != active_event_type) {
                    previous_event_type = cur_event_type;
                    cur_event_type = active_event_type;
                }
            }
        }
        
        // 处理事件录像模式下的等待逻辑
        {
            ScopedLocker lock_guard(lock);
            
            bool has_active_event = hasActiveEvent(now);
            
            if (!is_full_time_recording && !has_active_event) {
                bool file_expired = now >= file_end_time;
                
                if (video_fd >= 0 && !file_expired) {
                    // 文件未到期，继续使用
                } else {
                    // 关闭文件
                    if (video_fd >= 0) {
                        closeCurrentFile(now);
                    }
                    
                    // 使用超时等待，确保可以定期检查文件切换
                    bool wait_success = cond.TimeWait(&lock, 1000);
                    if (!wait_success) {
                        // 超时，继续下一次循环检查
                    }
                }
            }
        }
        
        // 根据录像类型选择适当的流
        bool stream_open_failed = false;

        if (is_full_time_recording) {
            if (pre_record_active) {
                pBufferMgr->closeReaderStream();
                reader = pBufferMgr->openReaderStream(0);
                if (reader == INVALID_HANDLE_VALUE) {
                    LogE("无法打开实时流: chn=%d", chn);
                    stream_open_failed = true;
                } else {
                    pre_record_active = FALSE;
                }
            }
        } else if (!pre_record_active) {
            pBufferMgr->closeReaderStream();
            reader = pBufferMgr->openPreReaderStream(0);
            if (reader == INVALID_HANDLE_VALUE) {
                LogE("无法打开预录像流: chn=%d", chn);
                stream_open_failed = true;
            } else {
                pBufferMgr->resetPreReaderPosition();
                pre_record_active = TRUE;
            }
        }

        // 如果流打开失败，等待后重试
        if (stream_open_failed) {
            Sleep(1000); // 等待1秒后重试
            continue;
        }

        // 读取一帧数据
        INT32 ret;
        {
            ScopedLocker lock_guard(lock);
            ret = pBufferMgr->readFrame(&frame_hdr, &buffer);
        }
        
        // 处理流错误
        if (ret != OK) {
            handleStreamError(ret, now);
            Sleep(20);
            continue;
        }

        // 未读到任何数据
        if (frame_hdr == NULL) {
            handleStreamError(ret, now);
            Sleep(20);
            continue;
        }
        
        // 成功获取到流，重置计数器
        no_stream_count = 0;

        // 处理帧数据错误计数器（静态变量，在函数调用间保持状态）
        static int frame_process_error_count = 0;

        if (!processFrame(frame_hdr, buffer, now)) {
            LogE("处理帧数据失败: chn=%d", chn);

            // 增加错误计数器，避免无限循环
            frame_process_error_count++;

            if (frame_process_error_count >= 10) {
                LogE("连续处理帧失败次数过多，暂停录像: chn=%d", chn);
                Sleep(30000); // 暂停30秒
                frame_process_error_count = 0; // 重置计数器
            } else {
                Sleep(1000); // 等待1秒后重试
            }
            continue;
        }

        // 成功处理帧，重置错误计数器
        frame_process_error_count = 0;
    }

    // 关闭文件前，更新最后一条记录
    if (!last_video_name.empty()) {
        time_t local_ts = get_now();
        updateRecordEnd(last_video_name, local_ts);
    }
    
    // 确保关闭所有文件
    CloseFdAndNull(video_fd);
    CloseFdAndNull(idx_fd);

    LogI("录像线程结束: chn=%d", chn);
}

// 准备磁盘切换 - 使用平滑过渡机制
void RecordChannel::prepareForDiskSwitch()
{
    ScopedLocker lock(m_diskSwitchLock);

    LogI("准备磁盘切换（平滑过渡模式）: chn=%d", chn);

    // 设置延迟切换标志，不立即强制关闭文件
    m_diskSwitchDeferred = true;

    LogI("磁盘切换设置为延迟模式，将在下次文件自然切换时执行: chn=%d", chn);
}

// 重置磁盘切换状态（磁盘切换完成后调用）
void RecordChannel::resetDiskSwitchState()
{
    ScopedLocker lock(m_diskSwitchLock);

    // LogI("重置磁盘切换状态: chn=%d", chn);

    // 重置磁盘切换相关标志
    m_diskSwitchPending = false;
    m_fileWriteInProgress = false;
    m_diskSwitchDeferred = false;  // 🔥 关键修复：重置延迟切换标志

    // LogI("磁盘切换状态重置完成: chn=%d", chn);
}

// 检查并执行待处理的磁盘切换
bool RecordChannel::checkAndExecutePendingDiskSwitch()
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager) {
        return false;
    }

    // 检查是否有待处理的磁盘切换
    if (!diskManager->isDiskSwitchPending()) {
        // 🔥 关键修复：如果没有待处理的切换，清除延迟标志
        {
            ScopedLocker lock(m_diskSwitchLock);
            if (m_diskSwitchDeferred) {
                LogI("磁盘切换已完成，清除延迟标志: chn=%d", chn);
                m_diskSwitchDeferred = false;
            }
        }
        return false;
    }

    LogI("检测到待处理的磁盘切换，准备执行: chn=%d", chn);

    // 执行延迟的磁盘切换
    bool result = diskManager->executePendingDiskSwitch();

    if (result) {
        LogI("延迟磁盘切换执行成功: chn=%d", chn);

        // 重置延迟切换标志
        {
            ScopedLocker lock(m_diskSwitchLock);
            m_diskSwitchDeferred = false;
        }
    } else {
        LogW("延迟磁盘切换执行失败: chn=%d", chn);
    }

    return result;
}

// 设置磁盘切换延迟标志
void RecordChannel::setDiskSwitchDeferred(bool deferred)
{
    ScopedLocker lock(m_diskSwitchLock);

    // LogI("设置磁盘切换延迟标志: chn=%d, deferred=%s", chn, deferred ? "true" : "false");

    m_diskSwitchDeferred = deferred;
}

// 检查文件写入是否正在进行
bool RecordChannel::isFileWriteInProgress() const
{
    ScopedLocker lock(m_diskSwitchLock);
    return m_fileWriteInProgress || (video_fd >= 0);
}

// 强制关闭当前文件
void RecordChannel::forceCloseCurrentFile()
{
    ScopedLocker lock(m_diskSwitchLock);

    if (video_fd >= 0 || idx_fd >= 0) {
        LogI("强制关闭当前录像文件: chn=%d", chn);

        time_t now = time(NULL);
        closeCurrentFile(now);

        // 重置文件写入标志
        m_fileWriteInProgress = false;
    }
}

void RecordChannel::setEventType(uint8_t event_type) 
{
    LogI("RecordChannel::setEventType: chn=%d, 原事件类型=%d, 新事件类型=%d", chn, cur_event_type, event_type);
    previous_event_type = cur_event_type;
    cur_event_type = event_type;
    LogI("事件类型设置完成: chn = %d, type = %d", chn, event_type);
}


bool RecordChannel::getCurrentRecordFiles(std::string& video_file, std::string& idx_file) const 
{
    if (last_video_name.empty() || last_idx_name.empty()) {
        return false;
    }
    
    video_file = last_video_name;
    idx_file = last_idx_name;
    return true;
}

// 生成录像文件名
std::string RecordChannel::generateFilename(time_t t, const char* ext)
{
    // 检查是否有可用磁盘
    if (!DiskManager::getInstance()->hasAvailableDisk()) {
        LogE("生成文件名失败: 没有可用的硬盘或硬盘挂载失败");
        return "";
    }
    
    // 🔥 关键修复：使用传入的时间参数，确保时间戳一致性
    T_DATETIME local_now;
    // 将传入的时间戳转换为本地时间结构
    struct tm* tm_ptr = localtime(&t);
    if (tm_ptr) {
        local_now.usYear = tm_ptr->tm_year + 1900;
        local_now.usMonth = tm_ptr->tm_mon + 1;
        local_now.usDay = tm_ptr->tm_mday;
        local_now.ucHour = tm_ptr->tm_hour;
        local_now.ucMin = tm_ptr->tm_min;
        local_now.ucSec = tm_ptr->tm_sec;
    } else {
        // 如果转换失败，使用当前时间作为备选
        time_t local_ts = get_now(&local_now);
    }
    
    char path[256];
    // 目录使用本地时间格式，使用get_channel_record_path获取多磁盘路径
    std::string basePath = get_active_disk_path();

    // 检查基础路径是否有效（没有挂载磁盘时为空）
    if (basePath.empty()) {
        LogE("生成文件名失败: 没有可用的已挂载磁盘");
        return "";
    }
    
    sprintf(path, "%s%04d%02d%02d", basePath.c_str(), 
            local_now.usYear, local_now.usMonth, local_now.usDay);
    
    // 确保目录存在
    ensureDirExist(path);
    
    // 文件名使用本地时间格式
    char filename[512];
    sprintf(filename, "%s/%d/%02d%02d%02d_%d.%s", path, chn,
            local_now.ucHour, local_now.ucMin, local_now.ucSec, chn, ext);

    // 🔥 时间戳连续性验证日志
    LogI("生成录像文件名: %s (时间戳: %ld)", filename, t);

    return filename;
}

void RecordChannel::ensureDirExist(const std::string& filename) 
{
    size_t pos = filename.find_last_of('/');
    if (pos == std::string::npos) {
        return;
    }
    
    std::string dir = filename.substr(0, pos);
    
    // 递归创建目录
    int ret = 0;
    pos = 0;
    while ((pos = dir.find('/', pos + 1)) != std::string::npos) {
        std::string subdir = dir.substr(0, pos);
        ret = mkdir(subdir.c_str(), 0755);
        if (ret != 0 && errno != EEXIST) {
            LogE("创建目录失败: %s, errno = %d", subdir.c_str(), errno);
            return;
        }
    }
    
    ret = mkdir(dir.c_str(), 0755);
    if (ret != 0 && errno != EEXIST) {
        LogE("创建目录失败: %s, errno = %d", dir.c_str(), errno);
    }
}

void RecordChannel::insertRecordStart(const std::string& video_name, const std::string& idx_name, time_t start_time) 
{
    T_ONE_REC one_rec;
    memset(&one_rec, 0, sizeof(one_rec));
    one_rec.st_time = start_time;
    strncpy(one_rec.file_name, video_name.c_str(), sizeof(one_rec.file_name) - 1);
	strncpy(one_rec.idx_name, idx_name.c_str(), sizeof(one_rec.idx_name) - 1);
    one_rec.rec_type = cur_event_type; // 使用当前事件类型
    one_rec.change_st = start_time_modified ? 1 : 0; // 根据是否修改设置标记
    one_rec.flag = OR_FLAG_REC;
    rec_db_add(&one_rec);
    
    // 重置修改标记
    start_time_modified = false;
}

// 关闭当前录像文件
void RecordChannel::closeCurrentFile(time_t now)
{
    // 🔥 时间戳连续性验证日志
    LogI("关闭录像文件: chn=%d, 结束时间戳=%ld, 文件=%s", chn, now, last_video_name.c_str());

    // 更新上一条记录结束标记
    if (!last_video_name.empty()) {
        updateRecordEnd(last_video_name, now);
    }
    
    // 同步文件
    if (video_fd >= 0) fdatasync(video_fd);
    if (idx_fd >= 0) fdatasync(idx_fd);
    
    // 关闭当前文件
    CloseFdAndNull(video_fd);
    CloseFdAndNull(idx_fd);
    pre_record_active = FALSE;
    
//    LogI("关闭当前录像文件: chn=%d, 时间=%ld", chn, now);
}

// 处理流错误
bool RecordChannel::handleStreamError(INT32 ret, time_t now) 
{
    // 增加无流计数
    no_stream_count++;

	// LogW("无法获取流: chn=%d, ret=%d, 连续次数=%d", chn, ret, no_stream_count);
    
    // 如果连续无法获取流的次数超过阈值，关闭当前文件
    if (no_stream_count >= MAX_NO_STREAM_COUNT && video_fd >= 0) {
        LogE("连续无法获取流，关闭当前录像文件: chn=%d, 连续次数=%d", chn, no_stream_count);
        closeCurrentFile(now);
    }
    
    Sleep(5);
	
    return true;
}

// 检查日期变化
bool RecordChannel::checkDateChange(time_t now, T_DATETIME *last_tm) 
{
    // 获取当前本地时间的tm结构
    T_DATETIME current_datetime;
    time_t local_ts = get_now(&current_datetime);
    
    
    // 检查日期是否变化
    if (current_datetime.usYear != last_tm->usYear ||
        current_datetime.usMonth != last_tm->usMonth ||
        current_datetime.usDay != last_tm->usDay) {
        // 更新last_tm
        memcpy(last_tm, &current_datetime, sizeof(T_DATETIME));
//        LogI("日期变化: chn=%d, date=%04d%02d%02d", chn,
//             current_datetime.usYear + 1900, current_datetime.usMonth + 1, current_datetime.usDay);
        return true;
    }
    
    return false;
}

// 检查文件切换
bool RecordChannel::checkFileSwitch(time_t now, time_t last_check_time, bool is_day_changed) 
{
    // 定期检查文件是否需要切换（每秒检查一次）
    if (now - last_check_time < 1) {
        return false;
    }
    
    // 检查是否达到文件切换时间或者跨天
    if (video_fd < 0 || !(now >= file_end_time || is_day_changed)) {
        return false;
    }
    
//    LogW("文件时长已到，等待I帧或最多10秒: chn=%d, 原因=%s",
//         chn, is_day_changed ? "日期变化" : "录像时间到期");
    
    // 设置检查下一帧标志，不关闭文件
    check_next_frame = true;
    
    // 记录实际结束时间和开始等待I帧的时间
    real_end_time = now;
    iframe_wait_start_time = now;
    
    // 如果是日期变化，记录标志，但不立即切换
    if (is_day_changed) {
        day_changed = true;
    }
    
//    LogW("开始等待I帧，最长10秒: chn=%d, 当前时间=%ld", chn, now);
    
    return true;
}

// 添加一个函数，用于直接检查文件路径的挂载状态
bool check_disk_mounted_for_file(const char* file_path)
{
    if (!file_path || !*file_path) {
        LogE("无效的文件路径");
        return false;
    }

    // 提取目录部分
    std::string path_str = file_path;
    size_t last_slash = path_str.find_last_of('/');
    if (last_slash == std::string::npos) {
        LogE("无法从路径中提取目录: %s", file_path);
        return false;
    }

    std::string dir_path = path_str.substr(0, last_slash);

    // 检查是否是DISK_MAPPING中定义的磁盘路径（使用标准路径格式）
    std::string disk_path;
    std::string basePath = "/mnt/custom/disk/";

    // 检查标准磁盘路径格式 /mnt/custom/disk/disk1, disk2, etc.
    if (dir_path.find(basePath) == 0) {
        // 提取磁盘路径部分
        std::string remaining = dir_path.substr(basePath.length());
        size_t next_slash = remaining.find('/');

        if (next_slash != std::string::npos) {
            // 有子目录，提取磁盘名称
            std::string diskName = remaining.substr(0, next_slash);
            disk_path = basePath + diskName;
        } else {
            // 没有子目录，整个路径就是磁盘路径
            disk_path = dir_path;
        }
    }

    if (disk_path.empty()) {
        // 如果不是DISK_MAPPING中定义的路径，视为未挂载
        LogE("未识别的磁盘路径: %s", dir_path.c_str());
        return false;
    }

    // LogI("检查磁盘挂载状态: %s (文件: %s)", disk_path.c_str(), file_path);

    // 直接读取 /proc/mounts 文件检查挂载状态
    FILE* fp = fopen("/proc/mounts", "r");
    if (!fp) {
        LogE("无法打开 /proc/mounts 文件");
        return false;
    }

    char line[512];
    bool mounted = false;

    while (fgets(line, sizeof(line), fp) != NULL) {
        if (strstr(line, disk_path.c_str())) {
            mounted = true;
            // LogI("通过/proc/mounts确认挂载: %s (完整信息: %s)", disk_path.c_str(), line);
            break;
        }
    }
    fclose(fp);

    if (!mounted) {
        LogE("磁盘未挂载: %s (文件: %s)", disk_path.c_str(), file_path);
        return false;
    }

    // LogI("磁盘已挂载: %s", disk_path.c_str());
    return true;
}

// 🚀 优化：智能文件创建策略，避免无硬盘状态下的无效操作
bool RecordChannel::createNewFile(time_t now)
{
    // 🚀 架构重构：使用通道级别的失败计数器，支持随机通道ID
    // 使用unordered_map提高查找性能，支持任意整数通道ID
    static std::unordered_map<int, int> channel_consecutive_failures;
    static std::unordered_map<int, time_t> channel_last_failure_time;
    static TCSLock failure_counter_lock; // 保护静态map的线程安全

    // 🚀 架构重构：辅助函数 - 更新失败计数器
    auto updateFailureCounter = [&]() {
        ScopedLocker lock(failure_counter_lock);
        channel_consecutive_failures[chn]++;
        channel_last_failure_time[chn] = time(nullptr);
    };

    int consecutive_failures = 0;
    time_t last_failure_time = 0;

    // 线程安全地获取失败计数器
    {
        ScopedLocker lock(failure_counter_lock);
        consecutive_failures = channel_consecutive_failures[chn];
        last_failure_time = channel_last_failure_time[chn];
    }

    // 🚀 智能退避策略：根据失败次数动态调整等待时间
    if (consecutive_failures >= 3) {
        time_t current_time = time(nullptr);
        int backoff_time = std::min(60 * (1 << (consecutive_failures - 3)), 300); // 指数退避，最大5分钟

        if (current_time - last_failure_time < backoff_time) {
            // 在退避期间，减少日志输出
            static time_t last_backoff_log = 0;
            if (current_time - last_backoff_log > 60) { // 每分钟最多一次日志
                LogW("通道%d进入退避模式，等待%d秒后重试（失败次数：%d）",
                     chn, backoff_time, consecutive_failures);
                last_backoff_log = current_time;
            }
            return false;
        } else {
            // 退避时间结束，重置计数器
            LogI("通道%d退避时间结束，重新尝试文件创建", chn);
            {
                ScopedLocker lock(failure_counter_lock);
                channel_consecutive_failures[chn] = 0;
                channel_last_failure_time[chn] = 0;
            }
            consecutive_failures = 0;
        }
    }

    // 🚀 优化：智能硬盘检查（使用缓存，减少系统调用）
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->hasAvailableDisk(false)) {
        // 缓存检查失败，进行一次强制检查
        if (!diskManager->hasAvailableDisk(true)) {
            LogE("通道%d创建新录像文件失败: 没有可用的硬盘", chn);
            updateFailureCounter();

            // 重新获取更新后的计数器
            {
                ScopedLocker lock(failure_counter_lock);
                consecutive_failures = channel_consecutive_failures[chn];
            }

            // 🔧 进入待机模式：当连续失败超过10次时，进入长时间待机
            if (consecutive_failures >= 10) {
                LogW("通道%d进入待机模式，等待硬盘重新接入", chn);
            }
            return false;
        }
    }
    
    // 生成文件名
    std::string video_name = generateFilename(now, REC_FILE_EXT);
    std::string idx_name = generateFilename(now, REC_IDX_EXT);
    
    // 检查路径是否有效
    if (video_name.empty() || idx_name.empty()) {
        LogE("创建新录像文件失败: 无效的文件路径");
        return false;
    }
    
    // 首先检查目录是否已挂载
    if (!check_disk_mounted_for_file(video_name.c_str())) {
        LogE("无法创建新文件，磁盘未挂载: %s", video_name.c_str());
        return false;
    }
    
    // 确保目录存在
    ensureDirExist(video_name);
    ensureDirExist(idx_name);
    
    // 在创建文件前进行详细的磁盘状态检查
    struct statvfs disk_stat;
    std::string dir_path = video_name.substr(0, video_name.find_last_of('/'));

    if (statvfs(dir_path.c_str(), &disk_stat) != 0) {
        LogE("无法获取磁盘状态: %s, errno=%d (%s)", dir_path.c_str(), errno, strerror(errno));
        updateFailureCounter();
        return false;
    }

    // 检查磁盘空间（至少需要100MB）
    uint64_t available_bytes = (uint64_t)disk_stat.f_bavail * disk_stat.f_frsize;
    if (available_bytes < 100 * 1024 * 1024) {
        LogE("磁盘空间不足: %s, 可用空间: %llu MB", dir_path.c_str(), available_bytes / (1024 * 1024));
        updateFailureCounter();
        return false;
    }

    // 检查文件系统是否只读
    if (disk_stat.f_flag & ST_RDONLY) {
        LogE("文件系统为只读模式: %s", dir_path.c_str());
        updateFailureCounter();
        return false;
    }

    // 创建文件
    video_fd = open(video_name.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
    idx_fd = open(idx_name.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);

	if (idx_fd < 0 || video_fd < 0) 
	{
        int idx_errno = errno;
        LogE("创建索引文件失败: chn = %d, file = %s, errno=%d (%s)", chn, idx_name.c_str(), idx_errno, strerror(idx_errno));

        // 🚨 磁盘写入错误统一紧急处理 - 索引文件
        if (idx_errno == 28 || idx_errno == 5 || idx_errno == 30 || idx_errno == 122 || idx_errno == 27 || idx_errno == 74) {

            // 调用DiskManager的紧急处理函数（非阻塞）
            DiskManager* diskManager = DiskManager::getInstance();
            if (diskManager) {
                
                LogE("调用磁盘写入错误紧急处理: 通道=%d, 需要空间=512MB", chn);
                bool handled = diskManager->handleDiskSpaceEmergency(idx_name, chn);
                if (!handled) {
                    LogE("磁盘写入错误紧急处理已启动，建议稍后重试");
                }
            }
        }
		
        // 清理已创建的视频文件
        CloseFdAndNull(video_fd);
		CloseFdAndNull(idx_fd);

        // 更新失败计数器
        updateFailureCounter();
        return false;
   }
    
//    LogI("创建新文件: video_fd=%d, idx_fd=%d, video_name=%s",
//         video_fd, idx_fd, video_name.c_str());
    
    // 设置新文件的开始和结束时间
    file_start_time = now;
    
    // 根据录像类型设置文件时长
    if (is_full_time_recording) {
        file_end_time = file_start_time + ALL_REC_FILE_TIME;
    } else if (cur_event_type != RT_NORMAL) {
        file_end_time = file_start_time + EVENT_REC_FILE_TIME;
    } else {
        file_end_time = file_start_time + REC_FILE_TIME;
    }
    
    // 插入新录像记录
    insertRecordStart(video_name, idx_name, file_start_time);
    
    // 保存当前录像文件名
    last_video_name = video_name;
    last_idx_name = idx_name;
    
//    LogI("新文件创建完成: chn=%d, 开始时间=%ld, 结束时间=%ld",
//         chn, file_start_time, file_end_time);
    
    // 设置等待I帧标志，确保新文件的第一帧是I帧
    wait_for_iframe = true;

    // 成功创建文件，重置失败计数器
    consecutive_failures = 0;

    // 关键修复：文件创建成功后清除延迟切换状态
    {
        ScopedLocker lock(m_diskSwitchLock);
        if (m_diskSwitchDeferred) {
            LogI("文件创建成功，清除延迟切换标志: chn=%d", chn);
            m_diskSwitchDeferred = false;
        }
    }

    return true;
}

// 处理帧数据
bool RecordChannel::processFrame(T_FRAME_HEADER *frame_hdr, CHAR *buffer, time_t now)
{
    // 检查是否有活动磁盘，没有则立即返回，不写入
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager && !diskManager->hasAvailableDisk()) {
        // 静默返回，不记录错误日志（避免日志刷屏）
        return true;
    }

    // 设置文件写入进行中标志（平滑过渡：不阻塞录像）
    {
        ScopedLocker lock(m_diskSwitchLock);
        m_fileWriteInProgress = true;

        // 如果有延迟切换待处理，记录日志但不阻塞
        if (m_diskSwitchDeferred) {
            static int log_count = 0;
            if (++log_count % 100 == 0) { // 每100帧打印一次，避免日志刷屏
                LogI("延迟磁盘切换待处理，继续录像到当前磁盘: chn=%d", chn);
            }
        }
    }
    // 检查I帧并处理等待I帧的情况
    if (check_next_frame) {
        time_t wait_time = now - iframe_wait_start_time;
        
        if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
            // 找到 I 帧，执行文件切换
//            LogI("检测到I帧，执行文件切换: chn=%d, 等待时间=%ld秒", chn, wait_time);
            closeCurrentFile(real_end_time ? real_end_time : now);
            
            // 重置检查标志
            check_next_frame = false;
            
            // 创建新文件并写入当前 I 帧
            if (!createNewFile(now)) {
                LogE("创建新文件失败");
                return false;
            }
            
            // 直接写入当前 I 帧数据到新文件
            uint64_t offset = lseek(video_fd, 0, SEEK_CUR);
            uint32_t frame_size = frame_hdr->size;
            
            // 写录像帧
            if (video_fd > 0) {
                ssize_t n1 = write(video_fd, frame_hdr, sizeof(T_FRAME_HEADER));
                ssize_t n2 = write(video_fd, buffer, frame_size);
                if (n1 != sizeof(T_FRAME_HEADER) || n2 != frame_size) {
                    LogE("写录像文件失败: n1=%d, n2=%d, errno=%d", n1, n2, errno);
                    return false;
                }
            }
            
            // 写入索引
            if (video_fd > 0 && idx_fd > 0) {
                T_INDEX_ENTRY idx_entry;
                memset(&idx_entry, 0, sizeof(idx_entry));
                
                // 使用当前本地时间
                time_t local_ts = get_now();
                idx_entry.utc_time = local_ts;  // 直接使用当前本地时间
                idx_entry.time_stamp = frame_hdr->timeStamp;
                idx_entry.file_offset = offset;
                idx_entry.frame_size = frame_size;
                idx_entry.frame_type = frame_hdr->frameType;
                idx_entry.event_type = cur_event_type;
                
                ssize_t n = write(idx_fd, &idx_entry, sizeof(idx_entry));
                if (n != sizeof(idx_entry)) {
                    LogE("写索引文件失败: n=%d, errno=%d", n, errno);
                    return false;
                }
                
                // I 帧 同步磁盘 更新结束时间
                if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
                    updateRecordEnd(last_video_name, local_ts);  // 使用当前本地时间
                    fdatasync(idx_fd);
                    fdatasync(video_fd);
                }
            }
            
            // 重置等待I帧标志，因为我们已经写入了I帧
            wait_for_iframe = false;
            
            // 已经处理完当前帧，返回
            return true;
        }
        else if (wait_time >= 10) {
            // 如果等待超过10秒，强制切换文件
            LogW("等待I帧超时(10秒)，强制切换文件: chn=%d", chn);
            closeCurrentFile(real_end_time ? real_end_time : now);
            
            // 重置检查标志
            check_next_frame = false;
            
            // 创建新文件
            if (!createNewFile(now)) {
                LogE("创建新文件失败");
                return false;
            }
            
            // 检查当前帧是否是I帧
            if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
                // 当前帧是I帧，立即写入
                LogI("强制切换文件后，当前帧是I帧，立即写入: chn=%d", chn);
                wait_for_iframe = false;
                
                // 写入I帧数据到新文件
                uint64_t offset = lseek(video_fd, 0, SEEK_CUR);
                uint32_t frame_size = frame_hdr->size;
                
                // 写录像帧
                if (video_fd > 0) {
                    ssize_t n1 = write(video_fd, frame_hdr, sizeof(T_FRAME_HEADER));
                    ssize_t n2 = write(video_fd, buffer, frame_size);
                    if (n1 != sizeof(T_FRAME_HEADER) || n2 != frame_size) {
                        LogE("写录像文件失败: n1=%d, n2=%d, errno=%d", n1, n2, errno);
                        return false;
                    }
                }
                
                // 写入索引
                if (video_fd > 0 && idx_fd > 0) {
                    T_INDEX_ENTRY idx_entry;
                    memset(&idx_entry, 0, sizeof(idx_entry));
                    
                    // 使用当前本地时间而不是UTC时间
                    time_t local_ts = get_now();
                    idx_entry.utc_time = local_ts;  // 直接使用当前时间，已经是本地时间
                    idx_entry.time_stamp = frame_hdr->timeStamp;
                    idx_entry.file_offset = offset;
                    idx_entry.frame_size = frame_size;
                    idx_entry.frame_type = frame_hdr->frameType;
                    idx_entry.event_type = cur_event_type;
                    
                    ssize_t n = write(idx_fd, &idx_entry, sizeof(idx_entry));
                    if (n != sizeof(idx_entry)) {
                        LogE("写索引文件失败: n=%d, errno=%d", n, errno);
                        return false;
                    }
                    
                    // I 帧 同步磁盘 更新结束时间
	                if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
	                    updateRecordEnd(last_video_name, local_ts);  // 使用当前本地时间
	                    fdatasync(idx_fd);
	                    fdatasync(video_fd);
	                }
                }
                
                return true;
            } 
			else {
                // 当前帧不是I帧，等待下一个I帧
                // LogW("等待I帧作为新文件的第一帧: chn=%d", chn);
                return true;
            }
        }
        else {
            // 仍在等待I帧，继续写入当前文件
            // LogI("等待I帧中: chn=%d, 已等待=%ld秒", chn, wait_time);
            ;
        }
    }
    
    // 如果需要创建新文件（关闭旧文件后或初始状态）
    if (video_fd < 0) {
        return createNewFile(now);
    }
    
    // 🔥 关键修复：等待I帧期间继续写入当前文件，避免数据丢失
    if (wait_for_iframe) {
        if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
            wait_for_iframe = false;
            // I帧到达，可以开始新文件，但先写入当前帧到旧文件
        } else {
            // 非I帧期间继续写入当前文件，不跳过数据
            // 这样确保录像连续性，没有帧丢失
        }
    }
    
    // 写入帧数据
    uint64_t offset = lseek(video_fd, 0, SEEK_CUR);
    uint32_t frame_size = frame_hdr->size;
    
    // 写录像帧
    if (video_fd > 0) {
        ssize_t n1 = write(video_fd, frame_hdr, sizeof(T_FRAME_HEADER));
        ssize_t n2 = write(video_fd, buffer, frame_size);
        if (n1 != sizeof(T_FRAME_HEADER) || n2 != frame_size) {
            LogE("写录像文件失败: n1=%d, n2=%d, errno=%d", n1, n2, errno);
            return false;
        }
    }
    
    // 写入索引
    if (video_fd > 0 && idx_fd > 0) {
        T_INDEX_ENTRY idx_entry;
        memset(&idx_entry, 0, sizeof(idx_entry));
        
        // 使用当前本地时间而不是UTC时间
        time_t local_ts = get_now();
        idx_entry.utc_time = local_ts;  // 直接使用当前时间，已经是本地时间
        idx_entry.time_stamp = frame_hdr->timeStamp;
        idx_entry.file_offset = offset;
        idx_entry.frame_size = frame_size;
        idx_entry.frame_type = frame_hdr->frameType;
        idx_entry.event_type = cur_event_type; // 使用当前事件类型
        
        ssize_t n = write(idx_fd, &idx_entry, sizeof(idx_entry));
        if (n != sizeof(idx_entry)) {
            LogE("写索引文件失败: n=%d, errno=%d", n, errno);
            return false;
        }
        
        // I 帧 同步磁盘 更新结束时间
        if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
            // 普通I帧处理
            updateRecordEnd(last_video_name, local_ts);  // 使用传入的当前时间
            fdatasync(idx_fd);
            fdatasync(video_fd);
        }
    }
    
    // 每处理10个I帧检查一次磁盘状态
    static int frame_count = 0;
    if (frame_hdr->frameType == IPC_FRAME_FLAG_IFRAME) {
        frame_count++;
        if (frame_count >= 10) {
            frame_count = 0;
            
            // 检查磁盘是否需要切换
            DiskManager* diskManager = DiskManager::getInstance();
            if (diskManager) {

                std::string currentPath = diskManager->getActiveDiskPath();
                if (diskManager->isDiskFull(currentPath)) {
                    LogW("录像处理帧时发现磁盘已满: %s", currentPath.c_str());
                }
            }
        }
    }

    // 重置文件写入进行中标志
    {
        ScopedLocker lock(m_diskSwitchLock);
        m_fileWriteInProgress = false;
    }

    return true;
}

void RecordChannel::updateRecordEnd(const std::string& video_name, time_t end_time) 
{
    CHAR path[200];
    
    // 提取文件路径
    extract_file_path(video_name.c_str(), path);
    
    // 修改为新的索引文件格式: DISK_MAPPING路径/YYYYMMDD/deviceID/index.db
    strcat(path, "index.db");
    
    // 确保索引文件目录存在
    mkdir_recursive(path);
    
    mfile_new_dbfile(path);

    FILE *pf = fopen(path, "r+b");
    if (pf != NULL) {
        T_ONE_REC one_rec;
        while (fread(&one_rec, sizeof(T_ONE_REC), 1, pf) == 1) {

			// && one_rec.flag == OR_FLAG_REC
            if (strncmp(one_rec.file_name, video_name.c_str(), sizeof(one_rec.file_name)) == 0) {
                // 找到正在录制的记录，更新
                one_rec.end_time = end_time;
                one_rec.flag = OR_FLAG_OK;
                fseek(pf, -((long)sizeof(T_ONE_REC)), SEEK_CUR);
                fwrite(&one_rec, sizeof(T_ONE_REC), 1, pf);
                // LogE("找到了记录 并更新成功!");
                break;
            }
        }
    }

	CloseFileAndNull(pf);
}

void RecordChannel::setPreRecordParams(bool enabled, int duration) 
{
    pre_record_enabled = enabled;
    pre_record_duration = duration;
    
    LogW("设置预录像参数: chn=%d, enabled=%d, duration=%d秒", 
         chn, enabled, duration);
}

bool RecordChannel::isPreRecordEnabled() const {
    return pre_record_enabled;
}

int RecordChannel::getPreRecordDuration() const {
    return pre_record_duration;
}

// 是否启用全天录像
void RecordChannel::setFullTimeRecording(bool enabled) 
{
    ScopedLocker lock_guard(lock);
    
    // 检查是否需要切换模式
    if (is_full_time_recording != enabled) {        
        // 更新录像模式
        is_full_time_recording = enabled;
    }
    
    cond.NotifyOne(); // 唤醒线程
}

void RecordChannel::setEventOnlyMode(bool enabled) 
{
    is_event_only_mode = enabled;
}

void RecordChannel::event_start(int event_type, int duration) 
{
    ScopedLocker lock_guard(lock);
    time_t now = get_now(NULL);
    LogI("开始事件录像: chn=%d, event_type=%d, duration=%d, 全天录像=%d", 
         chn, event_type, duration, is_full_time_recording);
    
    // 设置当前事件类型，确保索引中正确标记事件类型
    // 注意：在全天录像模式下，这只会改变标记，不会创建新文件
    setEventType(event_type);

    // 确保持续时间至少为默认值
    if (duration <= 0) {
        duration = DEFAULT_EVENT_DURATION;
    }

    // 在事件标记列表中查找相同类型的事件
    bool found = false;
    for (auto& mark : event_marks) {
        if (mark.event_type == event_type && now < mark.end_time) {
            // 找到相同类型的事件，检查是否需要延长
            int remain = mark.end_time - now;
            
            // 事件录像文件的最大结束时间
            time_t event_file_max_end_time;
            if (is_full_time_recording) {
                // 全天录像模式下，文件的最大结束时间由ALL_REC_FILE_TIME决定
                event_file_max_end_time = file_start_time + ALL_REC_FILE_TIME;
            } else {
                // 仅事件录像模式下，文件的最大结束时间由EVENT_REC_FILE_TIME决定
                event_file_max_end_time = file_start_time + EVENT_REC_FILE_TIME;
            }
            
            // 如果剩余时间小于10秒（EVENT_REC_ADD_TIME），延长事件时间
            if (remain < EVENT_REC_ADD_TIME) {
                time_t new_end = mark.end_time + EVENT_REC_ADD_TIME;
                
                // 确保不超过文件最大结束时间
                if (new_end > event_file_max_end_time) {
                    mark.end_time = event_file_max_end_time;
                } else {
                    mark.end_time = new_end;
                }
                
                // 在非全天录像模式下，还需要更新文件结束时间
                if (!is_full_time_recording) {
                    file_end_time = mark.end_time;
                }
                
                LogI("事件续期: chn=%d, type=%d, remain=%d, 新end=%ld", 
                     chn, event_type, remain, mark.end_time);
            }
            found = true;
            break;
        }
    }
    
    // 如果没有找到相同类型的事件，添加新事件
    if (!found) {
        // 新事件
        event_marks.push_back({event_type, now, now + duration});
        
        // 在非全天录像模式下，更新文件结束时间
        if (!is_full_time_recording) {
            file_end_time = now + duration;
        }
        
        LogI("新事件: chn=%d, type=%d, start=%ld, end=%ld", 
             chn, event_type, now, now + duration);
    }

    // 发出信号通知录像线程，有新的事件录像请求
    cond.NotifyOne();
    
    // ScopedLocker会在作用域结束时自动释放锁
    
    // 计算实际显示的结束时间，避免日志中显示累加后的值
    time_t display_end_time;
    if (found) {
        // 如果是已存在的事件，找到对应的事件记录
        for (const auto& mark : event_marks) {
            if (mark.event_type == event_type && now < mark.end_time) {
                display_end_time = mark.end_time;
                break;
            }
        }
    } else {
        // 如果是新事件，直接使用当前时间+持续时间
        display_end_time = now + duration;
    }
    
    LogI("事件录像打标记完成: chn=%d, type=%d, end=%ld", 
         chn, event_type, display_end_time);
}

// 检查是否有活跃事件
bool RecordChannel::hasActiveEvent(time_t now) const 
{
    for (const auto& mark : event_marks) {
        if (now < mark.end_time) {
            return true;
        }
    }
    return false;
}

// 获取当前活跃事件类型，如果没有活跃事件则返回0(RT_NORMAL)
uint8_t RecordChannel::getActiveEventType(time_t now) const 
{
    for (const auto& mark : event_marks) {
        if (now < mark.end_time) {
            return mark.event_type;
        }
    }
    return RT_NORMAL;  // 没有活跃事件，返回普通录像类型
}

bool RecordChannel::startNonBlocking()
{
    // 使用TryLock避免阻塞
    if (!m_nonBlockingLock.TryLock()) {
        LogW("启动录像时锁被占用，操作被跳过: chn=%d", chn);
        return false;
    }

    bool result = false;

    try {
        if (!running) {
            // 直接执行启动逻辑，避免调用阻塞的start()
            if (thread_id != 0) {
                thread_id = 0;
            }

            // 重置事件等待计数器
            event_wait_count = 0;

            // 创建录像线程
            running = true;

            int ret = pthread_create(&thread_id, NULL, recordThreadFunc, this);
            if (ret != 0) {
                LogE("创建录像线程失败: chn = %d, ret = %d, errno = %d", chn, ret, errno);
                running = false;
                thread_id = 0;
                result = false;
            } else {
                m_recordingState = true;
                result = true;
            }
        } 
		else {
            LogE("录像已在进行中: chn=%d", chn);
            result = true;
        }
    } 
	catch (const std::exception& e) {
        LogE("启动录像异常: chn=%d, error=%s", chn, e.what());
        result = false;
    }

    m_nonBlockingLock.Leave();
	
    return result;
}

// 非阻塞停止录像
bool RecordChannel::stopNonBlocking()
{
    // 使用TryLock避免阻塞
    if (!m_nonBlockingLock.TryLock()) {
        LogW("停止录像时锁被占用，操作被跳过: chn=%d", chn);
        return false;
    }

    bool result = false;

    try {
        if (running) {
            // 直接设置停止标志，不等待线程结束（非阻塞）
            running = false;
            m_recordingState = false;
            // 注意：线程会自然结束，thread_id在线程结束时会被清理
            result = true;
        } else {
            result = true;
        }
    } catch (const std::exception& e) {
        LogE("停止录像异常: chn=%d, error=%s", chn, e.what());
        result = false;
    }

    m_nonBlockingLock.Leave();
    return result;
}

// 非阻塞开始事件录像
void RecordChannel::startEventNonBlocking(uint8_t eventType, int duration)
{
    // 使用TryLock避免阻塞
    if (!m_eventLock.TryLock()) {
        LogW("开始事件录像时锁被占用，操作被跳过: chn=%d, type=%d", chn, eventType);
        return;
    }

    try {
        time_t now = time(nullptr);
        SimpleEventInfo& event = m_activeEvents[eventType];

        if (event.active) {
            // 延长现有事件
            event.endTime = now + duration;
            LogI("延长事件录像: chn=%d, type=%d", chn, eventType);
        } else {
            // 创建新事件
            event.type = eventType;
            event.startTime = now;
            event.endTime = now + duration;
            event.active = true;
            LogI("创建新事件录像: chn=%d, type=%d", chn, eventType);
        }

        // 调用传统事件接口（内部实现，保持原有调用）
        event_start(eventType, duration);

    } catch (const std::exception& e) {
        LogE("开始事件录像异常: chn=%d, error=%s", chn, e.what());
    }

    m_eventLock.Leave();
}

// 非阻塞停止事件录像
void RecordChannel::stopEventNonBlocking(uint8_t eventType)
{
    // 使用TryLock避免阻塞
    if (!m_eventLock.TryLock()) {
        LogW("停止事件录像时锁被占用，操作被跳过: chn=%d, type=%d", chn, eventType);
        return;
    }

    try {
        auto it = m_activeEvents.find(eventType);
        if (it != m_activeEvents.end()) {
            it->second.active = false;
            LogI("事件录像已停止: chn=%d, type=%d", chn, eventType);
        }
    } catch (const std::exception& e) {
        LogE("停止事件录像异常: chn=%d, error=%s", chn, e.what());
    }

    m_eventLock.Leave();
}

// 检查是否正在录像（非阻塞）
bool RecordChannel::isRecordingNonBlocking() const
{
    return m_recordingState.load();
}

// 检查是否有活跃事件（非阻塞）
bool RecordChannel::hasActiveEventNonBlocking(uint8_t eventType) const
{
    // 使用TryLock避免阻塞
    if (!m_eventLock.TryLock()) {
        LogW("检查活跃事件时锁被占用: chn=%d, type=%d", chn, eventType);
        return false;
    }

    bool result = false;
    auto it = m_activeEvents.find(eventType);
    if (it != m_activeEvents.end()) {
        result = it->second.active;
    }

    m_eventLock.Leave();
    return result;
}

void RecordChannel::generateDateDir(char* path, size_t size, time_t now)
{
    struct tm* tm_info = localtime(&now);
    // 使用get_channel_record_path获取通道对应的存储路径
    std::string basePath = get_active_disk_path();

    // 检查是否有可用磁盘
    if (basePath.empty()) {
        LogE("生成日期目录失败: 没有可用的已挂载磁盘");
        path[0] = '\0'; // 返回空字符串
        return;
    }

    snprintf(path, size, "%s%04d%02d%02d",
             basePath.c_str(),
             tm_info->tm_year + 1900,
             tm_info->tm_mon + 1,
             tm_info->tm_mday);
}

// ========== RecordManager 类实现 ==========

RecordManager::RecordManager(int channel_id) : 
    chn(channel_id)
{
    ;
}

bool RecordManager::startRecord(UINT32 streamMask) 
{
	bool s32Ret = FALSE;
	do {
	    // 首先检查是否有可用磁盘
	    DiskManager* diskManager = DiskManager::getInstance();
	    if (diskManager && !diskManager->hasAvailableDisk()) {
	        LogE("启动录像失败: 没有可用的硬盘或硬盘挂载失败");
	        break;
	    }
	    
	    ChannelManager* channelManager = ChannelManager::getInstance();
	    if (!channelManager) {
	        LogE("启动录像失败: 无法获取 ChannelManager 单例实例");
	        break;
	    }

	    Channel* pChannel = channelManager->getChannel(chn);
	    if (!pChannel) {
	        LogE("启动录像失败: 无法获取通道 %d", chn);
	        break;
	    }
	    
	    // 启动编码数据
	    BufferManager* pBufferMgr = pChannel->getBufferManager();
	    if (!pBufferMgr) {
	        LogE("启动录像失败: 无法获取缓冲区管理器: chn=%d", chn);
	        break;
	    }
	    
	    // 遍历所有码流通道
	    for (UINT32 i = 0; i < VS_MAX_STREAM_NUM; i++) {
	        if ((streamMask & (1 << i)) != 0) {
	            // 启动对应的编码数据
	            INT32 ret = pBufferMgr->startVencData(chn, i);
	            if (ret != 0) {
	                LogE("启动录像失败: 无法启动编码数据: chn=%d, streamChn=%u, ret=%d", chn, i, ret);
	                // 停止已启动的码流通道
	                for (UINT32 j = 0; j < i; j++) {
	                    if ((streamMask & (1 << j)) != 0) {
	                        LogI("回滚：停止已启动的码流通道: chn=%d, streamChn=%u", chn, j);
	                        pBufferMgr->stopVencData(j);
	                    }
	                }
	                break;
	            }
	        }
	    }
	    
	    // 获取录像通道
	    RecordChannel* pRecordChannel = pChannel->getRecordChannel();
	    if (!pRecordChannel) {
	        LogE("启动录像失败: 无法获取录像通道: chn=%d", chn);
	        break;
	    }

		// 启动录像
	    if (!pRecordChannel->startNonBlocking()) {
	        LogE("录像通道启动被跳过（锁被占用）: chn=%d", chn);
			break;
	    }

		s32Ret = TRUE;
	}while(FALSE);

    return s32Ret;
}

bool RecordManager::stopRecord() 
{
    bool s32Ret = FALSE;
	do {
	    ChannelManager* channelManager = ChannelManager::getInstance();
	    if (!channelManager) {
	        LogE("停止录像失败: 无法获取 ChannelManager 单例实例");
	       	break;
	    }

	    Channel* pChannel = channelManager->getChannel(chn);
	    if (!pChannel) {
	        LogE("停止录像失败: 通道 %d 不存在", chn);
	        break;
	    }
		
	    // 停止录像通道（非阻塞）
	    RecordChannel* pRecordChannel = pChannel->getRecordChannel();
        if (pRecordChannel && !pRecordChannel->stopNonBlocking()) {
            LogE("录像通道停止被跳过（锁被占用）: chn = %d", chn);
			break;
        }
	    
	    // 停止编码数据
	    BufferManager* pBufferMgr = pChannel->getBufferManager();
		if (!pBufferMgr) {
			LogE("停止录像失败: 缓冲区 %d 不存在", chn);
			break;
		}
		else {
	        // 关闭所有码流通道
	        for (UINT32 i = 0; i < VS_MAX_STREAM_NUM; i++) {
	            pBufferMgr->stopVencData(i);
	        }
		}

		s32Ret = TRUE;
	}while(FALSE);
		
    LogI("停止录像成功: chn = %d, s32Ret = %d", chn, s32Ret);
	
    return true;
}







// 原子计数器（C++11 atomic）
static std::atomic<uint64_t> g_file_counter(0);

// 生成唯一文件名
static void generate_unique_filename(std::string& buf, int device_id) 
{
    // 高精度时间戳（C++11 <chrono>）
    auto now = std::chrono::system_clock::now();
    auto nanos = std::chrono::duration_cast<std::chrono::nanoseconds>(
        now.time_since_epoch()).count();

    uint64_t unique_id = (nanos << 12) | 
                        (g_file_counter.fetch_add(1, std::memory_order_relaxed) & 0xFFF);

    char temp[256];
    T_DATETIME now_time;
    get_now(&now_time);
    std::string basePath = get_active_disk_path();

    // 检查是否有可用磁盘
    if (basePath.empty()) {
        LogE("生成唯一文件名失败: 没有可用的已挂载磁盘");
        buf.clear();
        return;
    }

    snprintf(temp, sizeof(temp), "%sevent_pic/%d/%04d%02d%02d/%llu.jpg",
             basePath.c_str(), device_id, now_time.usYear,now_time.usMonth,now_time.usDay,
             static_cast<unsigned long long>(unique_id));
    buf.assign(temp);
}

int create_directories_if_not_exist(const char *filepath) 
{
    char path[512];
    size_t len = strlen(filepath);
    
    if (len >= sizeof(path)) {
        LogE("Path too long");
        return -1;
    }

    // 复制路径
    strncpy(path, filepath, sizeof(path));
    path[sizeof(path) - 1] = '\0'; // 确保结尾为 '\0'

    // 去掉文件名部分，只保留目录路径
    char *last_slash = strrchr(path, '/');
    if (!last_slash) {
        LogE("Invalid path");
        return -1;
    }
    *last_slash = '\0';

    char temp_path[512];
    temp_path[0] = '\0';

    // 逐层创建目录
    char *p = path;
    if (*p == '/') {
        strcat(temp_path, "/");
        p++;
    }

    while (*p) {
        char *slash = strchr(p, '/');
        size_t len_part = slash ? (size_t)(slash - p) : strlen(p);

        if (strlen(temp_path) + len_part + 1 >= sizeof(temp_path)) {
            LogE("Path too long during iteration");
            return -1;
        }

        strncat(temp_path, p, len_part);
        temp_path[strlen(temp_path)] = '\0';

        // 尝试创建目录，若目录已存在 errno == EEXIST，不报错
        if (mkdir(temp_path, 0755) < 0) 
        {
            if (errno != EEXIST) {
                perror("mkdir");
                LogE("mkdir %s error",temp_path);
                return -1;
            }
        }
        else
        {
            LogI("mkdir:%s ok",temp_path);
        }

        if (slash)
            strcat(temp_path, "/");
        else
            break;
        p = slash ? slash + 1 : NULL;
    }

    return 0;
}


// 主功能函数
bool record_mgr_save_jpg(std::string& file_name, int device_id, const char* buff, int buff_size) 
{
    // 检查是否正在格式化磁盘，如果是则拒绝写入
    if (g_disk_formatting.load(std::memory_order_acquire)) {
        LogW("磁盘正在格式化中，拒绝保存图片");
        return false;
    }

	// 首先检查是否有可用磁盘
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager && !diskManager->hasAvailableDisk()) {
        // LogE("启动录像失败: 没有可用的硬盘或硬盘挂载失败");
        return false;
    }

    if(!buff || buff_size<= 0)
    {
        LogE("param error ");
        return false;
    }
    std::string file_name_tmp;

    // 生成文件路径
    while(!system_quit())
    {
        // 再次检查格式化状态（防止在循环过程中开始格式化）
        if (g_disk_formatting.load(std::memory_order_acquire)) {
            LogW("磁盘开始格式化，取消保存图片");
            return false;
        }

        generate_unique_filename(file_name_tmp, device_id);
        if(file_exist(file_name_tmp.c_str()) )
        {
            LogW(" file exist:%s",file_name_tmp.c_str());
            continue;
        }
        break;
    }

    // LogI("file name tmp : %s", file_name_tmp.c_str());
    
    // 再次检查格式化状态
    if (g_disk_formatting.load(std::memory_order_acquire)) {
        LogW("磁盘开始格式化，取消保存图片");
        return false;
    }
    
    create_directories_if_not_exist(file_name_tmp.c_str());

    // 写入文件前最后检查一次
    if (g_disk_formatting.load(std::memory_order_acquire)) {
        LogW("磁盘开始格式化，取消保存图片");
        return false;
    }

    // 写入文件
    int fd = open(file_name_tmp.c_str(), O_WRONLY | O_CREAT | O_TRUNC | O_DSYNC, 0644);
    if (fd == -1) {
        int err = errno;
        LogE("open %s error: %d (%s)", file_name_tmp.c_str(), err, strerror(err));
        return false;
    }

    bool success = write(fd, buff, buff_size) == static_cast<ssize_t>(buff_size);
    close(fd);

    if (!success) {
        unlink(file_name_tmp.c_str());
        return false;
    }
    file_name = std::move(file_name_tmp);

    return success;
}













// ========== API 函数实现 ==========


// 开始事件录像
VOID start_event(INT32 chn, UINT8 event_type, INT32 dur_time)
{
    LogI("=== start_event: 开始事件录像 ===");
    LogI("start_event参数: chn=%d, event_type=%d, dur_time=%d", chn, event_type, dur_time);

    // 参数校验
    if (chn < 0 || event_type == 0 || dur_time <= 0) {
        LogE("start_event参数非法: chn=%d, event_type=%d, dur_time=%d", chn, event_type, dur_time);
        return;
    }

    // 获取通道对象
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("start_event失败: 无法获取 ChannelManager 单例实例");
        return;
    }

    Channel* pChannel = channelManager->getChannel(chn);
    if (!pChannel) {
        LogE("start_event失败: 无法获取通道 chn=%d", chn);
        return;
    }
    LogI("start_event: 成功获取通道对象: chn=%d", chn);

    // 获取录像通道对象
    RecordChannel* pRecordChannel = pChannel->getRecordChannel();
    if (!pRecordChannel) {
        LogE("start_event失败: 无法获取RecordChannel: chn=%d", chn);
        return;
    }
    LogI("start_event: 成功获取RecordChannel: chn=%d", chn);

    // 检查录像线程状态（可选）
    if (!pRecordChannel->isRunning()) {
        LogW("start_event: 录像线程未运行，自动启动: chn=%d", chn);
        if (!pRecordChannel->startNonBlocking()) {
            LogW("start_event: 自动启动录像被跳过（锁被占用）: chn=%d", chn);
        }
    }

    // 调用非阻塞事件录像实现
    pRecordChannel->startEventNonBlocking(event_type, dur_time);
    LogI("start_event: 非阻塞事件录像调用完成: chn=%d, event_type=%d, dur_time=%d", chn, event_type, dur_time);
    LogI("=== start_event: 结束 ===");
}

// 事件录像停止
VOID stop_event(INT32 chn, UINT8 event_type)
{
	ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("启动录像通道失败: 无法获取 ChannelManager 单例实例");
        LogI("=== start_record_channel API 结束（失败） ===");
        return;
    }
	
    Channel* pChannel = channelManager->getChannel(chn);
    if (!pChannel) {
        LogE("停止事件录像失败: 无法获取通道 %d", chn);
        return;
    }

    RecordChannel* pRecordChannel = pChannel->getRecordChannel();
    if (!pRecordChannel) {
        LogE("停止事件录像失败: 无法获取录像通道: chn=%d", chn);
        return;
    }
    
    // 使用新的事件录像管理逻辑
    if (event_type != 0) {
        // 停止事件录像
        RecordEventManager::getInstance()->stopEventRecord(chn, event_type);
    }
    
    // 清除录像通道的事件类型，确保 cur_event_type 被正确清除
    pRecordChannel->setEventType(0);
    
    LogW("停止事件录像: chn = %d", chn);
}


// 开启录像 full_time_recording=TRUE 表示 全天录像 FALSE 表示 事件录像
VOID start_record_channel(INT32 chn, BOOL full_time_recording)
{
   	INT32 s32Ret = FALSE;

	do {
		// 优化：智能硬盘可用性检查
		DiskManager* diskManager = DiskManager::getInstance();
		if (diskManager) {
		    // 首次检查使用缓存，如果失败则强制检查一次
		    if (!diskManager->hasAvailableDisk(false)) {
		        LogW("初次磁盘检查失败，进行强制检查...");
		        if (!diskManager->hasAvailableDisk(true)) {
		            LogE("启动录像通道失败: 没有可用的硬盘或硬盘挂载失败");
		            break;
		        }
		    }
		} 
		else {
		    LogE("启动录像通道失败: 无法获取DiskManager实例");
		    break;
		}
		
	    ChannelManager* channelManager = ChannelManager::getInstance();
	    if (!channelManager) {
	        LogE("启动录像通道失败: 无法获取 ChannelManager 单例实例");
	        break;
	    }

	    Channel* pChannel = channelManager->getChannel(chn);
	    if (!pChannel) {
	        LogE("启动录像通道失败: 无法获取通道 %d", chn);
	        break;
	    }
		
	    // 根据参数设置录像模式
	    RecordChannel* pRecordChannel = pChannel->getRecordChannel();
	    if (pRecordChannel) {
	        pRecordChannel->setFullTimeRecording(full_time_recording);
	        
	        // 如果不是全天录像，则设置为事件录像模式
	        if (!full_time_recording) {
	            pRecordChannel->setEventOnlyMode(TRUE);
	        } else {
	            pRecordChannel->setEventOnlyMode(FALSE);
	        }
	    } 
		else {
	        LogE("无法获取录像通道: chn=%d", chn);
			break;
	    }
	    
	    // 获取录像管理器
	    RecordManager* pRecordMgr = pChannel->getRecordManager();
	    if (!pRecordMgr) {
	        LogE("启动录像通道失败: 无法获取录像管理器: chn=%d", chn);
	        break;
	    }

	    // 启动录像
	    bool result = pRecordMgr->startRecord(0x01); // 只启动主码流
	    
	    if (!result) {
	        LogE("外部录像启动失败: chn=%d", chn);
	    }

		s32Ret = TRUE;
	}while(FALSE);

	LogW("开始录像:: chn = %d, s32Ret = %d", chn, s32Ret);
}


// 关闭录像
VOID stop_record_channel(INT32 chn)
{
   	INT32 s32Ret = FALSE;

	do {
	    ChannelManager* channelManager = ChannelManager::getInstance();
	    if (!channelManager) {
	        LogE("停止录像通道失败: 无法获取 ChannelManager 单例实例");
	        break;
	    }

	    Channel* pChannel = channelManager->getChannel(chn);
	    if (!pChannel) {
	        LogE("停止录像通道失败: 无法获取通道 %d", chn);
	        break;
	    }
	    
	    RecordManager* pRecordMgr = pChannel->getRecordManager();
	    if (!pRecordMgr) {
	        LogE("停止录像通道失败: 无法获取录像管理器: chn=%d", chn);
	        break;
	    }
	    
	    if (!pRecordMgr->stopRecord()){
	        LogE("外部录像停止失败: chn=%d", chn);
			break;
	    }

		s32Ret = TRUE;
	}while(FALSE);
    
    LogE("停止录像:: chn = %d, s32Ret = %d", chn, s32Ret);
}

// 新增：关闭所有通道的录像函数，用于在格式化磁盘前调用
VOID stop_all_record_channels()
{
    LogW("关闭全部缓冲--开始");

    // 获取所有存在的通道
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("无法获取 ChannelManager 单例实例");
        return;
    }

    std::vector<INT32> channels = channelManager->getExistingChannels();
    LogI("关闭全部缓冲--发现 %d 个通道", (int)channels.size());

    if (channels.empty()) {
        LogI("没有活动通道，无需关闭录像");
        return;
    }

    // 第一阶段：快速设置停止标志（非阻塞）
    int stopped_channels = 0;
    int failed_channels = 0;
	
    for (size_t i = 0; i < channels.size(); i++) {
        INT32 chn = channels[i];

        Channel* pChannel = channelManager->getChannel(chn);
        if (!pChannel) {
            failed_channels++;
            continue;
        }

        // 使用非阻塞方式快速停止录像通道
        RecordChannel* pRecordChannel = pChannel->getRecordChannel();
        if (pRecordChannel) {
            if (pRecordChannel->stopNonBlocking()) {
                stopped_channels++;
            }
        }

        // 停止事件录像（快速操作）
        RecordEventManager::getInstance()->stopEventRecord(chn, 1);
    }

    // 第二阶段：关闭缓冲区（批量处理）
    int closed_buffers = 0;
    for (size_t i = 0; i < channels.size(); i++) {
        INT32 chn = channels[i];

        Channel* pChannel = channelManager->getChannel(chn);
        if (pChannel) {
            pChannel->closeAllBuffers();
            closed_buffers++;
        }
    }

    LogI("关闭全部缓冲--结束  %d 已关闭", closed_buffers);

    sync();
}

// 新增：开启所有通道的录像函数，用于在格式化磁盘后调用
VOID start_all_record_channels()
{
    // 检查是否有可用的已挂载磁盘（格式化后可能需要刷新状态）
    bool hasAvailableDisk = false;

    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager) {
        // 首次检查
        hasAvailableDisk = diskManager->hasAvailableDisk();

        if (!hasAvailableDisk) {

            // 触发磁盘状态更新（格式化后磁盘状态可能需要刷新）
            diskManager->updateAllDiskStatus();

            // 等待短暂时间让状态更新完成
            usleep(500000);  // 等待500ms

            // 重新检查
            hasAvailableDisk = diskManager->hasAvailableDisk();

            if (!hasAvailableDisk) {
                sleep(1);  // 再等待1秒
                hasAvailableDisk = diskManager->hasAvailableDisk();
            }
        }
    }

    if (!hasAvailableDisk) {
        
        // 添加详细的调试信息
        DiskManager* diskManager = DiskManager::getInstance();
        if (diskManager) {
            std::string activePath = diskManager->getActiveDiskPath();

            // 手动检查DISK_MAPPING中的磁盘
            MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
            for (int i = 0; i < MAX_DISK_COUNT; i++) {
                if (disks[i].mountpoint && *disks[i].mountpoint) {
                    bool mounted = diskManager->isMounted(std::string(disks[i].mountpoint));
                    LogE("调试信息: 磁盘 %s -> %s, 挂载状态: %s", disks[i].device, disks[i].mountpoint, mounted ? "已挂载" : "未挂载");
                }
            }
        }

		LogE("启动所有录像通道失败: 没有可用的硬盘或硬盘挂载失败");
        return;
    }

    // 获取所有存在的通道
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("无法获取 ChannelManager 单例实例");
        return;
    }

    std::vector<INT32> channels = channelManager->getExistingChannels();
    
    // 启动每个通道的录像
    for (size_t i = 0; i < channels.size(); i++) {
        INT32 chn = channels[i];
        LogI("正在启动通道 %d 的录像 (%d/%d)...", chn, (int)i+1, (int)channels.size());
        
        // 获取通道对象
        Channel* pChannel = channelManager->getChannel(chn);
        if (!pChannel) {
            LogE("无法获取通道 %d，跳过", chn);
            continue;
        }
        
        // 启动通道录像，默认使用全天录像模式
        start_record_channel(chn, TRUE);
    }
    
}


// 获取当前工作的磁盘路径
std::string get_active_disk_path()
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager) {
        std::string path = diskManager->getActiveDiskPath();
        // 如果没有挂载磁盘，返回空字符串
        if (path.empty()) {
            return "";
        }
        return path;
    }
    // 磁盘管理器未初始化时，不返回默认路径
    return "";
}


// 修改init_record_manager函数，不再进行磁盘初始化
void init_record_manager()
{
    LogI("======== 录像管理器初始化开始 ========");

    // 检查磁盘管理模块是否已初始化
    if (!disk_module_is_initialized()) {
        LogE("录像管理器初始化失败: 磁盘管理模块未初始化");
        LogE("请确保在主程序启动时调用 disk_module_initialize()");
        return;
    }

    // 检查通道模块是否已初始化
    if (!channel_module_is_initialized()) {
        LogE("录像管理器初始化失败: 通道模块未初始化");
        LogE("请确保在主程序启动时调用 channel_module_initialize()");
        return;
    }

    // 检查回放模块是否已初始化
    if (!playback_module_is_initialized()) {
        LogE("录像管理器初始化失败: 回放模块未初始化");
        LogE("请确保在主程序启动时调用 playback_module_initialize()");
        return;
    }

    // 获取磁盘管理器实例
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager) {
        LogE("录像管理器初始化失败: 无法获取 DiskManager 单例实例");
        return;
    }

    if (!diskManager->isModuleInitialized()) {
        LogE("录像管理器初始化失败: DiskManager 模块未初始化");
        LogE("请确保在主程序启动时调用 disk_module_initialize()");
        return;
    }

    // 验证磁盘状态
    if (!diskManager->hasAvailableDisk()) {
        LogW("录像管理器初始化警告: 当前没有可用的磁盘");
        LogW("录像功能可能无法正常工作，请检查磁盘挂载状态");
    } else {
        std::string activeDisk = diskManager->getActiveDiskPath();
        LogI("当前活动磁盘: %s", activeDisk.c_str());
    }

    // 验证通道状态（按需创建模式下，初始时可能没有通道）
    ChannelManager* channelManager = ChannelManager::getInstance();
    std::vector<INT32> existingChannels = channelManager->getExistingChannels();
    LogI("当前已创建的通道数量: %zu", existingChannels.size());
    LogI("通道将按需创建，支持任意正整数通道ID");

    // 初始化录像事件管理器
    if (!RecordEventManager::getInstance()->init()) {
        LogE("录像事件管理器初始化失败");
        return;
    }

    LogI("录像管理器初始化完成");
    LogI("======== 录像管理器初始化结束 ========");
}



// 在现有的 deinit_record_manager 函数中添加磁盘管理器释放代码
void deinit_record_manager()
{
    // 停止磁盘管理器
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager) {
        diskManager->stopChecking();
        // 单例模式下不需要手动删除实例
    }
}

// 在使用TOP_DIR的函数中，使用get_channel_record_path函数替换
// 修改生成日期目录的函数

// 修改抓拍图片保存的函数
int record_mgr_save_jpg(int channel_id, const char* jpg_data, int jpg_size, const char* event_type)
{
    // 检查磁盘是否正在格式化，如果是则拒绝保存
    if (g_disk_formatting.load(std::memory_order_acquire)) {
        LogW("磁盘正在格式化中，拒绝保存图片");
        return -1;
    }
    
    // 检查是否有可用磁盘
    DiskManager* diskManager = DiskManager::getInstance();
    if (diskManager && !diskManager->hasAvailableDisk()) {
        LogW("没有可用的硬盘或硬盘挂载失败，拒绝保存图片");
        return -1;
    }
    
    // 参数检查
    if (!jpg_data || jpg_size <= 0 || channel_id < 0) {
        LogE("保存JPEG参数错误: 通道=%d, 数据=%p, 大小=%d", 
             channel_id, jpg_data, jpg_size);
        return -1;
    }
    
    // 获取当前时间
    time_t now = time(NULL);
    struct tm *now_tm = localtime(&now);
    
    // 使用get_channel_record_path获取通道对应的存储路径
    std::string basePath = get_active_disk_path();

    // 检查是否有可用磁盘
    if (basePath.empty()) {
        LogE("保存事件图片失败: 没有可用的已挂载磁盘");
        return false;
    }

    // 创建事件图片目录
    char temp[512];
    snprintf(temp, sizeof(temp), "%sevent_pic/%d/%04d%02d%02d",
             basePath.c_str(),
             channel_id,
             now_tm->tm_year + 1900,
             now_tm->tm_mon + 1,
             now_tm->tm_mday);
    
    // 确保目录存在
    if (access(temp, F_OK) != 0) {
        // 逐层创建目录
        char dirPath[512];
        
        // 先创建event_pic目录
        snprintf(dirPath, sizeof(dirPath), "%sevent_pic", basePath.c_str());
        if (access(dirPath, F_OK) != 0) {
            mkdir(dirPath, 0777);
        }
        
        // 创建通道目录
        snprintf(dirPath, sizeof(dirPath), "%sevent_pic/%d", 
                 basePath.c_str(), channel_id);
        if (access(dirPath, F_OK) != 0) {
            mkdir(dirPath, 0777);
        }
        
        // 创建日期目录
        if (mkdir(temp, 0777) != 0) {
            LogE("创建事件图片目录失败: %s, errno=%d", temp, errno);
            return -1;
        }
    }
    
    // 创建文件名，添加时间戳
    snprintf(temp, sizeof(temp), "%sevent_pic/%d/%04d%02d%02d/%ld.jpg",
             basePath.c_str(),
             channel_id,
             now_tm->tm_year + 1900, 
             now_tm->tm_mon + 1, 
             now_tm->tm_mday,
             now);
    
    // 打开文件
    int fd = open(temp, O_WRONLY | O_CREAT, 0644);
    if (fd < 0) {
        LogE("打开文件失败: %s, errno=%d", temp, errno);
        return -1;
    }
    
    // 写入数据
    ssize_t written = write(fd, jpg_data, jpg_size);
    close(fd);
    
    if (written != jpg_size) {
        LogE("写入JPEG数据失败: 写入=%ld, 预期=%d", written, jpg_size);
        unlink(temp); // 删除不完整的文件
        return -1;
    }
    
    LogI("保存JPEG成功: %s, 大小=%d字节", temp, jpg_size);
    return 0;
}

// 时间格式化辅助函数
std::string format_datetime_str(time_t t) 
{
    char buf[64] = {0};
    struct tm *tm_info = localtime(&t);
    if (tm_info) {
        strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", tm_info);
    }
    return std::string(buf);
}

// ============================================================================
// RecordChannel 非阻塞接口实现（解决界面卡顿问题）
// ============================================================================

// 非阻塞启动录像（使用TryLock避免阻塞）

// 🚀 新增：检查指定通道是否正在录像
bool is_record_channel_running(int channelId)
{
    // 获取通道管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("无法获取 ChannelManager 单例实例");
        return false;
    }

    // 检查指定通道是否正在录像
    Channel* channel = channelManager->getChannel(channelId);
    if (channel) {
        RecordChannel* recordChannel = channel->getRecordChannel();
        if (recordChannel) {
            return recordChannel->isFileWriteInProgress();
        }
    }

    return false;
}

// 检查是否有任何录像正在进行
bool is_any_recording_in_progress()
{
    // 获取通道管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogE("无法获取 ChannelManager 单例实例");
        return false;
    }

    // 🚀 架构重构：动态遍历所有活跃通道（支持任意通道ID）
    // 获取所有活跃的通道ID列表
    std::vector<int> activeChannels;

    // 从RecordEventManager获取活跃通道列表
    RecordEventManager* eventManager = RecordEventManager::getInstance();
    if (eventManager && eventManager->isInitialized()) {
        activeChannels = eventManager->getActiveChannels();
    }

    // 如果事件管理器没有活跃通道，尝试从ChannelManager获取
    if (activeChannels.empty()) {
        // 作为备选方案，检查ChannelManager中的所有可能通道
        // 这里我们使用一个合理的范围作为备选检查
        for (int chn = 0; chn < 64; chn++) { // 扩展检查范围到64个通道
            Channel* channel = channelManager->getChannel(chn);
            if (channel) {
                RecordChannel* recordChannel = channel->getRecordChannel();
                if (recordChannel && recordChannel->isFileWriteInProgress()) {
                    LogI("通道 %d 正在进行录像写入", chn);
                    return true;
                }
            }
        }
    } else {
        // 使用活跃通道列表进行检查
        for (int chn : activeChannels) {
            Channel* channel = channelManager->getChannel(chn);
            if (channel) {
                RecordChannel* recordChannel = channel->getRecordChannel();
                if (recordChannel && recordChannel->isFileWriteInProgress()) {
                    LogI("通道 %d 正在进行录像写入", chn);
                    return true;
                }
            }
        }
    }

    return false;
}

// 获取所有活跃录像通道的辅助函数（优化版）
static std::vector<int> getActiveRecordChannels(ChannelManager* channelManager) {
    std::vector<int> activeChannels;

    if (!channelManager) {
        return activeChannels;
    }

    // 直接使用ChannelManager的getActiveChannels方法
    std::vector<INT32> channelManagerActiveChannels = channelManager->getActiveChannels();

    // 转换数据类型（INT32 -> int）
    for (INT32 chn : channelManagerActiveChannels) {
        activeChannels.push_back(static_cast<int>(chn));
    }

    // 如果ChannelManager没有找到活跃通道，尝试从RecordEventManager获取
    if (activeChannels.empty()) {
        LogI("ChannelManager未找到活跃通道，尝试从RecordEventManager获取");

        RecordEventManager* eventManager = RecordEventManager::getInstance();
        if (eventManager && eventManager->isInitialized()) {
            std::vector<int> eventChannels = eventManager->getActiveChannels();

            // 验证这些通道是否真的有录像活动
            for (int chn : eventChannels) {
                Channel* channel = channelManager->getChannel(chn);
                if (channel) {
                    RecordChannel* recordChannel = channel->getRecordChannel();
                    if (recordChannel && recordChannel->isRunning()) {
                        activeChannels.push_back(chn);
                    }
                }
            }
        }
    }

    // 最后的备选方案：检查已存在的通道
    if (activeChannels.empty()) {
        LogI("使用备选方案：检查所有已存在的通道");

        std::vector<INT32> existingChannels = channelManager->getExistingChannels();
        for (INT32 chn : existingChannels) {
            Channel* channel = channelManager->getChannel(chn);
            if (channel) {
                RecordChannel* recordChannel = channel->getRecordChannel();
                if (recordChannel && recordChannel->isRunning()) {
                    activeChannels.push_back(static_cast<int>(chn));
                }
            }
        }
    }

    LogI("找到 %zu 个活跃录像通道", activeChannels.size());
    return activeChannels;
}

// 准备所有通道进行磁盘切换
void prepare_all_channels_for_disk_switch()
{
    LogI("准备所有录像通道进行磁盘切换");

	// 获取通道管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogW("无法获取 ChannelManager 单例实例");
        return;
    }

    // 🚀 架构重构：动态通知所有活跃通道准备磁盘切换（支持任意通道ID）
    std::vector<int> activeChannels = getActiveRecordChannels(channelManager);

    for (int chn : activeChannels) {
        Channel* channel = channelManager->getChannel(chn);
        if (channel) {
            RecordChannel* recordChannel = channel->getRecordChannel();
            if (recordChannel && recordChannel->isRunning()) {
                LogI("通知通道 %d 准备磁盘切换", chn);
                recordChannel->prepareForDiskSwitch();
            }
        }
    }

    LogI("已通知 %zu 个活跃通道准备磁盘切换", activeChannels.size());

    LogI("所有录像通道磁盘切换准备完成");
}

// 等待所有录像完成
bool wait_for_all_recordings_complete(int timeoutSeconds)
{
    LogI("等待所有录像完成，超时时间: %d秒", timeoutSeconds);

    int waitCount = 0;
    int maxWait = timeoutSeconds * 10; // 每100ms检查一次

    while (is_any_recording_in_progress() && waitCount < maxWait) {
        usleep(100000); // 100ms
        waitCount++;

        if (waitCount % 50 == 0) { // 每5秒打印一次状态
            LogI("等待录像完成中... %d/%d秒", waitCount / 10, timeoutSeconds);
        }
    }

    bool completed = !is_any_recording_in_progress();
    if (completed) {
        LogI("所有录像完成，可以安全切换磁盘");
    } else {
        LogW("等待录像完成超时，强制继续磁盘切换");
    }

    return completed;
}

// 重置所有通道的磁盘切换状态
void reset_all_channels_disk_switch_state()
{
    LogI("重置所有录像通道的磁盘切换状态");

    // 获取通道管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogW("无法获取 ChannelManager 单例实例");
        return;
    }

    // 🚀 架构重构：动态重置所有活跃通道的磁盘切换状态（支持任意通道ID）
    std::vector<int> activeChannels = getActiveRecordChannels(channelManager);

    for (int chn : activeChannels) {
        Channel* channel = channelManager->getChannel(chn);
        if (channel) {
            RecordChannel* recordChannel = channel->getRecordChannel();
            if (recordChannel && recordChannel->isRunning()) {
                // LogI("重置通道 %d 的磁盘切换状态", chn);
                recordChannel->resetDiskSwitchState();
            }
        }
    }

    // LogI("已重置 %zu 个活跃通道的磁盘切换状态", activeChannels.size());

}

// 设置所有通道的磁盘切换延迟标志
void set_all_channels_disk_switch_deferred(bool deferred)
{
    // LogI("设置所有录像通道的磁盘切换延迟标志: %s", deferred ? "true" : "false");

    // 获取通道管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogW("无法获取 ChannelManager 单例实例");
        return;
    }

    // 重构：动态设置所有活跃通道的延迟切换标志（支持任意通道ID）
    std::vector<int> activeChannels = getActiveRecordChannels(channelManager);

    for (int chn : activeChannels) {
        Channel* channel = channelManager->getChannel(chn);
        if (channel) {
            RecordChannel* recordChannel = channel->getRecordChannel();
            if (recordChannel && recordChannel->isRunning()) {
                LogI("设置通道 %d 的磁盘切换延迟标志: %s", chn, deferred ? "true" : "false");
                recordChannel->setDiskSwitchDeferred(deferred);
            }
        }
    }

    //LogI("已设置 %zu 个活跃通道的磁盘切换延迟标志", activeChannels.size());

    // LogI("所有录像通道磁盘切换延迟标志设置完成");
}

// 检查并执行所有待处理的磁盘切换
bool check_and_execute_pending_disk_switches()
{
    LogI("检查并执行所有待处理的磁盘切换");

    // 获取通道管理器
    ChannelManager* channelManager = ChannelManager::getInstance();
    if (!channelManager) {
        LogW("无法获取 ChannelManager 单例实例");
        return false;
    }

    bool anyExecuted = false;

    // 🚀 架构重构：动态检查所有活跃通道的待处理切换（支持任意通道ID）
    std::vector<int> activeChannels = getActiveRecordChannels(channelManager);

    for (int chn : activeChannels) {
        Channel* channel = channelManager->getChannel(chn);
        if (channel) {
            RecordChannel* recordChannel = channel->getRecordChannel();
            if (recordChannel && recordChannel->isRunning()) {
                bool executed = recordChannel->checkAndExecutePendingDiskSwitch();
                if (executed) {
                    LogI("通道 %d 执行了待处理的磁盘切换", chn);
                    anyExecuted = true;
                }
            }
        }
    }

    LogI("检查了 %zu 个活跃通道的待处理磁盘切换", activeChannels.size());

    if (anyExecuted) {
        LogI("部分通道执行了待处理的磁盘切换");
    } else {
        LogI("没有待处理的磁盘切换需要执行");
    }

    return anyExecuted;
}
