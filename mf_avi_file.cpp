/***************************************************************************
** 版权所有: 成都云创天下 Copyright (c) 2015-2020  ********************     
** 文件名称: X:\mpp\sample\vsipc\storage\vs_media_file.cpp
** 文件标识: 
** 内容摘要: 
			1.每天0点强制切换文件
			2.
** 当前版本: v1.0
** 作     者: 徐辉
** 完成日期: 2018年3月15日
** 修改记录: 
** 修改记录: 
** 修改日期: 
** 版 本 号: 
** 修 改 人: 
** 修改内容: 
***************************************************************************/

#include <unistd.h>
#include <sys/stat.h>
#include <sys/vfs.h>
#include <sys/types.h>
#include <dirent.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/types.h>
#include <linux/netlink.h>
#include <errno.h>

#if defined(MFILE_PRE_RECORD)
#include <string>
#endif

#include "vs_media_file.h"
#include "avilib.h"
//#include "ipc/vsipc.h"

#if	defined(OBS_ENABLE) && (OBS_MODE == 1)	
	#include "obs/vs_obs.h"
#endif


#ifdef CMCC_HJQ
#include "vs_hjq_module.h"
#endif


extern T_REC_WORK	g_rec_work;
extern INT32		g_pcm_fd;

#define	AV_FILE_EXT			".avi"


#ifdef CN21_ENABLE
	#undef  AV_FILE_EXT
	#define	AV_FILE_EXT		".mp4"
#endif


// avi无效,必须大于10帧视频
#define AVI_INVALIDE(av)	(AVI_video_frames(av) < 10)

/**
 * 写avi头
 *  成功返回1,失败返回0
 */
UINT8 avi_write_header(avi_t *avh)
{

#define CHECK_STREAM_PARAMS() \
	if((g_rec_work.videoWidth EQU 0)\
		|| (g_rec_work.videoHeight EQU 0)\
		|| (g_rec_work.videoKbps EQU 0)\
		|| (g_rec_work.videoFps EQU 0)){\
		LogE("Param Err!!");\
		return 0;\
	}
	

	if (!avh) return 0;

	// vsipc_get_avheader(g_pRunSets->misc_set.rec_stream_chn, &g_rec_work);

	CHECK_STREAM_PARAMS();

	g_rec_work.videoNums = 0;
	g_rec_work.audioPts = 0;
	AVI_set_video(avh, 
			g_rec_work.videoWidth, 
			g_rec_work.videoHeight, 
			(double)g_rec_work.videoFps, 
			g_rec_work.videoCodec EQU QF_CODEC_H265 ? "H265" : "H264");

	if (g_rec_work.audioEnable) {
		INT32 adoFormat = WAVE_FORMAT_UNKNOWN;

		switch (g_rec_work.audioCodec) {
		case QF_CODEC_PCM:
			adoFormat = WAVE_FORMAT_PCM;
			break;

		case QF_CODEC_G711A:
			adoFormat = WAVE_FORMAT_ALAW;
			break;

		case QF_CODEC_G711U:
			adoFormat = WAVE_FORMAT_MULAW;
			break;

		case QF_CODEC_AAC:
			adoFormat = WAVE_FORMAT_AAC;
			break;

		default:
			LOGE("%s(), WAVE_FORMAT_UNKNOWN: %d", __FUNCTION__, g_rec_work.audioCodec);
		}	
		
		if (adoFormat != WAVE_FORMAT_UNKNOWN) {
			AVI_set_audio(avh, 
					g_rec_work.audioChannel, 
					g_rec_work.audioSampleRate, 
					g_rec_work.audioBitsPerSample,
					adoFormat,
					64
					);
		}
	}

	return 1;		
}

/**
 * 时间转换
 */
extern time_t to_timet(STimeDay *p);

/**
 * 插入一条记录
 * @param  one_rec 记录
 * @return         成功为1；失败为0
 */
extern UINT8 rec_db_add(T_ONE_REC *one_rec);

#ifdef CMCC_HJQ
static T_CMCC_DEVICE_PRINT	cmcc_ext;
#endif


/**
 * 打开写文件
 * @param  rec_type  录像类型 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_open(VS_RECORD_TYPE rec_type)
{
	avi_t 		*avih = NULL;
	CHAR 		*p = g_rec_work.one_rec.file_name;
	T_DATETIME	now;

	if (g_rec_work.start) {

		// 已经开始了,并且类型一致,继续工作
		if (rec_type EQU g_rec_work.one_rec.rec_type)
			return TRUE;

		// 如果录像已经开始了, 不论当前是什么录像, 不改变类型(可能是报警录像,但是对于录像来说,只要在录就可以了.)
		if (rec_type EQU RT_NORMAL){
			LogE("已经开始了");
			return TRUE;
		}
		else {
			/**
			 * 如果已经开始了,就把当前文件标记为报警录像,并且在结束时改变成报警录像
			 */
			g_rec_work.one_rec.rec_type = RT_MOTION_DETECT;
			g_rec_work.one_rec.change_st = TRUE;
			return TRUE;
		}
		
		// mfile_close();
	}

	ScopedLocker	lock(g_rec_work.avi_lock);

	// 如果两个线程同时调用, 可能后面这个线程还在排队,前面一个刚才出去
	if (g_rec_work.start){
		LogE("已经开始了");
		return TRUE;
	}
	
	LogI("mfile_wopen() file_name = %s", g_rec_work.one_rec.file_name);
	avih = AVI_open_output_file(g_rec_work.one_rec.file_name);
	
	if (avih EQU NULL){
		LogE("avi NULL!!");
		return 0;
	}
	
	LogI("open file:%s", g_rec_work.one_rec.file_name);
	if (!avi_write_header(avih)) {
		AVI_close(avih);
		remove(g_rec_work.one_rec.file_name);
		LogE("写入头失败!!");
		return 0;
	}	
	
	// 强制产生I帧
	// vsipc_requst_iframe(g_pRunSets->misc_set.rec_stream_chn);

	// 保存录像上下文
	{		
		get_now(&now);
		        time_t local_ts = get_now();
        g_rec_work.one_rec.st_time	= local_ts;	
		g_rec_work.one_rec.end_time = g_rec_work.one_rec.st_time;
		g_rec_work.one_rec.rec_type = rec_type;
		g_rec_work.one_rec.flag = OR_FLAG_REC;
		g_rec_work.wait_key_frame = TRUE;
		g_rec_work.avh = avih;
		g_rec_work.start= TRUE;
	}

	// 发送通知
	if (g_rec_work.event_cb)
		g_rec_work.event_cb(TRUE, g_rec_work.one_rec.file_name);

	LogE("成功返回!!!!");
	
	return TRUE;
}


/**
 * 写视频
 * @return   成功返回1,失败返回0
 */
UINT8 mfile_write_video(BYTE *buff , UINT32 size, UINT8 key_frame, UINT64 timestamp)
{
	ScopedLocker	lock(g_rec_work.avi_lock);

	if (!g_rec_work.start
		|| g_rec_work.avh EQU NULL)
		return 0;

	if (g_rec_work.wait_key_frame) {
 		if (!key_frame) return 1;
		g_rec_work.wait_key_frame = FALSE;
	}	

	g_rec_work.videoNums++;
	
 	if (AVI_write_frame((avi_t*)g_rec_work.avh, (CHAR *)buff, size, key_frame) < 0){
		mfile_close();
		return FALSE;
	}
	else {
		if (key_frame){
			LogI("成功写入:: size = %d, timestamp = %ld", size, timestamp);
		}
	}

	return TRUE;
	
}

/**
 * 写音频
 * @return   成功返回1,失败返回0
 */
UINT8 mfile_write_audio(BYTE *buff , UINT32 size, UINT64 timestamp)
{
	ScopedLocker	lock(g_rec_work.avi_lock);

	if (!g_rec_work.start
		|| g_rec_work.avh EQU NULL
		|| !mfile_tf_ready())
		return 0;

	if (g_rec_work.wait_key_frame) {
		return 1;
	}
	
	if (AVI_write_audio((avi_t*)g_rec_work.avh, (CHAR *)buff, size) < 0){
		mfile_close();
		return FALSE;
	}

	return TRUE;
}

/**
 * 关闭写文件
 */
VOID mfile_close(T_REC_WORK* p_rec_work)
{
	ScopedLocker	lock(g_rec_work.avi_lock);
	avi_t *h = (avi_t *)g_rec_work.avh;

	g_rec_work.avh = NULL;
	g_rec_work.start= FALSE;
	g_rec_work.wait_key_frame = TRUE;
	
	if (h != NULL) {
		UINT8	del_file = AVI_INVALIDE(h);	// 小于10fps的文件没有意义
		AVI_close(h);	

#if defined(MFILE_PRE_RECORD)
		// 修改文件名
		if (g_rec_work.one_rec.change_st) {
			STimeDay	stBeginTime;
			CHAR 		*p = g_rec_work.one_rec.file_name;
			std::string	org_file(p);
				
			LTIME_2_PARSE(g_rec_work.one_rec.st_time, stBeginTime);
			p += sprintf(g_rec_work.one_rec.file_name, "%s/" REC_DIR_FMT "/", 
						REC_PATH, stBeginTime.year, (UINT16)stBeginTime.month, (UINT16)stBeginTime.day);
			sprintf(p, "%02hhu%02hhu%02hhu_%d%s", stBeginTime.hour, stBeginTime.minute, stBeginTime.second, 
				g_rec_work.one_rec.rec_type, AV_FILE_EXT);
			rename(org_file.c_str(), g_rec_work.one_rec.file_name);
		}
#elif  defined(LH_LWJ)
		
		if(strstr(g_rec_work.one_rec.file_name, ".del")){
			STimeDay	stBeginTime;
			CHAR 		*p = g_rec_work.one_rec.file_name;
			std::string	org_file(p);
				
			LTIME_2_PARSE(g_rec_work.one_rec.st_time, stBeginTime);
			p += sprintf(g_rec_work.one_rec.file_name, "%s/" REC_DIR_FMT "/", 
						REC_PATH, stBeginTime.year, (UINT16)stBeginTime.month, (UINT16)stBeginTime.day);
			sprintf(p, "%02hhu%02hhu%02hhu_%d%s", stBeginTime.hour, stBeginTime.minute, stBeginTime.second, 
				g_rec_work.one_rec.rec_type, AV_FILE_EXT);
			rename(org_file.c_str(), g_rec_work.one_rec.file_name);
			
		}
		
#endif
		LogI("close file:%s", g_rec_work.one_rec.file_name);		
		
		T_DATETIME	now;

		get_now(&now);
#ifdef CN21_ENABLE
		{
			LPSTR	src_file = _strdup(g_rec_work.one_rec.file_name);
			LPSTR	p = g_rec_work.one_rec.file_name + strlen(g_rec_work.one_rec.file_name) - 6;
			
			p += sprintf(p, "-%04d%02d%02d%02d%02d%02d", 
					now.usYear, now.usMonth, now.usDay, 
					now.ucHour, now.ucMin, now.ucSec);
			if (g_rec_work.one_rec.rec_type EQU RT_MOTION_DETECT) {
				p += sprintf(p, "_%s", "ALARM");
			}
			p += sprintf(p, "%s", AV_FILE_EXT);
			rename(src_file, g_rec_work.one_rec.file_name);			
			free(src_file);
		}
#endif		
		time_t local_ts = get_now();
        g_rec_work.one_rec.end_time = local_ts;
		g_rec_work.one_rec.flag = OR_FLAG_OK;
		if (!del_file) {
			rec_db_add(&g_rec_work.one_rec);
			sync();
#if	defined(OBS_ENABLE) && (OBS_MODE == 1)	
			if (g_pRunSets->obs_set.enable) {
				CHAR		obs_url[200];
				CHAR		*tmp = obs_url;
				STimeDay	stBeginTime;
				STimeDay	stEndTime;
				
				LTIME_2_PARSE(g_rec_work.one_rec.st_time, stBeginTime);
				LTIME_2_PARSE(g_rec_work.one_rec.end_time, stEndTime);
	
				if (strlen(g_pRunSets->obs_set.prefix) > 0)
					tmp += sprintf(tmp, "/%s", g_pRunSets->obs_set.prefix);
				
				sprintf(tmp, "/%s/%04d%02d%02d/%02d%02d%02d_%02d%02d%02d_%02d%s", 
					g_pEmbedInfo->p2pid,
					stBeginTime.year, stBeginTime.month, stBeginTime.day,
					stBeginTime.hour, stBeginTime.minute, stBeginTime.second,
					stEndTime.hour, stEndTime.minute, stEndTime.second, 
					g_rec_work.one_rec.rec_type,
					AV_FILE_EXT
					);
				_strlwr(tmp);
				obs_push_file(g_rec_work.one_rec.file_name, obs_url, 
					g_rec_work.one_rec.st_time, g_rec_work.one_rec.end_time);
			}
#endif


		} else {
			CHAR tmp[200];

			sprintf(tmp, "%s.del", g_rec_work.one_rec.file_name);
			rename(g_rec_work.one_rec.file_name, tmp);
			LOGE("avifile to small, be remove: %s", tmp);
		}

		// 发送通知
		if (g_rec_work.event_cb)
			g_rec_work.event_cb(del_file?-1:FALSE, g_rec_work.one_rec.file_name);
	}
}


/**
 * 打开文件, 根据stTimeDay定位文件
 * @param  old_h     old_h=NULL, 则分配句柄；old_h!=NULL,则根据定位决定是否关闭再重建；此句柄要返回给下面的三个函数使用
 * @param  stTimeDay 需要定位的时间
 * @return           成功返回非0
 */
T_PLAY_HADLE mfile_play_open(T_PLAY_HADLE h, STimeDay *stTimeDay, UINT8 with_tz)
{
	CHAR	path[200];	
	FILE	*pf;	
	time_t	ltime;
	UINT8	read_next;

#ifdef USE_HTS_PROTOCOL
	extern void time_convert(STimeDay *td, int zone);

	time_convert(stTimeDay, g_pRunSets->time_set.timezone * 60);
#endif	

	// 确认是定位还是打开下一个记录
	if (stTimeDay != NULL) {
		mfile_play_close(h, TRUE);
		h = NULL;	
		read_next = FALSE;
	} else {
		QF_ASSERT(h != NULL);
		stTimeDay = &h->stTimeDay;
		read_next = TRUE;
	}
	QF_ASSERT(stTimeDay != NULL);
	ltime = to_timet(stTimeDay);	

	sprintf(path, "%s/" REC_DIR_FMT "/" REC_DB_INDEX, 
		REC_PATH, stTimeDay->year, stTimeDay->month, stTimeDay->day);
	LogI("open index: %s, ltime:%lld, %04d%02d%02d %02d:%02d:%02d", path,
		ltime, stTimeDay->year, stTimeDay->month, stTimeDay->day, stTimeDay->hour, stTimeDay->minute, stTimeDay->second);
	mfile_new_dbfile(path);
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{		
		T_ONE_REC	one_rec;
		UINT32		fseq = 0;
		avi_t		*avi_file;
		LPSTR		tmp_str;
		INT32		tmp_value;

		// 直接读某个文件
		if (read_next) {
			fseq = h->fseq_day + 1;
			if (fseek(pf, fseq*sizeof(one_rec), SEEK_SET) != 0) {
				LOGE("%s(), fseek error, errno=%d", __FUNCTION__, errno);
				fclose(pf);
				return NULL;
			}
		}
		
		for ( ;fread(&one_rec, sizeof(one_rec), 1, pf) EQU 1; fseq++)
		{		
//			LOGI("%s(), one_rec, begtime:%ld, endtime:%ld, flag:%d, rectype:%d, filename:%s, ", 
//				__FUNCTION__, one_rec.st_time, one_rec.end_time, one_rec.flag, one_rec.rec_type, one_rec.file_name);
			
			// 判断是否正常
			if (one_rec.flag != OR_FLAG_OK) {
				continue;
			}

			UINT8	find_rec = FALSE;
			if (QfLeftRange(ltime, one_rec.st_time, one_rec.end_time)) {
				T_ONE_REC	next_one_rec;
				
				find_rec = TRUE;
				do {
					if (fread(&next_one_rec, sizeof(next_one_rec), 1, pf) EQU 1) {
						if (QfLeftRange(ltime, next_one_rec.st_time, next_one_rec.end_time)) {
							memcpy(&one_rec, &next_one_rec, sizeof(next_one_rec));
							break;
						}
					}
					fseek(pf, -1*(LONG)sizeof(one_rec), SEEK_CUR);
				} while (FALSE);
			}
			
			// 找到合适的记录了, 或是找到大于此记录的文件
			if (find_rec
				|| one_rec.st_time > ltime
				|| read_next) {
				
				avi_file = AVI_open_input_file(one_rec.file_name, 1);
				
				// 记录不存在或是开启失败,就定位下一条记录
				if (avi_file EQU NULL) {
					continue;
				}
				
				// 不复用句柄
				if (!read_next) {
					h = (T_PLAY_HADLE)malloc(sizeof(T_PLAY_CONTEXT));
					bzero(h, sizeof(T_PLAY_CONTEXT));
					memcpy(&h->stTimeDay, stTimeDay, sizeof(h->stTimeDay));
				}

				// 判断是否有效
				if (AVI_INVALIDE(avi_file)) {
					LOGE("%s(), frame:%ld, too small", __FUNCTION__, AVI_video_frames(avi_file));
					AVI_close(avi_file);
					continue;
				}
				
				h->fseq_day = fseq;
				h->av_file = avi_file;
				h->with_tz  = with_tz;
				h->f_st_time= one_rec.st_time;
				if (with_tz)
					h->f_st_time= one_rec.st_time + 8*60;		// g_pRunSets->time_set.timezone
				h->f_fps	= (UINT32)AVI_frame_rate(avi_file);
				LogI("total:%" PRId64 "sec, frame:%ld fps:%d, file:%s", 
					one_rec.end_time-one_rec.st_time,
					AVI_video_frames(avi_file), h->f_fps, one_rec.file_name);
				h->videoWidth  = AVI_video_width(avi_file);
				h->videoHeight = AVI_video_height(avi_file);

				// 视频格式
				tmp_str = AVI_video_compressor(avi_file);
				if (stricmp(tmp_str, "h264") EQU 0) 
					h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;
				else
					h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_HEVC;

				// 音频格式
				tmp_value = AVI_audio_format(avi_file);
				if (tmp_value EQU WAVE_FORMAT_ALAW
					|| tmp_value EQU IBM_FORMAT_ALAW) {
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711A;
					h->a_frame_info.flags 	 = (AUDIO_SAMPLE_8K << 2) | (AUDIO_DATABITS_16 << 1) | AUDIO_CHANNEL_MONO;
				} else if (tmp_value EQU WAVE_FORMAT_MULAW
					|| tmp_value EQU IBM_FORMAT_MULAW) {
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711U;
					h->a_frame_info.flags 	 = (AUDIO_SAMPLE_8K << 2) | (AUDIO_DATABITS_16 << 1) | AUDIO_CHANNEL_MONO;
				} else if (tmp_value EQU WAVE_FORMAT_PCM) {
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_PCM;
					h->a_frame_info.flags 	 = (AUDIO_SAMPLE_8K << 2) | (AUDIO_DATABITS_16 << 1) | AUDIO_CHANNEL_MONO;
				} else if (tmp_value EQU WAVE_FORMAT_AAC) {	
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_AAC;					
					if (AVI_audio_channels(avi_file) EQU 1)
						h->a_frame_info.flags = AUDIO_CHANNEL_MONO;
					else
						h->a_frame_info.flags = AUDIO_CHANNEL_STERO;
					if (AVI_audio_bits(avi_file) EQU 16)
						h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
					else
						h->a_frame_info.flags |= (AUDIO_DATABITS_8 << 1);
					switch (AVI_audio_rate(avi_file)){
					BREAK_CASE(8000, 	h->a_frame_info.flags |= (AUDIO_SAMPLE_8K << 2));
					BREAK_CASE(16000, 	h->a_frame_info.flags |= (AUDIO_SAMPLE_16K << 2));
					BREAK_CASE(32000, 	h->a_frame_info.flags |= (AUDIO_SAMPLE_32K << 2));
					BREAK_CASE(44100, 	h->a_frame_info.flags |= (AUDIO_SAMPLE_44K << 2));
					BREAK_CASE(48000, 	h->a_frame_info.flags |= (AUDIO_SAMPLE_48K << 2));
					}
				} else {
					LOGE("unknow audio format: %d", tmp_value);
				}		

				// 定位
				tmp_value = ltime - one_rec.st_time;
				if (tmp_value > 0) {
					int seek_ret = AVI_seek_frame(avi_file, tmp_value*h->f_fps);
					if(seek_ret != 0)
					{
						LOGE("total video frames:%d seek frames:%d",AVI_video_frames(avi_file),tmp_value*h->f_fps);
						AVI_seek_frame(avi_file, tmp_value*(AVI_video_frames(avi_file)/(one_rec.end_time-one_rec.st_time)));
					}
				}

				break;
			}
//			else {
//				LOGI("%s(), not in range, seektime:%ld", __FUNCTION__, ltime);
//			}
			
		}

		fclose(pf);
	} else {
		LOGE("%s() error, file not exist: %s", __FUNCTION__, path);
	}

	if (h && h->av_file) 
		return h;
		
	return NULL;
}

T_PLAY_HADLE mfile_play_open_ex(int snridx, T_PLAY_HADLE h, STimeDay *stTimeDay, UINT8 with_tz)
{
	return mfile_play_open(h, stTimeDay, with_tz);
}

/** 
 * 打开指定文件
 * @param  filename  [文件名]
 * @return           成功返回非NULL;失败返回NULL
 */
T_PLAY_HADLE mfile_play_file(LPCTSTR filename)
{
	avi_t *avi_file = AVI_open_input_file(filename, 1);

	do {
		
		// 记录不存在或是开启失败,就定位下一条记录
		if (avi_file EQU NULL) {
			break;
		}
		
		// 判断是否有效
		if (AVI_INVALIDE(avi_file)) {
			LOGE("%s(), frame:%ld, too small", __FUNCTION__, AVI_video_frames(avi_file));
			AVI_close(avi_file);		
			break;
		}
		
		LPSTR	tmp_str;
		INT32	tmp_value;
		T_PLAY_HADLE h = (T_PLAY_HADLE)calloc(1, sizeof(T_PLAY_CONTEXT));	
		
		h->av_file = avi_file;
		/*************************
		* 不可用
		* h->fseq_day;
		* h->with_tz;
		* h->f_st_time
		*************************/
		h->f_fps	= (UINT32)AVI_frame_rate(avi_file);
		LogI("frame:%ld fps:%d, file:%s", AVI_video_frames(avi_file), h->f_fps, filename);
		h->videoWidth  = AVI_video_width(avi_file);
		h->videoHeight = AVI_video_height(avi_file);

		// 视频格式
		tmp_str = AVI_video_compressor(avi_file);
		if (stricmp(tmp_str, "h264") EQU 0) 
			h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;
		else
			h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_HEVC;

		// 音频格式
		tmp_value = AVI_audio_format(avi_file);
		if (tmp_value EQU WAVE_FORMAT_ALAW
			|| tmp_value EQU IBM_FORMAT_ALAW) {
			h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711A;
			h->a_frame_info.flags	 = (AUDIO_SAMPLE_8K << 2) | (AUDIO_DATABITS_16 << 1) | AUDIO_CHANNEL_MONO;
		} else if (tmp_value EQU WAVE_FORMAT_MULAW
			|| tmp_value EQU IBM_FORMAT_MULAW) {
			h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711U;
			h->a_frame_info.flags	 = (AUDIO_SAMPLE_8K << 2) | (AUDIO_DATABITS_16 << 1) | AUDIO_CHANNEL_MONO;
		} else if (tmp_value EQU WAVE_FORMAT_PCM) {
			h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_PCM;
			h->a_frame_info.flags	 = (AUDIO_SAMPLE_8K << 2) | (AUDIO_DATABITS_16 << 1) | AUDIO_CHANNEL_MONO;
		} else if (tmp_value EQU WAVE_FORMAT_AAC) { 
			h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_AAC;					
			if (AVI_audio_channels(avi_file) EQU 1)
				h->a_frame_info.flags = AUDIO_CHANNEL_MONO;
			else
				h->a_frame_info.flags = AUDIO_CHANNEL_STERO;
			if (AVI_audio_bits(avi_file) EQU 16)
				h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
			else
				h->a_frame_info.flags |= (AUDIO_DATABITS_8 << 1);
			switch (AVI_audio_rate(avi_file)){
			BREAK_CASE(8000,	h->a_frame_info.flags |= (AUDIO_SAMPLE_8K << 2));
			BREAK_CASE(16000,	h->a_frame_info.flags |= (AUDIO_SAMPLE_16K << 2));
			BREAK_CASE(32000,	h->a_frame_info.flags |= (AUDIO_SAMPLE_32K << 2));
			BREAK_CASE(44100,	h->a_frame_info.flags |= (AUDIO_SAMPLE_44K << 2));
			BREAK_CASE(48000,	h->a_frame_info.flags |= (AUDIO_SAMPLE_48K << 2));
			}
		} else {
			LOGE("unknow audio format: %d", tmp_value);
		}	

		if (h && h->av_file) 
			return h;
		
	}while (FALSE);
	
	return NULL;
	
}


/**
 * 读数据
 * @param  h        上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  buf      缓冲
 * @param  buf_len  缓冲大小
 * @param  auto_next_file  自动读取下一个文件 
 * @return          返回读取到的数据大小；失败返回0
 */
INT32 mfile_play_read_frame(T_PLAY_HADLE h, CHAR *buf, UINT32 buf_len, UINT8 auto_next_file)
{
	LONG 	read_len;
	INT32 	is_key_frame;
	LONG	frame_num;
	INT32	fnRet;
	
	if (h EQU NULL || h->av_file EQU NULL)
		return 0;

	while (h->av_file != NULL) {
		fnRet = AVI_read_data((avi_t*)h->av_file, buf, buf_len, buf, buf_len, &read_len, &is_key_frame);
		if (fnRet > 0) 
		{	
			// 时间戳
			frame_num = AVI_get_frame_pos((avi_t*)h->av_file);
			
			h->frame_size = read_len;
			
//			printf("AVI_read_data(), seq:%3ld, len:%ld, utc:%d, ts:%d\n", 
//				frame_num, read_len, h->v_frame_info.utctime, h->v_frame_info.timestamp);
			
			if (1 EQU fnRet) // 视频
			{
				h->is_video = TRUE;
				h->v_frame_info.flags = is_key_frame ? IPC_FRAME_FLAG_IFRAME:IPC_FRAME_FLAG_PBFRAME; 
				h->v_frame_info.timestamp = frame_num*1000/h->f_fps;
				h->v_frame_info.utctime   = h->f_st_time + frame_num/h->f_fps;
			}
			else if (2 EQU fnRet) // 音频
			{
				h->is_video = FALSE;			
				h->a_frame_info.timestamp = frame_num*1000/h->f_fps;
				h->a_frame_info.utctime   = h->f_st_time + frame_num/h->f_fps;
			}
			
			return read_len;
		} else if (fnRet EQU 0) {
			if (!auto_next_file)
				break;
			
			mfile_play_close(h, FALSE);			
			if (mfile_play_open(h, NULL, h->with_tz) EQU NULL) {
				break;
			}
		}	
	}
		
	return 0;
}


/**
 * 定位文件
 * @param  h        	上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  offset_sec   偏移多少秒(相当文件开始)
 * @return          	成功返回TRUE；失败返回FALSE
 */
UINT8 mfile_play_seek(T_PLAY_HADLE h, UINT32 offset_sec)
{
	if (h EQU NULL || h->av_file EQU NULL)
		return FALSE;

	// 定位
	return AVI_seek_frame((avi_t*)h->av_file, offset_sec*h->f_fps) EQU 0 ? TRUE : FALSE;
}


/**
 * 关闭句柄并释放资源
 * @param  h 		上下文句柄
 * @param  free_p 	释放资源标记
 */
VOID mfile_play_close(T_PLAY_HADLE h, UINT8 free_p)
{
	if (h EQU NULL)
		return;

	if (h->av_file != NULL) {
		AVI_close((avi_t*)h->av_file);
		h->av_file = NULL;
	}

	if (free_p) 
		free(h);
}


