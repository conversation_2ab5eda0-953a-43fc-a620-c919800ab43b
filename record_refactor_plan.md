# RecordManager 重构方案

## 1. 职责分离重构

### 原始问题
- RecordChannel类3264行，职责过重
- 事件录像与普通录像逻辑混合
- 线程同步复杂，多个锁交织

### 重构方案：拆分为4个专职类

```cpp
// 1. 录像文件管理器
class RecordFileManager {
public:
    bool createNewFile(const std::string& basePath, time_t timestamp);
    void closeCurrentFile();
    bool writeFrame(const T_FRAME_HEADER* header, const char* data);
    std::string getCurrentVideoFile() const;
    std::string getCurrentIndexFile() const;
    
private:
    int m_videoFd{-1};
    int m_indexFd{-1};
    std::string m_currentVideoFile;
    std::string m_currentIndexFile;
    time_t m_fileStartTime{0};
};

// 2. 录像事件管理器（简化）
class SimpleEventManager {
public:
    void startEvent(uint8_t eventType, int duration);
    void stopEvent(uint8_t eventType);
    bool hasActiveEvent() const;
    uint8_t getCurrentEventType() const;
    
private:
    struct EventInfo {
        uint8_t type;
        time_t endTime;
    };
    std::vector<EventInfo> m_activeEvents;
    TCSLock m_eventLock;
};

// 3. 录像线程管理器
class RecordThreadManager {
public:
    bool startRecording();
    void stopRecording();
    bool isRecording() const { return m_running; }
    
private:
    void recordingLoop();
    static void* threadEntry(void* arg);
    
    pthread_t m_thread{0};
    std::atomic<bool> m_running{false};
    std::atomic<bool> m_shouldStop{false};
};

// 4. 简化的录像通道
class SimpleRecordChannel {
public:
    SimpleRecordChannel(int channelId);
    ~SimpleRecordChannel();
    
    bool start();
    void stop();
    void setEventType(uint8_t eventType);
    bool isRunning() const;
    
private:
    int m_channelId;
    RecordFileManager m_fileManager;
    SimpleEventManager m_eventManager;
    RecordThreadManager m_threadManager;
};
```

## 2. 线程同步简化

### 原始问题
- 多个锁和条件变量交织使用
- 死锁风险高
- 同步逻辑复杂

### 重构方案：单一同步机制

```cpp
// 统一的录像状态
struct RecordState {
    bool isRecording{false};
    bool hasActiveEvent{false};
    uint8_t currentEventType{0};
    time_t fileStartTime{0};
    std::string currentFile;
};

// 线程安全的状态管理器
class RecordStateManager {
public:
    RecordState getState() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_state;
    }
    
    void updateState(const std::function<void(RecordState&)>& updater) {
        std::lock_guard<std::mutex> lock(m_mutex);
        updater(m_state);
        m_condition.notify_all();
    }
    
    void waitForStateChange(const std::function<bool(const RecordState&)>& predicate) {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_condition.wait(lock, [&] { return predicate(m_state); });
    }
    
private:
    mutable std::mutex m_mutex;
    std::condition_variable m_condition;
    RecordState m_state;
};
```

## 3. 事件处理简化

### 原始问题
- 事件录像逻辑与普通录像混合
- 事件状态管理复杂
- 事件优先级处理困难

### 重构方案：策略模式

```cpp
// 录像策略基类
class RecordStrategy {
public:
    virtual ~RecordStrategy() = default;
    virtual bool shouldRecord(time_t currentTime) = 0;
    virtual int getFileDuration() = 0;
    virtual uint8_t getRecordType() = 0;
};

// 普通录像策略
class NormalRecordStrategy : public RecordStrategy {
public:
    bool shouldRecord(time_t currentTime) override {
        return true; // 始终录像
    }
    int getFileDuration() override { return ALL_REC_FILE_TIME; }
    uint8_t getRecordType() override { return RT_NORMAL; }
};

// 事件录像策略
class EventRecordStrategy : public RecordStrategy {
public:
    EventRecordStrategy(uint8_t eventType, time_t endTime)
        : m_eventType(eventType), m_endTime(endTime) {}
    
    bool shouldRecord(time_t currentTime) override {
        return currentTime < m_endTime;
    }
    int getFileDuration() override { return EVENT_REC_FILE_TIME; }
    uint8_t getRecordType() override { return m_eventType; }
    
private:
    uint8_t m_eventType;
    time_t m_endTime;
};

// 策略管理器
class RecordStrategyManager {
public:
    void setStrategy(std::unique_ptr<RecordStrategy> strategy) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_strategy = std::move(strategy);
    }
    
    RecordStrategy* getCurrentStrategy() {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_strategy.get();
    }
    
private:
    std::mutex m_mutex;
    std::unique_ptr<RecordStrategy> m_strategy;
};
```

## 4. 文件操作简化

### 原始问题
- 文件创建和关闭逻辑分散
- 文件名生成复杂
- 错误处理不统一

### 重构方案：文件操作封装

```cpp
// 简化的文件操作类
class RecordFile {
public:
    struct FileInfo {
        std::string videoPath;
        std::string indexPath;
        time_t startTime;
        uint8_t recordType;
    };
    
    static FileInfo generateFileInfo(int channelId, time_t timestamp, uint8_t recordType);
    static bool createRecordFiles(const FileInfo& info, int& videoFd, int& indexFd);
    static void closeRecordFiles(int videoFd, int indexFd, const FileInfo& info);
    static bool writeVideoFrame(int fd, const T_FRAME_HEADER* header, const char* data);
    static bool writeIndexEntry(int fd, const T_INDEX_ENTRY* entry);
    
private:
    static std::string generateFileName(int channelId, time_t timestamp, const char* ext);
    static void ensureDirectoryExists(const std::string& filePath);
};
```

## 5. 重构实施步骤

### 第一阶段：文件操作重构（2天）
1. 实现RecordFile类
2. 替换原有文件操作代码
3. 统一错误处理

### 第二阶段：状态管理重构（2天）
1. 实现RecordStateManager
2. 简化线程同步逻辑
3. 统一状态查询接口

### 第三阶段：事件处理重构（2天）
1. 实现策略模式
2. 分离事件录像逻辑
3. 简化事件状态管理

### 第四阶段：职责分离（3天）
1. 创建4个专职类
2. 迁移功能到对应类
3. 保持接口兼容性

## 6. 预期效果

### 代码量减少
- 原始：3264行 → 重构后：约1200行（减少63%）
- RecordChannel类：1500行 → 300行（减少80%）

### 复杂度降低
- 线程同步：多锁机制 → 单一状态管理器
- 事件处理：混合逻辑 → 策略模式
- 文件操作：分散处理 → 统一封装

### 可维护性提升
- 职责清晰，易于理解
- 同步机制简化，减少死锁风险
- 策略模式，易于扩展新的录像类型
