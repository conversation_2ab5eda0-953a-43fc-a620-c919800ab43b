/***************************************************************************
** 版权所有: 成都云创天下 Copyright (c) 2015-2020  ********************     
** 文件名称: X:\mpp\sample\vsipc\storage\vs_media_file.cpp
** 文件标识: 
** 内容摘要: 
			1.每天0点强制切换文件
			2.
** 当前版本: v1.0
** 作     者: 徐辉
** 完成日期: 2018年3月15日
** 修改记录: 
** 修改记录: 
** 修改日期: 
** 版 本 号: 
** 修 改 人: 
** 修改内容: 
***************************************************************************/

#include <unistd.h>
#include <sys/stat.h>
#include <sys/vfs.h>
#include <sys/types.h>
#include <dirent.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/types.h>
#include <linux/netlink.h>
#include <errno.h>

#if defined(MFILE_PRE_RECORD)
#include <string>
#endif

#include "vs_media_file.h"
#include "ipc/vsipc.h"
#if	defined(OBS_ENABLE) && (OBS_MODE == 1)	
	#include "obs/vs_obs.h"
#endif
extern "C" {
	#include "libavcodec/avcodec.h"
	#include "libavutil/opt.h"
	#include "libavformat/avformat.h"
	#include "libavutil/mathematics.h"
	#include "libavutil/avutil.h"
}
#if defined(GB28181_ENABLE) && (GB28181_VERSION==0) 
	#include "gb28181/gb_main.h"
#endif
#if defined(GB28181_ENABLE) && (GB28181_VERSION==2) 
	#include "gb28181_v2/gbv2_main.h"
#endif


extern T_REC_WORK	g_rec_work;
extern INT32		g_pcm_fd;

#define	AV_FILE_EXT			".mp4"

#define MP4_WRITE_BSF	0
#define MP4_READ_BSF	1

VOID get_ffmpeg_error_msg(int errCode){
	char errmsg[1024] = {0};

	av_strerror(errCode, errmsg, sizeof(errmsg));
	printf("errInfo: %s\n", errmsg);
	
	return;
}


/**
 * 写mp4头
 *  成功返回TRUE,失败返回FALSE
 */
UINT8 mp4_write_header(AVFormatContext *avh, AVBSFContext **audBsf,
	AVBSFContext **vidBsf)
{

#define CHECK_STREAM_PARAMS() \
	if((g_rec_work.videoWidth EQU 0)\
		|| (g_rec_work.videoHeight EQU 0)\
		|| (g_rec_work.videoKbps EQU 0)\
		|| (g_rec_work.videoFps EQU 0)){\
		return FALSE;\
	}

    if (!avh) return FALSE;

	vsipc_get_avheader(g_pRunSets->misc_set.rec_stream_chn, &g_rec_work);

	CHECK_STREAM_PARAMS();
	
	g_rec_work.videoNums = 0;
	g_rec_work.audioPts = 0;
	g_rec_work.vid = INVALID_VALUE;
	g_rec_work.aid = INVALID_VALUE;
#ifdef GB28181_ENABLE	
#if (GB_PLATFORM_ID==GB_PFID_SCDX_YJJ)	// 四川电信药监局
	g_rec_work.audioEnable = FALSE;
#endif
#endif
	
	// 视频格式
    {
		AVStream 			*out_stream = avformat_new_stream(avh, NULL);
		AVCodecParameters	*c;
#if MP4_WRITE_BSF		
		INT32	fnRet;
		CONST 	AVBitStreamFilter *filter = NULL;
#endif

		if (out_stream EQU NULL) {
			LOGE("%s(), avformat_new_stream(video) fail", __func__);
			return FALSE;
		}

		g_rec_work.vid = out_stream->index;		
		out_stream->id = avh->nb_streams - 1;
		c = out_stream->codecpar;
		if (g_rec_work.videoCodec EQU QF_CODEC_H265) {	
			c->codec_id  = AV_CODEC_ID_HEVC;
#if MP4_WRITE_BSF
			filter = av_bsf_get_by_name("hevc_mp4toannexb");
#endif
		}	
		else {
			c->codec_id  = AV_CODEC_ID_H264;	
#if MP4_WRITE_BSF			
			filter = av_bsf_get_by_name("h264_mp4toannexb");
#endif
		}
		
#if MP4_WRITE_BSF
		do {
			if (!filter)
				break;
			
			fnRet = av_bsf_alloc(filter, vidBsf);
			if (fnRet < 0) {
				LOGE("%s(), av_bsf_alloc()=%#x fail", __FUNCTION__, fnRet);
				get_ffmpeg_error_msg(fnRet);
				break;
			}
			fnRet = avcodec_parameters_copy((*vidBsf)->par_in, out_stream->codecpar);
			if (fnRet < 0) {
				LOGE("%s(), avcodec_parameters_copy()=%#x fail", __FUNCTION__, fnRet);
				get_ffmpeg_error_msg(fnRet);
				av_bsf_free(vidBsf);
				*vidBsf = NULL;
				break;
			}
			fnRet = av_bsf_init(*vidBsf);
			if (fnRet < 0) {
				LOGE("%s(), av_bsf_init()=%#x fail", __FUNCTION__, fnRet);
				get_ffmpeg_error_msg(fnRet);
				av_bsf_free(vidBsf);
				*vidBsf = NULL;
				break;
			}
		} while (FALSE);
#else
		*vidBsf = NULL;
#endif
			
		c->codec_type 	 = AVMEDIA_TYPE_VIDEO;
		c->bit_rate 	 = g_rec_work.videoKbps*1000;
		c->width 		 = g_rec_work.videoWidth;
		c->height 		 = g_rec_work.videoHeight;
		out_stream->time_base.den = g_rec_work.videoFps * 100;		//设置fps有效
		out_stream->time_base.num = 100;
		//out_stream->codecpar->codec_tag = MKTAG('m','p','4','v');
		out_stream->codecpar->codec_tag = 0;
		
		c->format = AV_PIX_FMT_YUV420P;				//hcc 设置后，可移动
    }

	// 音频
	if (g_rec_work.audioEnable) {
		AVCodecID adoFormat = AV_CODEC_ID_NONE;

		switch (g_rec_work.audioCodec) {
		case QF_CODEC_PCM:
			adoFormat = AV_CODEC_ID_PCM_S16LE;
			break;

		case QF_CODEC_G711A:
			adoFormat = AV_CODEC_ID_PCM_ALAW;
			break;

		case QF_CODEC_G711U:
			adoFormat = AV_CODEC_ID_PCM_MULAW;
			break;
		
		case QF_CODEC_AAC:
			adoFormat = AV_CODEC_ID_AAC;
			//*audBsf = av_bitstream_filter_init("aac_adtstoasc"); 
			break;

		default:
			LOGE("%s(), WAVE_FORMAT_UNKNOWN: %d", __FUNCTION__, g_rec_work.audioCodec);
		}		
		
		if (adoFormat != AV_CODEC_ID_NONE) {
			AVCodec 	*codec = NULL;
			
			if (adoFormat EQU AV_CODEC_ID_AAC)	codec = avcodec_find_encoder(AV_CODEC_ID_AAC);
			
			AVStream 	*out_stream = avformat_new_stream(avh, codec);
			AVCodecParameters	*c;

			if (out_stream) {

				g_rec_work.aid = out_stream->index;
				out_stream->id = avh->nb_streams-1;

				c = out_stream->codecpar;

				c->codec_type 	= AVMEDIA_TYPE_AUDIO;
				c->codec_id 	= adoFormat;
				c->channel_layout=AV_CH_LAYOUT_MONO;//	AV_CH_LAYOUT_STEREO;
				c->sample_rate = g_rec_work.audioSampleRate;
				c->channels    = g_rec_work.audioChannel;
				c->bits_per_raw_sample = g_rec_work.audioBitsPerSample;

				if (adoFormat EQU AV_CODEC_ID_AAC) {
					c->bit_rate    = g_rec_work.audioSampleRate *8;
					c->format = AV_SAMPLE_FMT_FLTP;
					out_stream->time_base.num = 1;
					out_stream->time_base.den = out_stream->codec->sample_rate;
					out_stream->codec->time_base = out_stream->time_base;
					c->frame_size = 768;
					out_stream->codecpar->codec_tag = 0;
				}
				else {
					c->bit_rate    = g_rec_work.audioSampleRate * g_rec_work.audioBitsPerSample;
					c->format = AV_SAMPLE_FMT_S16;
					out_stream->codecpar->codec_tag = 0;
				
					if (avh->oformat->flags & AVFMT_GLOBALHEADER)
						out_stream->codecpar->codec_tag |= AV_CODEC_FLAG_GLOBAL_HEADER;
				}
			
			}
			else {
				LOGE("%s(), avformat_new_stream(audio) fail", __func__);
			}

		}
	}
	

    return TRUE;       
}


/**
 * 时间转换
 */
extern time_t to_timet(STimeDay *p);

/**
 * 插入一条记录
 * @param  one_rec 记录
 * @return         成功为1；失败为0
 */
extern UINT8 rec_db_add(T_ONE_REC *one_rec);

/**
 * 打开写文件
 * @param  rec_type  录像类型 
 * @return	 成功返回1,失败返回0
 */
UINT8 mfile_open(VS_RECORD_TYPE rec_type)
{
	INT32 			fnRet;	
	CHAR 			*p = g_rec_work.one_rec.file_name;
	T_DATETIME		now;
	AVDictionary 	*opt = NULL;
	AVFormatContext	*avih = NULL;
	AVBSFContext 	*audBsf = NULL;
	AVBSFContext 	*vidBsf = NULL;

	if (!mfile_tf_ready())
		return FALSE;

	// 定进录像没有开启计划表,不录像
	if (rec_type EQU RT_NORMAL
		&& !g_rec_work.enable_sche)
	{
		return FALSE;
	}


#ifdef CN21_ENABLE
	if (system_is_dormancy()) { // 休眠
		return FALSE;
	}
#else
	// 没有同步到时间,不录像
//	if (MIN_UTC_TIME > utc_time()){
//		return FALSE;
//	}
#endif
		
	if (g_rec_work.start) {

		// 已经开始了,并且类型一致,继续工作
		if (rec_type EQU g_rec_work.one_rec.rec_type)
			return TRUE;

		// 如果录像已经开始了, 不论当前是什么录像, 不改变类型(可能是报警录像,但是对于录像来说,只要在录就可以了.)
		if (rec_type EQU RT_NORMAL)
			return TRUE;
		else {
			/**
			 * 如果已经开始了,就把当前文件标记为报警录像,并且在结束时改变成报警录像
			 */
			g_rec_work.one_rec.rec_type = RT_MOTION_DETECT;
			g_rec_work.one_rec.change_st = TRUE;
			return TRUE;
		}
		
		// mfile_close();
	}

	ScopedLocker	lock(g_rec_work.avi_lock);

	// 如果两个线程同时调用, 可能后面这个线程还在排队,前面一个刚才出去
	if (g_rec_work.start)
		return TRUE;
	
	QfSet0(&g_rec_work.one_rec, sizeof(g_rec_work.one_rec));
	get_now(&now);
	p += sprintf(g_rec_work.one_rec.file_name, "%s/" REC_DIR_FMT "/", 
			REC_PATH, now.usYear, now.usMonth, now.usDay);
	if (!dir_exist(g_rec_work.one_rec.file_name)) {
		mkdir(g_rec_work.one_rec.file_name, 0666);	
	}
	else {
		CHAR	index_file[100];	

		sprintf(index_file, "%s%s", g_rec_work.one_rec.file_name, REC_DB_INDEX);
		if (!file_exist(index_file))
			rebuild_dbfile(g_rec_work.one_rec.file_name);
	}

//	LOGI("mfile_wopen() dir:%s", g_one_rec.file_name);
#ifdef CN21_ENABLE
	sprintf(p, "%04d%02d%02d%02d%02d%02d_%d%s", 
		now.usYear, now.usMonth, now.usDay, 
		now.ucHour, now.ucMin, now.ucSec, rec_type, AV_FILE_EXT);
#else
	sprintf(p, "%02d%02d%02d_%d%s", now.ucHour, now.ucMin, now.ucSec, rec_type, AV_FILE_EXT);
#endif
	avformat_alloc_output_context2(&avih, NULL, NULL, g_rec_work.one_rec.file_name);
	if (avih EQU NULL)
		return FALSE;
	
	LogI("open file:%s", g_rec_work.one_rec.file_name);
	if (!mp4_write_header(avih, &audBsf, &vidBsf)) {
		goto FN_ERR;
	}	
	
	av_dump_format(avih, 0, g_rec_work.one_rec.file_name, 1);

//	printf("avh->oformat->flags = 0x%x\n", avih->oformat->flags);
	
	// 打开文件po
	if (!(avih->oformat->flags & AVFMT_NOFILE)) {
		fnRet = avio_open(&avih->pb, g_rec_work.one_rec.file_name, AVIO_FLAG_WRITE);
		if (fnRet < 0) {
			LOGE( "ERR:: avio_open output file '%s', '%d'\n", g_rec_work.one_rec.file_name, fnRet);
			goto FN_ERR;
		}
	}

	// Write file header
	av_dict_set_int(&opt, "video_track_timescale", g_rec_work.videoFps, 0);		// 12 fps
    fnRet = avformat_write_header(avih, &opt);
	if (fnRet < 0) {
		printf( "ERR:: avformat_write_header file = %s, fnRet = 0x%x\n", g_rec_work.one_rec.file_name, fnRet);
		goto FN_ERR;
	}
	
	// 强制产生I帧
	vsipc_requst_iframe(g_pRunSets->misc_set.rec_stream_chn);

	// 保存录像上下文
	{
		g_rec_work.one_rec.st_time	= now.tv.tv_sec;	
		g_rec_work.one_rec.end_time = g_rec_work.one_rec.st_time;
		g_rec_work.one_rec.rec_type = rec_type;
		g_rec_work.one_rec.flag = OR_FLAG_REC;
		g_rec_work.wait_key_frame = TRUE;
		g_rec_work.avh = avih;
		g_rec_work.audBsf = audBsf;
		g_rec_work.vidBsf = vidBsf;
		g_rec_work.start= TRUE;
	}

	// 发送通知
	if (g_rec_work.event_cb)
		g_rec_work.event_cb(TRUE, g_rec_work.one_rec.file_name);
	
	return TRUE;

FN_ERR:		

	LOGE("%s() fail, filename: %s", __func__, g_rec_work.one_rec.file_name);

	if (audBsf != NULL)
		av_bsf_free(&audBsf);
	if (vidBsf != NULL)
   		av_bsf_free(&vidBsf);
   
	// 关闭输出
	if (avih != NULL)
		avformat_close_input(&avih);
	avih = NULL;

	unlink(g_rec_work.one_rec.file_name);

   	return FALSE;
}


/**
 * 写视频
 * @return   成功返回1,失败返回0
 */
UINT8 mfile_write_video(BYTE *buff , UINT32 size, UINT8 key_frame, UINT64 timestamp)
{
	ScopedLocker	lock(g_rec_work.avi_lock);	

	if (!g_rec_work.start
		|| g_rec_work.avh EQU NULL
		|| !mfile_tf_ready())
		return 0;

	if (g_rec_work.wait_key_frame) {
 		if (!key_frame) return 1;
		g_rec_work.wait_key_frame = FALSE;
	}
	
	AVFormatContext	*avCtx = (AVFormatContext*)g_rec_work.avh; 	
#if MP4_WRITE_BSF	
	AVBSFContext 	*vidBsf = (AVBSFContext*)g_rec_work.vidBsf; 
#endif
	AVPacket 	 	pkt;
	INT32		 	fnRet;	

	AVRational rt_video_bit;			// 10 fps
	
	rt_video_bit.den = 90000;
	rt_video_bit.num = 1;

	// 视频
 	{
		if (g_rec_work.vid EQU INVALID_VALUE)
			return FAIL;

		if (g_rec_work.videoNums EQU 0 && !key_frame)
			return OK;

		AVStream	*avStream = avCtx->streams[g_rec_work.vid];
		
		if (avStream EQU NULL)
			return FAIL;

		av_init_packet(&pkt);
		pkt.flags |=  key_frame ? AV_PKT_FLAG_KEY : 0 ;
		pkt.stream_index = g_rec_work.vid;
		
		pkt.duration = rt_video_bit.den / g_rec_work.videoFps;		
		pkt.pts = (g_rec_work.videoNums * rt_video_bit.den) / g_rec_work.videoFps;
		pkt.dts = pkt.pts;
		g_rec_work.videoNums++;
		
		pkt.pos = -1;
		
		AVRational &rt_video_fps = avStream->time_base;

	//	enc_warn("VVV::pts = %ld, dts = %ld duration = %ld\n", pkt.pts, pkt.dts, pkt.duration);
	
		pkt.pts = av_rescale_q_rnd(pkt.pts, rt_video_bit, rt_video_fps, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
		pkt.dts = av_rescale_q_rnd(pkt.dts, rt_video_bit, rt_video_fps, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
		pkt.duration = av_rescale_q(pkt.duration, rt_video_bit, rt_video_fps);

		//QfTrace(HERE, "video, pts = %ld, dts = %ld duration = %ld##", pkt.pts, pkt.dts, pkt.duration);

#if 1
		// 不写ios回放有问题
		if (key_frame) {
			UINT32 		st_code = -1;
			INT32		st_nums = avStream->codecpar->codec_id EQU AV_CODEC_ID_HEVC ? 3 : 2;

			// 把sps与pps加到一起
			for (int i = 4; i < 256; i++){
				st_code = (st_code << 8) + buff[i];
				if (st_code EQU 0x00000001) {
					if (--st_nums EQU 0) {
 						UINT32 idr_pos = i + 1 - 4;
						
						avStream->codecpar->extradata = buff;
						avStream->codecpar->extradata_size = idr_pos;
						// 必须双数
						if (idr_pos&1) { 
							avStream->codecpar->extradata_size += 1;
						}
						buff += idr_pos;
						size -= idr_pos;
//						LOGI("sps+pps:%d, idrsize:%d", avStream->codecpar->extradata_size, size);
//						hex_print(avStream->codecpar->extradata, 64);
//						hex_print(buff, 16);
						break;
					}
				}
			}

		}
		else {
			avStream->codecpar->extradata = NULL;
			avStream->codecpar->extradata_size = 0;
		}
#endif	

	}

	pkt.data = buff;
	pkt.size = size;
#if MP4_WRITE_BSF	
	if (vidBsf != NULL) {
		fnRet = av_bsf_send_packet(vidBsf, &pkt);
		if (fnRet < 0) {
			LOGI("%s() av_bsf_send_packet()=%#x", __func__, fnRet);
			get_ffmpeg_error_msg(fnRet);
			return FALSE;
		}
		fnRet = av_bsf_receive_packet(vidBsf, &pkt);
		if (fnRet < 0) {
			LOGI("%s() av_bsf_send_packet()=%#x", __func__, fnRet);
			get_ffmpeg_error_msg(fnRet);
			return FALSE;
		}
	}
#endif	

	fnRet = av_interleaved_write_frame(avCtx, &pkt);	
	if (fnRet < 0)
		mfile_close();
//	fnRet = av_write_frame(m_outCont, &pkt);

//    LOGI("%s()=%d, iskeyframe:%d, size:%d", __func__, fnRet, key_frame, size);

	return fnRet < 0 ? FALSE : TRUE;
}

/**
 * 写音频
 * @return   成功返回1,失败返回0
 */
UINT8 mfile_write_audio(BYTE *buff , UINT32 size, UINT64 timestamp)
{
#define AAC_AUDIO_PER_SIZE 1024
	ScopedLocker	lock(g_rec_work.avi_lock);	

	if (!g_rec_work.start
		|| g_rec_work.avh EQU NULL
		|| !mfile_tf_ready())
		return 0;

	if (g_rec_work.wait_key_frame) {
		return 1;
	}

	AVFormatContext	*avCtx = (AVFormatContext*)g_rec_work.avh;
//	AVBSFContext 	*audBsf = (AVBSFContext*)g_rec_work.audBsf; 
 	
	AVPacket 	 	pkt;
	INT32		 	fnRet;

	AVRational rt_audio_sample;		// 8000 
	rt_audio_sample.den = g_rec_work.audioSampleRate;
	rt_audio_sample.num = 1;


	// 音频
	{
		if (g_rec_work.aid EQU INVALID_VALUE)
			return FAIL;

		av_init_packet(&pkt);
		pkt.flags = 0;
		pkt.stream_index = g_rec_work.aid;
		
		if (g_rec_work.audioCodec EQU QF_CODEC_AAC) {
			int64_t calc_duration = (double)av_q2d(rt_audio_sample)*AV_TIME_BASE;
			
			pkt.duration = calc_duration*AAC_AUDIO_PER_SIZE;
			pkt.pts = g_rec_work.audioPts *AAC_AUDIO_PER_SIZE;		
			pkt.dts = pkt.pts;		
			g_rec_work.audioPts ++;
		}
		else {
			pkt.duration = size;
			pkt.pts = g_rec_work.audioPts;		//hcc static 
			pkt.dts = pkt.pts;
			g_rec_work.audioPts += size;
		}
		pkt.pos = -1;	
		pkt.pts = av_rescale_q_rnd(pkt.pts, rt_audio_sample, rt_audio_sample, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
		pkt.dts = av_rescale_q_rnd(pkt.dts, rt_audio_sample, rt_audio_sample, (AVRounding)(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
		pkt.duration = av_rescale_q(pkt.duration, rt_audio_sample, rt_audio_sample);

		//QfTrace(HERE, "audio, pts = %ld, dts = %ld duration = %ld##", pkt.pts, pkt.dts, pkt.duration);
	}
	
	pkt.data = buff;
	pkt.size = size;

	fnRet = av_interleaved_write_frame(avCtx, &pkt);
	if (fnRet < 0)
		mfile_close();

	//LOGI("%s()=%d, size:%d", __func__, fnRet, size);

	return fnRet < 0 ? FALSE : TRUE;
	
}

/**
 * 关闭写文件
 */
VOID mfile_close(T_REC_WORK* p_rec_work)
{
	ScopedLocker	lock(g_rec_work.avi_lock);
	AVFormatContext *h = (AVFormatContext *)g_rec_work.avh;
	AVBSFContext 	*vidBsf = (AVBSFContext*)g_rec_work.vidBsf; 
	AVBSFContext 	*audBsf = (AVBSFContext*)g_rec_work.audBsf; 

	g_rec_work.avh 		= NULL;
	g_rec_work.vidBsf	= NULL;
	g_rec_work.audBsf	= NULL;
	g_rec_work.start	= FALSE;
	g_rec_work.wait_key_frame = TRUE;
	
	if (h != NULL) {
		UINT8	del_file = g_rec_work.videoNums < 10 ? TRUE : FALSE;

		if (audBsf != NULL)
			av_bsf_free(&audBsf);
		if (vidBsf != NULL)
	   		av_bsf_free(&vidBsf);

		av_write_trailer(h);		// 写入MP4文件尾，否则无法播放;			
		avformat_close_input(&h);

#if defined(MFILE_PRE_RECORD)
		// 修改文件名
		if (g_rec_work.one_rec.change_st) {
			STimeDay	stBeginTime;
			CHAR 		*p = g_rec_work.one_rec.file_name;
			std::string		org_file(p);
				
			LTIME_2_PARSE(g_rec_work.one_rec.st_time, stBeginTime);
			p += sprintf(g_rec_work.one_rec.file_name, "%s/" REC_DIR_FMT "/", 
						REC_PATH, stBeginTime.year, (UINT16)stBeginTime.month, (UINT16)stBeginTime.day);
			sprintf(p, "%02hhu%02hhu%02hhu_%d%s", stBeginTime.hour, stBeginTime.minute, stBeginTime.second, 
				g_rec_work.one_rec.rec_type, AV_FILE_EXT);
			rename(org_file.c_str(), g_rec_work.one_rec.file_name);
		}
#endif

		T_DATETIME	now;

		LogI("close file:%s", g_rec_work.one_rec.file_name);
		get_now(&now);
#ifdef CN21_ENABLE
		{
			LPSTR	src_file = _strdup(g_rec_work.one_rec.file_name);
			LPSTR	p = g_rec_work.one_rec.file_name + strlen(g_rec_work.one_rec.file_name) - 6;
			
			p += sprintf(p, "-%04d%02d%02d%02d%02d%02d", 
					now.usYear, now.usMonth, now.usDay, 
					now.ucHour, now.ucMin, now.ucSec);
			if (g_rec_work.one_rec.rec_type EQU RT_MOTION_DETECT) {
				p += sprintf(p, "_%s", "ALARM");
			}
			p += sprintf(p, "%s", AV_FILE_EXT);
			rename(src_file, g_rec_work.one_rec.file_name);			
			free(src_file);
		}
#endif
		g_rec_work.one_rec.end_time = now.tv.tv_sec;
		g_rec_work.one_rec.flag = OR_FLAG_OK;
		if (!del_file) {
			rec_db_add(&g_rec_work.one_rec);
			sync();
#if	defined(OBS_ENABLE) && (OBS_MODE == 1)	
			if (g_pRunSets->obs_set.enable) {
				CHAR 		obs_url[200];
				CHAR		*tmp = obs_url;
				STimeDay 	stBeginTime;
			    STimeDay 	stEndTime;
				
				LTIME_2_PARSE(g_rec_work.one_rec.st_time, stBeginTime);
				LTIME_2_PARSE(g_rec_work.one_rec.end_time, stEndTime);

				if (strlen(g_pRunSets->obs_set.prefix) > 0)
					tmp += sprintf(tmp, "/%s", g_pRunSets->obs_set.prefix);
				
				sprintf(tmp, "/%s/%04d%02d%02d/%02d%02d%02d_%02d%02d%02d_%02d%s", 
					g_pEmbedInfo->p2pid,
					stBeginTime.year, stBeginTime.month, stBeginTime.day,
					stBeginTime.hour, stBeginTime.minute, stBeginTime.second,
					stEndTime.hour, stEndTime.minute, stEndTime.second, 
					g_rec_work.one_rec.rec_type,
					AV_FILE_EXT
					);
				_strlwr(tmp);
				obs_push_file(g_rec_work.one_rec.file_name, obs_url, 
					g_rec_work.one_rec.st_time, g_rec_work.one_rec.end_time);
			}
#endif			
		} else {
			CHAR tmp[200];

			sprintf(tmp, "%s.del", g_rec_work.one_rec.file_name);
			rename(g_rec_work.one_rec.file_name, tmp);
			LOGE("avifile to small, be remove: %s", tmp);
		}

		// 发送通知
		if (g_rec_work.event_cb)
			g_rec_work.event_cb(del_file?-1:FALSE, g_rec_work.one_rec.file_name);
	}
}



/**
 * 打开文件, 根据stTimeDay定位文件
 * @param  old_h     old_h=NULL, 则分配句柄；old_h!=NULL,则根据定位决定是否关闭再重建；此句柄要返回给下面的三个函数使用
 * @param  stTimeDay 需要定位的时间
 * @return           成功返回非0
 */
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
T_PLAY_HADLE mfile_play_open(T_PLAY_HADLE h, STimeDay *stTimeDay, UINT8 with_tz)
{
	INT32	fnRet;
	CHAR	path[200];	
	FILE	*pf;	
	time_t	ltime;
	UINT8	read_next;

#ifdef USE_HTS_PROTOCOL
	extern void time_convert(STimeDay *td, int zone);

	time_convert(stTimeDay, g_pRunSets->time_set.timezone * 60);
#endif	

	// 确认是定位还是打开下一个记录
	if (stTimeDay != NULL) {
		mfile_play_close(h, TRUE);
		h = NULL;	
		read_next = FALSE;
	} else {
		QF_ASSERT(h != NULL);
		stTimeDay = &h->stTimeDay;
		read_next = TRUE;
	}
	QF_ASSERT(stTimeDay != NULL);
	ltime = to_timet(stTimeDay);	

	sprintf(path, "%s/" REC_DIR_FMT "/" REC_DB_INDEX, 
		REC_PATH, stTimeDay->year, (UINT16)stTimeDay->month, (UINT16)stTimeDay->day);
	LogI("open index: %s, ltime:%ld, %04d%02d%02d %02d:%02d:%02d", path,
		ltime, stTimeDay->year, stTimeDay->month, stTimeDay->day, stTimeDay->hour, stTimeDay->minute, stTimeDay->second);
	mfile_new_dbfile(path);
	pf = fopen(path, "rb");
	if (pf != NULL) 
	{		
		T_ONE_REC	one_rec;
		UINT32		fseq = 0;
		UINT64		totalFrames = 0;
		AVFormatContext		*av_file;
		INT32		tmp_value;

		// 直接读某个文件
		if (read_next) {
			fseq = h->fseq_day + 1;
			if (fseek(pf, fseq*sizeof(one_rec), SEEK_SET) != 0) {
				LOGE("%s(), fseek error, errno=%d", __FUNCTION__, errno);
				fclose(pf);
				return NULL;
			}
		}
		
		for ( ;fread(&one_rec, sizeof(one_rec), 1, pf) EQU 1; fseq++)
		{		
//			LOGI("%s(), one_rec, begtime:%ld, endtime:%ld, flag:%d, rectype:%d, filename:%s, ", 
//				__FUNCTION__, one_rec.st_time, one_rec.end_time, one_rec.flag, one_rec.rec_type, one_rec.file_name);
			
			// 判断是否正常
			if (one_rec.flag != OR_FLAG_OK) {
				continue;
			}

			UINT8	find_rec = FALSE;
			if (QfLeftRange(ltime, one_rec.st_time, one_rec.end_time)) {
				T_ONE_REC	next_one_rec;
				
				find_rec = TRUE;
				do {
					if (fread(&next_one_rec, sizeof(next_one_rec), 1, pf) EQU 1) {
						if (QfLeftRange(ltime, next_one_rec.st_time, next_one_rec.end_time)) {
							memcpy(&one_rec, &next_one_rec, sizeof(next_one_rec));
							break;
						}
					}
					fseek(pf, -1*(LONG)sizeof(one_rec), SEEK_CUR);
				} while (FALSE);
			}
			
			// 找到合适的记录了, 或是找到大于此记录的文件
			if (find_rec
				|| one_rec.st_time > ltime
				|| read_next) {

				av_file = avformat_alloc_context();
				fnRet = avformat_open_input(&av_file , one_rec.file_name, NULL, NULL);
				if (fnRet < 0) {
			       	LOGE("%s(), avformat_open_input(%s)=%#x fail", __FUNCTION__, one_rec.file_name, fnRet);
					get_ffmpeg_error_msg(fnRet);
					avformat_free_context(av_file);
					av_file = NULL;
					continue;
			    }

			    // Retrieve stream information
			    fnRet = avformat_find_stream_info(av_file, NULL); 
			    if (fnRet < 0) {
			       	LOGE("%s(), avformat_find_stream_info(%s)=%#x fail", __FUNCTION__, one_rec.file_name, fnRet);
					get_ffmpeg_error_msg(fnRet);
					avformat_close_input(&av_file);
			        continue;
			    }				
				
				// 不复用句柄
				if (!read_next) {
					h = (T_PLAY_HADLE)malloc(sizeof(T_PLAY_CONTEXT));
					bzero(h, sizeof(T_PLAY_CONTEXT));
					memcpy(&h->stTimeDay, stTimeDay, sizeof(h->stTimeDay));
				}

				// 找ID
				h->vid = INVALID_VALUE;
				h->aid = INVALID_VALUE;
				for (int i = 0; i < av_file->nb_streams; i++) {
			        if (av_file->streams[i]->codecpar->codec_type EQU AVMEDIA_TYPE_VIDEO
			            && h->vid EQU INVALID_VALUE) {
			            h->vid = i;
			        }
			        if (av_file->streams[i]->codecpar->codec_type EQU AVMEDIA_TYPE_AUDIO
			            && h->aid EQU INVALID_VALUE) {
			            h->aid = i;
			        }
			    }

				// 判断是否有效
			    if (h->vid EQU h->aid
			        && h->aid EQU INVALID_VALUE) {
			        LOGE("%s(), 没有有效的音频[aid=%d]或是视频流[vid=%d]", __FUNCTION__, h->aid, h->vid);
					avformat_close_input(&av_file);
			        continue;
			    }
								
				h->fseq_day = fseq;
				h->av_file 	= av_file;
				h->with_tz  = with_tz;
				h->audBsf	= NULL;
				h->vidBsf	= NULL;
				if (with_tz) {
					h->f_st_time= one_rec.st_time + g_pRunSets->time_set.timezone*60;
				}

				// 解析参数
				{
					// 视频
				    if (h->vid != INVALID_VALUE) {
				        AVStream *stream = av_file->streams[h->vid];

				        switch (stream->codecpar->codec_id) {
				            case AV_CODEC_ID_H264:
				                h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;
				                break;

				            case AV_CODEC_ID_H265:
				              	h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_HEVC;
				                break;

				            default:
				                LOGE("%s(), 未知的视频编码格式: %d", __func__, stream->codecpar->codec_id);
				                h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;

				        }

						h->f_fps = stream->avg_frame_rate.num/stream->avg_frame_rate.den;	// hcc use ffmpeg-4.1.3
						h->videoWidth  = stream->codecpar->width;
						h->videoHeight = stream->codecpar->height;
				       	totalFrames = stream->nb_frames;
				    }

				    // 音频
				    if (h->aid != INVALID_VALUE) {
				        AVStream *stream = av_file->streams[h->aid];				       
			
						switch (stream->codecpar->sample_rate) {
						case 8000:
							h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
							break;

						case 16000:
							h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
							break;
						
						case 32000:
							h->a_frame_info.flags = (AUDIO_SAMPLE_32K << 2);
							break;

						case 44100:
							h->a_frame_info.flags = (AUDIO_SAMPLE_44K << 2);
							break;

						default:
							h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
						}
						
				        if (stream->codecpar->format EQU AV_SAMPLE_FMT_U8
				            || stream->codecpar->format EQU AV_SAMPLE_FMT_U8P) {
				            h->a_frame_info.flags |= (AUDIO_DATABITS_8 << 1);
				        }
				        else if (stream->codecpar->format EQU AV_SAMPLE_FMT_S16
				                || stream->codecpar->format EQU AV_SAMPLE_FMT_S16P) {
				            h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
				        }
				        else if (stream->codecpar->format EQU AV_SAMPLE_FMT_S16
				                   || stream->codecpar->format EQU AV_SAMPLE_FMT_S16P) {
				        	h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
				        }

						if (stream->codecpar->channels EQU 2){
							h->a_frame_info.flags |= AUDIO_CHANNEL_STERO;
						}
						else {
							h->a_frame_info.flags |= AUDIO_CHANNEL_MONO;
						}

						switch (stream->codecpar->codec_id) {
			            case AV_CODEC_ID_PCM_S16LE:
			                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_PCM;
							h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2)|(AUDIO_DATABITS_16 << 1)|AUDIO_CHANNEL_MONO;
			                break;				           

			            case AV_CODEC_ID_PCM_ALAW:
			                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711A;
							h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2)|(AUDIO_DATABITS_16 << 1)|AUDIO_CHANNEL_MONO;
			                break;

			            case AV_CODEC_ID_PCM_MULAW:
			                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711U;
							h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2)|(AUDIO_DATABITS_16 << 1)|AUDIO_CHANNEL_MONO;
			                break;

			            case AV_CODEC_ID_OPUS:
			                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_OPUS;
			                break;

			            case AV_CODEC_ID_AAC:
			                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_AAC;
			                break;

			            default:
			                LOGE("%s(), 未知的音频编码格式: %d",  __func__, stream->codecpar->codec_id);
			                h->a_frame_info.codec_id = MEDIA_CODEC_UNKNOWN;
				        }

						LogI("sample_rate:%d, format:%d, channel:%d", 
							stream->codecpar->sample_rate, stream->codecpar->format, stream->codecpar->channels);
						
				    }
				}

#if MP4_READ_BSF
				// 264的数据需要使用过滤器来读取 
				do {
					CONST AVBitStreamFilter 	*filter = NULL;
					AVBSFContext 		*absCtx = NULL;
				
					filter = av_bsf_get_by_name(h->v_frame_info.codec_id EQU MEDIA_CODEC_VIDEO_HEVC ? "hevc_mp4toannexb" : "h264_mp4toannexb");
					if (filter EQU NULL) {
					 	LOGE("%s(), av_bsf_get_by_name() error",  __func__);
						break;
					}
				
					//过滤器分配内存   
					av_bsf_alloc(filter, &absCtx);
#if 0				
					//添加解码器属性   
					avcodec_parameters_copy(absCtx->par_in, av_file->streams[h->vid]->codecpar);
				
					absCtx->time_base_in = av_file->streams[h->vid]->time_base;
#else
					avcodec_parameters_copy(absCtx->par_in, av_file->streams[h->vid]->codecpar);
					fnRet = avcodec_parameters_from_context(absCtx->par_in, av_file->streams[h->vid]->codec);
			        if (fnRet < 0) {
					 	LOGE("%s(), avcodec_parameters_from_context() error",  __func__);
						av_bsf_free(&absCtx);
						break;
					}

			        absCtx->time_base_in = av_file->streams[h->vid]->time_base;;

//			        if (filter->priv_class) {
//			            const AVOption *opt = av_opt_next(absCtx->priv_data, NULL);
//			            const char * shorthand[2] = {NULL};
//
//			            if (opt)
//			                shorthand[0] = opt->name;
//						
//						LOGW("%s(), name=%s",  __func__, opt->name);
//						
//			            fnRet = av_opt_set_from_string(absCtx->priv_data, filter->name, shorthand, "=", ":");
//			            if (fnRet < 0){
//						 	LOGE("%s(), av_opt_set_from_string() error",  __func__);
//							av_bsf_free(&absCtx);
//							break;
//						}
//			        }

#endif				
					//初始化过滤器上下文	
					fnRet = av_bsf_init(absCtx);
					if (fnRet < 0){
						LOGE("%s(), av_bsf_init() error",  __func__);
						av_bsf_free(&absCtx);
						break;
					}

					h->vidBsf = absCtx;
				}while (FALSE);
#endif

				LogI("v-codec:%#x, a-codec:%#x, total:%ldsec, frame:%lld fps:%d, file:%s", 
					h->v_frame_info.codec_id, h->a_frame_info.codec_id, 
					one_rec.end_time-one_rec.st_time,
					totalFrames, h->f_fps, one_rec.file_name);
				
				// 定位
				tmp_value = ltime - one_rec.st_time;
				if (tmp_value > 0) {
					mfile_play_seek(h, tmp_value);
				}					

				break;
			}
//			else {
//				LOGI("%s(), not in range, seektime:%ld", __FUNCTION__, ltime);
//			}
			
		}

		fclose(pf);
	} else {
		LOGE("%s() error, file not exist: %s", __FUNCTION__, path);
	}
		
	if (h && h->av_file) 
		return h;
		
	return NULL;
}

T_PLAY_HADLE mfile_play_open_ex(int snridx, T_PLAY_HADLE h, STimeDay *stTimeDay, UINT8 with_tz)
{
	return mfile_play_open(h, stTimeDay, with_tz);
}

/** 
 * 打开指定文件
 * @param  filename  [文件名]
 * @return           成功返回非NULL;失败返回NULL
 */
T_PLAY_HADLE mfile_play_file(LPCTSTR filename)
{
	INT32	fnRet;
	UINT64	totalFrames = 0;
	AVFormatContext *av_file;
		
	do {

		av_file = avformat_alloc_context();
		fnRet = avformat_open_input(&av_file , filename, NULL, NULL);
		if (fnRet < 0) {
			LOGE("%s(), avformat_open_input(%s)=%#x fail", __FUNCTION__, filename, fnRet);
			get_ffmpeg_error_msg(fnRet);
			avformat_free_context(av_file);
			av_file = NULL;
			break;
		}

		// Retrieve stream information
		fnRet = avformat_find_stream_info(av_file, NULL); 
		if (fnRet < 0) {
			LOGE("%s(), avformat_find_stream_info(%s)=%#x fail", __FUNCTION__, filename, fnRet);
			get_ffmpeg_error_msg(fnRet);
			avformat_close_input(&av_file);
			break;
		}				
		
		// 不复用句柄
		T_PLAY_HADLE h = (T_PLAY_HADLE)calloc(1, sizeof(T_PLAY_CONTEXT));

		// 找ID
		h->vid = INVALID_VALUE;
		h->aid = INVALID_VALUE;
		for (int i = 0; i < av_file->nb_streams; i++) {
			if (av_file->streams[i]->codecpar->codec_type EQU AVMEDIA_TYPE_VIDEO
				&& h->vid EQU INVALID_VALUE) {
				h->vid = i;
			}
			if (av_file->streams[i]->codecpar->codec_type EQU AVMEDIA_TYPE_AUDIO
				&& h->aid EQU INVALID_VALUE) {
				h->aid = i;
			}
		}

		// 判断是否有效
		if (h->vid EQU h->aid
			&& h->aid EQU INVALID_VALUE) {
			LOGE("%s(), 没有有效的音频[aid=%d]或是视频流[vid=%d]", __FUNCTION__, h->aid, h->vid);
			avformat_close_input(&av_file);
			free(h);
			break;
		}
						
		h->av_file	= av_file;
		/*************************
		* 不可用
		* h->fseq_day;
		* h->with_tz;
		* h->f_st_time
		*************************/
		h->audBsf	= NULL;
		h->vidBsf	= NULL;
		
		
		// 解析参数
		{
			// 视频
			if (h->vid != INVALID_VALUE) {
				AVStream *stream = av_file->streams[h->vid];

				switch (stream->codecpar->codec_id) {
					case AV_CODEC_ID_H264:
						h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;
						break;

					case AV_CODEC_ID_H265:
						h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_HEVC;
						break;

					default:
						LOGE("%s(), 未知的视频编码格式: %d", __func__, stream->codecpar->codec_id);
						h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;

				}

				h->f_fps = stream->avg_frame_rate.num/stream->avg_frame_rate.den;	// hcc use ffmpeg-4.1.3
				h->videoWidth  = stream->codecpar->width;
				h->videoHeight = stream->codecpar->height;
				totalFrames = stream->nb_frames;
			}

			// 音频
			if (h->aid != INVALID_VALUE) {
				AVStream *stream = av_file->streams[h->aid];					   
	
				switch (stream->codecpar->sample_rate) {
				case 8000:
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
					break;

				case 16000:
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
					break;
				
				case 32000:
					h->a_frame_info.flags = (AUDIO_SAMPLE_32K << 2);
					break;

				case 44100:
					h->a_frame_info.flags = (AUDIO_SAMPLE_44K << 2);
					break;

				default:
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
				}
				
				if (stream->codecpar->format EQU AV_SAMPLE_FMT_U8
					|| stream->codecpar->format EQU AV_SAMPLE_FMT_U8P) {
					h->a_frame_info.flags |= (AUDIO_DATABITS_8 << 1);
				}
				else if (stream->codecpar->format EQU AV_SAMPLE_FMT_S16
						|| stream->codecpar->format EQU AV_SAMPLE_FMT_S16P) {
					h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
				}
				else if (stream->codecpar->format EQU AV_SAMPLE_FMT_S16
						   || stream->codecpar->format EQU AV_SAMPLE_FMT_S16P) {
					h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
				}

				if (stream->codecpar->channels EQU 2){
					h->a_frame_info.flags |= AUDIO_CHANNEL_STERO;
				}
				else {
					h->a_frame_info.flags |= AUDIO_CHANNEL_MONO;
				}

				switch (stream->codecpar->codec_id) {
				case AV_CODEC_ID_PCM_S16LE:
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_PCM;
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2)|(AUDIO_DATABITS_16 << 1)|AUDIO_CHANNEL_MONO;
					break;						   

				case AV_CODEC_ID_PCM_ALAW:
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711A;
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2)|(AUDIO_DATABITS_16 << 1)|AUDIO_CHANNEL_MONO;
					break;

				case AV_CODEC_ID_PCM_MULAW:
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711U;
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2)|(AUDIO_DATABITS_16 << 1)|AUDIO_CHANNEL_MONO;
					break;

				case AV_CODEC_ID_OPUS:
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_OPUS;
					break;

				case AV_CODEC_ID_AAC:
					h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_AAC;
					break;

				default:
					LOGE("%s(), 未知的音频编码格式: %d",  __func__, stream->codecpar->codec_id);
					h->a_frame_info.codec_id = MEDIA_CODEC_UNKNOWN;
				}

				LogI("sample_rate:%d, format:%d, channel:%d", 
					stream->codecpar->sample_rate, stream->codecpar->format, stream->codecpar->channels);
				
			}
		}

#if MP4_READ_BSF
		// 264的数据需要使用过滤器来读取 
		do {
			CONST AVBitStreamFilter 	*filter = NULL;
			AVBSFContext		*absCtx = NULL;
		
			filter = av_bsf_get_by_name(h->v_frame_info.codec_id EQU MEDIA_CODEC_VIDEO_HEVC ? "hevc_mp4toannexb" : "h264_mp4toannexb");
			if (filter EQU NULL) {
				LOGE("%s(), av_bsf_get_by_name() error",  __func__);
				break;
			}
		
			//过滤器分配内存   
			av_bsf_alloc(filter, &absCtx);
#if 0				
			//添加解码器属性   
			avcodec_parameters_copy(absCtx->par_in, av_file->streams[h->vid]->codecpar);
		
			absCtx->time_base_in = av_file->streams[h->vid]->time_base;
#else
			avcodec_parameters_copy(absCtx->par_in, av_file->streams[h->vid]->codecpar);
			fnRet = avcodec_parameters_from_context(absCtx->par_in, av_file->streams[h->vid]->codec);
			if (fnRet < 0) {
				LOGE("%s(), avcodec_parameters_from_context() error",  __func__);
				av_bsf_free(&absCtx);
				break;
			}

			absCtx->time_base_in = av_file->streams[h->vid]->time_base;;

//					if (filter->priv_class) {
//						const AVOption *opt = av_opt_next(absCtx->priv_data, NULL);
//						const char * shorthand[2] = {NULL};
//
//						if (opt)
//							shorthand[0] = opt->name;
//						
//						LOGW("%s(), name=%s",  __func__, opt->name);
//						
//						fnRet = av_opt_set_from_string(absCtx->priv_data, filter->name, shorthand, "=", ":");
//						if (fnRet < 0){
//							LOGE("%s(), av_opt_set_from_string() error",  __func__);
//							av_bsf_free(&absCtx);
//							break;
//						}
//					}

#endif				
			//初始化过滤器上下文	
			fnRet = av_bsf_init(absCtx);
			if (fnRet < 0){
				LOGE("%s(), av_bsf_init() error",  __func__);
				av_bsf_free(&absCtx);
				break;
			}

			h->vidBsf = absCtx;
		}while (FALSE);
#endif

		LogI("v-codec:%#x, a-codec:%#x, frame:%lld fps:%d, file:%s", 
			h->v_frame_info.codec_id, h->a_frame_info.codec_id, 
			totalFrames, h->f_fps, filename);
				
		if (h && h->av_file) 
			return h;
		
	} while (FALSE);
		
	return NULL;

}


#pragma GCC diagnostic pop

/**
 * 读数据
 * @param  h        上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  buf      缓冲
 * @param  buf_len  缓冲大小
 * @param  auto_next_file  自动读取下一个文件 
 * @return          返回读取到的数据大小；失败返回0
 */
INT32 mfile_play_read_frame(T_PLAY_HADLE h, CHAR *buf, UINT32 buf_len, UINT8 auto_next_file)
{
	INT32	fnRet;
	AVFormatContext *av_file;
	AVBSFContext 	*bsfCtx;
	INT32   copy_size = 0;
    AVStream *st;
	AVPacket packet;
	
	if (h EQU NULL || h->av_file EQU NULL)
		return 0;

	av_file = (AVFormatContext *)h->av_file;
	bsfCtx  = (AVBSFContext *)h->vidBsf;
    while (TRUE) {

        fnRet = av_read_frame(av_file, &packet);
        if (fnRet < 0){		

			if (!auto_next_file) {
				break;
			}
			
			//av_packet_unref(&packet);
			av_init_packet(&packet);
            mfile_play_close(h, FALSE);			
			if (mfile_play_open(h, NULL, h->with_tz) EQU NULL) {
				return 0;
			}
			
			av_file = (AVFormatContext *)h->av_file;
			bsfCtx  = (AVBSFContext *)h->vidBsf;
			continue;
        }		
       
        st = av_file->streams[packet.stream_index];
        if (packet.stream_index EQU h->vid) {

#if MP4_READ_BSF			
			if (bsfCtx != NULL) {
				fnRet = av_bsf_send_packet(bsfCtx, &packet);
				if (fnRet < 0) {
					LOGI("%s() av_bsf_send_packet()=%d, size:%d, type:%d", __func__, fnRet, 
						packet.size, packet.flags);
					av_packet_unref(&packet);
					continue;
				}
				fnRet = av_bsf_receive_packet(bsfCtx, &packet);
				if (fnRet < 0) {
					LOGI("%s() av_bsf_receive_packet()=%d, size:%d, type:%d", __func__, fnRet, 
						packet.size, packet.flags);
					av_packet_unref(&packet);
					continue;
				}
			}
#endif		
			memcpy(buf, packet.data, packet.size);
			h->frame_size = copy_size = packet.size;

//			LOGI("%s() size:%d, extradata_size:%d, type:%d", 
//				__func__, copy_size, st->codecpar->extradata_size, packet.flags);

			h->is_video = TRUE;
			h->v_frame_info.flags = (packet.flags & AV_PKT_FLAG_KEY) ? IPC_FRAME_FLAG_IFRAME:IPC_FRAME_FLAG_PBFRAME;
            h->v_frame_info.timestamp = packet.dts * 1000 * av_q2d(st->time_base);
            h->v_frame_info.utctime  = h->f_st_time + h->v_frame_info.timestamp / 1000;			
        }
        else if (packet.stream_index EQU h->aid) {

			h->frame_size = copy_size = packet.size;
	        memcpy(buf, packet.data, copy_size);

			h->is_video = FALSE;
			h->a_frame_info.timestamp = packet.dts * 1000 * av_q2d(st->time_base);
			h->a_frame_info.utctime   = h->f_st_time + h->a_frame_info.timestamp / 1000;
        }
		else {
			av_packet_unref(&packet);
			continue;
		}		
        av_packet_unref(&packet);

//		if (h->is_video && h->v_frame_info.flags EQU IPC_FRAME_FLAG_IFRAME) {
//			LOGI("%s() size:%d, buf_len:%d, isvido:%d,  type:%d", __func__, copy_size, buf_len, h->is_video, h->v_frame_info.flags);
//		}

        return copy_size;
    }

	av_packet_unref(&packet);

    return 0;	
}


/**
 * 定位文件
 * @param  h        	上下文句柄；当前文件读完好,自动读取下一个文件
 * @param  offset_sec   偏移多少秒(相当文件开始)
 * @return          	成功返回TRUE；失败返回FALSE
 */
UINT8 mfile_play_seek(T_PLAY_HADLE h, UINT32 offset_sec)
{
	INT64 seekTime_tmp = offset_sec;
	AVFormatContext *av_file;

	if (h EQU NULL || h->av_file EQU NULL || h->vid EQU INVALID_VALUE)
		return FALSE;

	av_file = (AVFormatContext *)h->av_file;	
	seekTime_tmp = seekTime_tmp * av_file->streams[h->vid]->avg_frame_rate.num / av_file->streams[h->vid]->avg_frame_rate.den;  
    if (av_file->start_time != AV_NOPTS_VALUE)  
        seekTime_tmp += av_file->start_time;  
	
    if (av_seek_frame(av_file, h->vid, seekTime_tmp, AVSEEK_FLAG_ANY/*AVSEEK_FLAG_BACKWARD*/) < 0)  
    {  
        LOGE("av_seek_frame() seek to %lld failed!", 
			seekTime_tmp/(av_file->streams[h->vid]->avg_frame_rate.num / av_file->streams[h->vid]->avg_frame_rate.den));  
        return FALSE;  
    }  

	return TRUE;
}



/**
 * 关闭句柄并释放资源
 * @param  h 		上下文句柄
 * @param  free_p 	释放资源标记
 */
VOID mfile_play_close(T_PLAY_HADLE h, UINT8 free_p)
{
	if (h EQU NULL)
		return;

	LOGI("%s(h=%p, free_p=%d) ", __func__, h, free_p);
	
	if (h->av_file != NULL) {
		AVFormatContext *av_file = (AVFormatContext *)h->av_file;
		AVBSFContext	*vidBsf  = (AVBSFContext *)h->vidBsf;	
		AVBSFContext	*audBsf  = (AVBSFContext *)h->audBsf;

		h->av_file = NULL;	
		h->vidBsf  = NULL;
		h->audBsf  = NULL;
		
		av_bsf_free(&vidBsf);
		av_bsf_free(&audBsf);
		avformat_close_input(&av_file);
		avformat_free_context(av_file);
	}	

	if (free_p) 
		free(h);
}

#if 0
#include <sys/stat.h>
#include <fcntl.h>
INT32	g_out_file = 0;


T_PLAY_HADLE mfile_play_open_file(T_PLAY_HADLE h, LPCSTR file_name)
{
	INT32	fnRet;
	T_ONE_REC	one_rec;
	AVFormatContext		*av_file;
	UINT64		totalFrames = 0;
	
	QfSet0(&one_rec, sizeof(one_rec));
	strcpy(one_rec.file_name, file_name);

	do {

		av_file = avformat_alloc_context();
		fnRet = avformat_open_input(&av_file , one_rec.file_name, NULL, NULL);
		if (fnRet < 0) {
	       	LOGE("%s(), avformat_open_input(%s)=%#x fail", __FUNCTION__, one_rec.file_name, fnRet);
			get_ffmpeg_error_msg(fnRet);
			avformat_free_context(av_file);
			av_file = NULL;
			continue;
	    }

	    // Retrieve stream information
	    fnRet = avformat_find_stream_info(av_file, NULL); 
	    if (fnRet < 0) {
	       	LOGE("%s(), avformat_find_stream_info(%s)=%#x fail", __FUNCTION__, one_rec.file_name, fnRet);
			get_ffmpeg_error_msg(fnRet);
			avformat_close_input(&av_file);
	        continue;
	    }				
		
		// 不复用句柄		
		h = (T_PLAY_HADLE)malloc(sizeof(T_PLAY_CONTEXT));
		bzero(h, sizeof(T_PLAY_CONTEXT));
		
		// 找ID
		h->vid = INVALID_VALUE;
		h->aid = INVALID_VALUE;
		for (int i = 0; i < av_file->nb_streams; i++) {
	        if (av_file->streams[i]->codecpar->codec_type EQU AVMEDIA_TYPE_VIDEO
	            && h->vid EQU INVALID_VALUE) {
	            h->vid = i;
	        }
	        if (av_file->streams[i]->codecpar->codec_type EQU AVMEDIA_TYPE_AUDIO
	            && h->aid EQU INVALID_VALUE) {
	            h->aid = i;
	        }
	    }

		// 判断是否有效
	    if (h->vid EQU h->aid
	        && h->aid EQU INVALID_VALUE) {
	        LOGE("%s(), 没有有效的音频[aid=%d]或是视频流[vid=%d]", __FUNCTION__, h->aid, h->vid);
			avformat_close_input(&av_file);
	        continue;
	    }
						
		h->av_file 	= av_file;
		h->audBsf	= NULL;
		h->vidBsf	= NULL;
		

		// 解析参数
		{
			// 视频
		    if (h->vid != INVALID_VALUE) {
		        AVStream *stream = av_file->streams[h->vid];

		        switch (stream->codecpar->codec_id) {
		            case AV_CODEC_ID_H264:
		                h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;
		                break;

		            case AV_CODEC_ID_H265:
		              	h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_HEVC;
		                break;

		            default:
		                LOGE("%s(), 未知的视频编码格式: %d", __func__, stream->codecpar->codec_id);
		                h->v_frame_info.codec_id = MEDIA_CODEC_VIDEO_H264;

		        }

				h->f_fps = stream->avg_frame_rate.num/stream->avg_frame_rate.den;	// hcc use ffmpeg-4.1.3
		       	totalFrames = stream->nb_frames;
		    }

		    // 音频
		    if (h->aid != INVALID_VALUE) {
		        AVStream *stream = av_file->streams[h->aid];

		        switch (stream->codecpar->codec_id) {
		            case AV_CODEC_ID_PCM_S16LE:
		                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_PCM;
		                break;				           

		            case AV_CODEC_ID_PCM_ALAW:
		                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711A;						
		                break;

		            case AV_CODEC_ID_PCM_MULAW:
		                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_G711U;
		                break;

		            case AV_CODEC_ID_OPUS:
		                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_OPUS;
		                break;

		            case AV_CODEC_ID_AAC:
		                h->a_frame_info.codec_id = MEDIA_CODEC_AUDIO_AAC;
		                break;

		            default:
		                LOGE("%s(), 未知的音频编码格式: %d",  __func__, stream->codecpar->codec_id);
		                h->a_frame_info.codec_id = MEDIA_CODEC_UNKNOWN;

		        }
	
				switch (stream->codecpar->sample_rate) {
				case 8000:
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
					break;

				case 16000:
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
					break;
				
				case 32000:
					h->a_frame_info.flags = (AUDIO_SAMPLE_32K << 2);
					break;

				case 44100:
					h->a_frame_info.flags = (AUDIO_SAMPLE_44K << 2);
					break;

				default:
					h->a_frame_info.flags = (AUDIO_SAMPLE_8K << 2);
				}

				
		        if (stream->codecpar->format EQU AV_SAMPLE_FMT_U8
		            || stream->codecpar->format EQU AV_SAMPLE_FMT_U8P) {
		            h->a_frame_info.flags |= (AUDIO_DATABITS_8 << 1);
		        }
		        else if (stream->codecpar->format EQU AV_SAMPLE_FMT_S16
		                || stream->codecpar->format EQU AV_SAMPLE_FMT_S16P) {
		            h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
		        }
		        else if (stream->codecpar->format EQU AV_SAMPLE_FMT_S16
		                   || stream->codecpar->format EQU AV_SAMPLE_FMT_S16P) {
		        	h->a_frame_info.flags |= (AUDIO_DATABITS_16 << 1);
		        }

				if (stream->codecpar->channels EQU 2){
					h->a_frame_info.flags |= AUDIO_CHANNEL_STERO;
				}
				else {
					h->a_frame_info.flags |= AUDIO_CHANNEL_MONO;
				}
		    }
		}
		
#if MP4_READ_BSF
		// 264的数据需要使用过滤器来读取 
		do {
			CONST AVBitStreamFilter 	*filter = NULL;
			AVBSFContext		*absCtx = NULL;
		
			filter = av_bsf_get_by_name(h->v_frame_info.codec_id EQU MEDIA_CODEC_VIDEO_HEVC ? "hevc_mp4toannexb" : "h264_mp4toannexb");
			if (filter EQU NULL) {
				LOGE("%s(), av_bsf_get_by_name() error",  __func__);
				break;
			}
		
			//过滤器分配内存   
			av_bsf_alloc(filter, &absCtx);
#if 0				
			//添加解码器属性   
			avcodec_parameters_copy(absCtx->par_in, av_file->streams[h->vid]->codecpar);
		
			absCtx->time_base_in = av_file->streams[h->vid]->time_base;
#else
			avcodec_parameters_copy(absCtx->par_in, av_file->streams[h->vid]->codecpar);
			fnRet = avcodec_parameters_from_context(absCtx->par_in, av_file->streams[h->vid]->codec);
			if (fnRet < 0) {
				LOGE("%s(), avcodec_parameters_from_context() error",  __func__);
				av_bsf_free(&absCtx);
				break;
			}

			absCtx->time_base_in = av_file->streams[h->vid]->time_base;;

//					if (filter->priv_class) {
//						const AVOption *opt = av_opt_next(absCtx->priv_data, NULL);
//						const char * shorthand[2] = {NULL};
//
//						if (opt)
//							shorthand[0] = opt->name;
//						
//						LOGW("%s(), name=%s",  __func__, opt->name);
//						
//						fnRet = av_opt_set_from_string(absCtx->priv_data, filter->name, shorthand, "=", ":");
//						if (fnRet < 0){
//							LOGE("%s(), av_opt_set_from_string() error",  __func__);
//							av_bsf_free(&absCtx);
//							break;
//						}
//					}

#endif				
			//初始化过滤器上下文	
			fnRet = av_bsf_init(absCtx);
			if (fnRet < 0){
				LOGE("%s(), av_bsf_init() error",  __func__);
				av_bsf_free(&absCtx);
				break;
			}

			h->vidBsf = absCtx;
		}while (FALSE);
#endif

		
		LOGI("%s(), total:%ldsec, frame:%lld fps:%d, file:%s", 
			__FUNCTION__, one_rec.end_time-one_rec.st_time,
			totalFrames, h->f_fps, one_rec.file_name);

		break;
	}
	while (FALSE);

		
	return h;
}


int main1(int argc, char* argv[])
{
#define MAX_BUFF_SZ	1024*500
	T_PLAY_HADLE av_file = mfile_play_open_file(NULL, argv[1]);
	CHAR 	*buf = (CHAR*)malloc(MAX_BUFF_SZ);
	INT32	fnRet;

	QF_ASSERT(av_file != NULL);
	
	g_out_file = open(argv[2], O_WRONLY|O_CREAT, 0666);
	QF_ASSERT(g_out_file != -1);

	do {
		fnRet = mfile_play_read_frame(av_file, buf, MAX_BUFF_SZ, FALSE);
		if (fnRet <= 0)
			break;

		if (av_file->is_video)
			write(g_out_file, buf, fnRet);
	} while (1);

	mfile_play_close(av_file, TRUE);

	if (g_out_file != 0) {
		close(g_out_file);
		g_out_file = 0;
	}
	
	return 0;
}
#endif
