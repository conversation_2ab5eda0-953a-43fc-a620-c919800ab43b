#pragma once

#include <string>
#include <vector>
#include <map>
#include <queue>
#include <memory>
#include <pthread.h>
#include "low_delay_common.h"
#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "vs_media_buffer.h"
#include "buffer_manager.h"
#include "Thread/Lock.hpp"
#include "Thread/RWLock.hpp"
#include "Thread/Cond.hpp"
#include <pthread.h>
#include <thread>
#include <atomic>


// 常量定义
#define LIVE_MAX_CHANNEL_NUM        64              // 最大支持64个通道(0-63)
#define LIVE_DEFAULT_BUFFER_SIZE    (1024*1024)     // 默认1MB缓冲区
#define LIVE_MAX_BUFFER_SIZE        (10*1024*1024)  // 最大10MB缓冲区
#define LIVE_DEFAULT_QUEUE_SIZE     100             // 默认队列大小

// 自动流发送相关常量
#define LIVE_SENDER_THREAD_SLEEP_MS     10          // 工作线程休眠时间(毫秒)
#define LIVE_QUEUE_WAIT_TIMEOUT_MS      100         // 队列等待超时时间(毫秒)
#define LIVE_BUFFER_DRAIN_TIMEOUT_MS    5000        // 缓冲区排空超时时间(毫秒)
#define LIVE_THREAD_AUTO_PAUSE_TIMEOUT_MS 60000     // 自动暂停超时时间(毫秒) - 60秒
#define LIVE_STREAM_SLEEP_TIMEOUT_MS    30000       // 流休眠超时时间(毫秒) - 30秒
#define LIVE_MIN_BUFFER_LEVEL           5           // 最小缓冲区级别
#define LIVE_MAX_BUFFER_LEVEL           50          // 最大缓冲区级别
#define LIVE_SMOOTH_BUFFER_THRESHOLD    10          // 平滑播放缓冲阈值

// 线程状态枚举
typedef enum {
    THREAD_STATE_STOPPED = 0,      // 停止状态
    THREAD_STATE_RUNNING,          // 运行状态
    THREAD_STATE_PAUSED,           // 暂停状态
    THREAD_STATE_STOPPING          // 正在停止状态
} THREAD_STATE;

// 实时流数据包结构 - 使用shared_ptr智能指针管理内存
typedef struct _T_LIVE_STREAM_PACKET {
    UINT32 dataSize;                        // 数据大小
    UINT64 timestamp;                       // 时间戳
    UINT8  frameType;                       // 帧类型 (I帧/P帧等)
    UINT8  keyFrame;                        // 是否关键帧
    UINT32 sequence;                        // 序列号
    std::shared_ptr<CHAR[]> data;           // shared_ptr智能指针管理的数据

    // 默认构造函数
    _T_LIVE_STREAM_PACKET()
        : dataSize(0), timestamp(0), frameType(0), keyFrame(0), sequence(0), data(nullptr) {}

} T_LIVE_STREAM_PACKET;

// 流状态枚举
typedef enum {
    LIVE_STREAM_STOPPED = 0,       // 停止状态
    LIVE_STREAM_RUNNING,           // 运行状态
    LIVE_STREAM_PAUSED,            // 暂停状态
    LIVE_STREAM_SLEEPING,          // 休眠状态
    LIVE_STREAM_BUFFERING,         // 缓冲状态
    LIVE_STREAM_ERROR              // 错误状态
} LIVE_STREAM_STATE;



// 前向声明
class LiveStreamManager;

// 简化的流发送工作线程类
class StreamSenderThread {
public:
    StreamSenderThread(INT32 channelId, LiveStreamManager* manager);
    ~StreamSenderThread();

    // 线程控制
    INT32 start();
    INT32 stop();
    void requestStop();  // 快速停止请求，不等待线程结束

    // 线程状态查询
    THREAD_STATE getState() const;
    bool isRunning() const;
    bool isPaused() const;

    // VO通道管理
    void setVoChannel(INT32 vo_chn);
    INT32 getVoChannel() const;

    // 暂停控制
    void resumeFromPause();

private:
    INT32 m_channelId;                          // 通道ID
    LiveStreamManager* m_manager;               // 管理器指针

    // 线程相关
    pthread_t m_thread;                         // 线程句柄
    std::atomic<bool> m_shouldStop;             // 停止标志
    std::atomic<THREAD_STATE> m_state;          // 线程状态

    // 配置
    std::atomic<INT32> m_voChn;                 // VO通道号

    // 同步对象
    mutable TCSLock m_configLock;               // 配置锁
    TCSLock m_pauseLock;                        // 暂停锁
    TCondition m_pauseCondition;                // 暂停条件变量

    // 内部方法
    static void* threadEntry(void* arg);
    void threadLoop();
    INT32 sendFrame(const T_LIVE_STREAM_PACKET& packet);
    void drainBufferOnStop();
    bool waitForData(UINT32 timeoutMs);
    void handleAutoPause();
};

// 前向声明
class StreamSenderThread;

// 增强的单个通道实时流管理器
class LiveChannelStream {
public:
    LiveChannelStream(INT32 channelId);
    ~LiveChannelStream();

    // 设置发送线程引用
    void setSenderThread(StreamSenderThread* senderThread);

    // 初始化和清理
    INT32 initialize();
    void cleanup();

    // 休眠和恢复
    void enterSleepState();
    void exitSleepState();
    bool isSleeping() const;

    // 快速状态检查（无锁）
    LIVE_STREAM_STATE getStateFast() const;

    // 数据存储接口
    INT32 pushStreamData(FRAMEINFO_t* hdr, void* data, uint32_t size);

    // 数据取出接口 - 支持阻塞和非阻塞模式，使用引用传递
    INT32 popStreamData(T_LIVE_STREAM_PACKET& packet);
    INT32 popStreamDataBlocking(T_LIVE_STREAM_PACKET& packet, UINT32 timeoutMs);

    // 状态查询
    LIVE_STREAM_STATE getState() const;
    UINT32 getQueueSize() ;
    bool isEmpty() ;
    bool isFull() ;

    // 配置参数
    void setMaxQueueSize(UINT32 maxSize);
    void setBufferSize(UINT32 bufferSize);

    // 缓冲控制
    void notifyDataAvailable();
    void notifyThreadResume();

private:
    INT32 m_channelId;                          // 通道ID
    std::atomic<LIVE_STREAM_STATE> m_state;     // 流状态
    bool m_running;                             // 运行标志

    // 数据队列
    std::queue<T_LIVE_STREAM_PACKET> m_dataQueue;
    UINT32 m_maxQueueSize;                      // 最大队列大小
    UINT32 m_bufferSize;                        // 缓冲区大小
    UINT32 m_sequence;                          // 序列号

    // 线程引用
    StreamSenderThread* m_senderThread;        // 发送线程引用

    // 线程同步
    mutable TRWLock m_rwLock;                   // 读写锁
    TCSLock m_queueLock;                        // 队列锁
    TCondition m_dataAvailable;                 // 数据可用条件变量

    // 内部方法
    INT32 createPacket(T_LIVE_STREAM_PACKET& packet, FRAMEINFO_t* hdr, void* data, uint32_t size);
    bool isQueueFull() const;
    void dropOldestPacket();

    // 内存安全分配方法 - 使用shared_ptr
    std::shared_ptr<CHAR[]> safeAllocateMemory(uint32_t size);
};

// 实时流管理器主类
class LiveStreamManager {
public:
    LiveStreamManager(INT32 channelId);
    ~LiveStreamManager();
    
    // 初始化和清理
    INT32 initialize();
    void cleanup();
    
    // 流管理接口 - 精简版本
    INT32 startStream();
    INT32 stopStream();
    INT32 sleepStream();
    INT32 wakeupStream();
    bool isStreamSleeping() const;

    // 数据存储接口 - 接收外部实时流数据
    INT32 pushStreamData(FRAMEINFO_t* hdr, void* data, uint32_t size);

    // 数据取出接口 - 提供给播放模块，使用引用传递
    INT32 popStreamData(T_LIVE_STREAM_PACKET& packet);

    // 配置管理
    INT32 setStreamConfig(UINT32 maxQueueSize, UINT32 bufferSize);

    // 获取通道ID
    INT32 getChannelId() const { return m_channelId; }

    // === 自动流发送功能 ===

    // 启用/禁用自动发送
    INT32 enableAutoSend(INT32 vo_chn);
    INT32 disableAutoSend();

    // 线程状态查询
    THREAD_STATE getSenderThreadState() const;

    // VO通道动态切换
    INT32 switchVoChannel(INT32 vo_chn);
    INT32 getVoChannel() const;

    // 状态查询
    LIVE_STREAM_STATE getStreamState() const;
    UINT32 getStreamQueueSize() const;
    
private:
    INT32 m_channelId;                          // 通道ID
    INT32 m_voChn;                              // VO通道号
    bool m_initialized;                         // 初始化标志

    // 流管理
    LiveChannelStream* m_stream;                // 单个流实例

    // 自动发送线程管理
    StreamSenderThread* m_senderThread;         // 发送线程实例

    // 线程同步
    mutable TRWLock m_rwLock;                   // 读写锁

    // 内部方法
    bool isValidChannelId(INT32 channelId) const;
};

// 全局接口函数 - 与现有代码风格保持一致

/**
 * 初始化实时流管理器
 * @param channelId 通道ID
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_init(INT32 channelId);

/**
 * 清理实时流管理器
 * @param channelId 通道ID
 */
VOID live_stream_cleanup(INT32 channelId);

/**
 * 启动指定通道的实时流
 * @param channelId 通道ID (0-63)
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_start(INT32 channelId);

/**
 * 停止指定通道的实时流
 * @param channelId 通道ID (0-63)
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_stop(INT32 channelId);

/**
 * 推送实时流数据
 * @param channelId 通道ID (0-63)
 * @param hdr 帧头信息
 * @param data 数据指针
 * @param size 数据大小
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_push_data(INT32 channelId, FRAMEINFO_t* hdr, LPVOID data, UINT32 size);

/**
 * 取出实时流数据
 * @param channelId 通道ID (0-63)
 * @param packet 输出数据包引用
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_pop_data(INT32 channelId, T_LIVE_STREAM_PACKET& packet);

// ========== 自动流发送接口 ==========

/**
 * 启用自动流发送功能（一键启动）
 * @param channelId 通道ID (0-63)
 * @param vo_chn 视频输出通道号
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_enable_auto_send(INT32 channelId, INT32 vo_chn);

/**
 * 禁用自动流发送功能
 * @param channelId 通道ID (0-63)
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_disable_auto_send(INT32 channelId);

/**
 * 动态切换VO通道
 * @param channelId 通道ID (0-63)
 * @param vo_chn 新的视频输出通道号
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_switch_vo_channel(INT32 channelId, INT32 vo_chn);

/**
 * 获取当前VO通道号
 * @param channelId 通道ID (0-63)
 * @return VO通道号，失败返回-1
 */
INT32 live_stream_get_vo_channel(INT32 channelId);

// ========== 流休眠管理接口 ==========

/**
 * 强制停止流（完全清理资源）
 * @param channelId 通道ID (0-63)
 * @return 成功返回OK，失败返回FAIL
 */
INT32 live_stream_force_stop(INT32 channelId);

/**
 * 检查流是否处于休眠状态
 * @param channelId 通道ID (0-63)
 * @return true表示休眠，false表示非休眠
 */
bool live_stream_is_sleeping(INT32 channelId);


